<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Privacy Policy - BookMe</title>
  <meta name="description" content="BookMe Privacy Policy - Learn how we collect, use, and protect your personal information when using our booking platform.">
  <meta name="keywords" content="BookMe, privacy policy, data protection, personal information, booking app">
  
  <!-- icofont-css-link -->
  <link rel="stylesheet" href="css/icofont.min.css">
  <!-- Owl-Carosal-Style-link -->
  <link rel="stylesheet" href="css/owl.carousel.min.css">
  <!-- Bootstrap-Style-link -->
  <link rel="stylesheet" href="css/bootstrap.min.css">
  <!-- Aos-Style-link -->
  <link rel="stylesheet" href="css/aos.css">
  <!-- Coustome-Style-link -->
  <link rel="stylesheet" href="css/style.css">
  <!-- Responsive-Style-link -->
  <link rel="stylesheet" href="css/responsive.css">
  <!-- Favicon -->
<link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">
<link rel="apple-touch-icon" href="images/apple-touch-icon.png" />
<link rel="apple-touch-icon" sizes="57x57" href="images/apple-touch-icon-57x57.png" />
<link rel="apple-touch-icon" sizes="72x72" href="images/apple-touch-icon-72x72.png" />
<link rel="apple-touch-icon" sizes="76x76" href="images/apple-touch-icon-76x76.png" />
<link rel="apple-touch-icon" sizes="114x114" href="images/apple-touch-icon-114x114.png" />
<link rel="apple-touch-icon" sizes="120x120" href="images/apple-touch-icon-120x120.png" />
<link rel="apple-touch-icon" sizes="144x144" href="images/apple-touch-icon-144x144.png" />
<link rel="apple-touch-icon" sizes="152x152" href="images/apple-touch-icon-152x152.png" />
<link rel="apple-touch-icon" sizes="180x180" href="images/apple-touch-icon-180x180.png" />
<link rel="apple-touch-icon" sizes="60x60" href="images/apple-touch-icon-60x60.png">
<link rel="icon" href="images/favicon.ico">

<!-- Custom CSS for Privacy Policy Page -->
<style>
/* Privacy Policy Page Styling */
.privacy-policy-page {
  background: #ffffff;
  min-height: 100vh;
  padding: 120px 0 80px 0;
  color: #333333;
}

.privacy-policy-page .section_title h1 {
  color: #008f96;
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-align: center;
}

.privacy-effective-date {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 15px 25px;
  margin: 20px 0 40px 0;
  text-align: center;
}

.privacy-effective-date p {
  margin: 0;
  font-size: 1.1rem;
  color: #00d4aa;
}

.privacy-content {
  max-width: 900px;
  margin: 0 auto 30px auto;
  background: #CCE9EA;
  border-radius: 15px;
  padding: 40px;
  color: #000000;
  box-shadow: 0 8px 25px rgba(0, 143, 150, 0.15);
  border: none;
}

.privacy-intro {
  background: rgba(255, 255, 255, 0.6);
  border-radius: 10px;
  padding: 25px;
  margin-bottom: 30px;
  border-left: 4px solid #00d4aa;
}

.privacy-intro p {
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.7;
}

.privacy-section {
  margin-bottom: 35px;
  padding-bottom: 25px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.privacy-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.privacy-section h3 {
  color: #00d4aa;
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #00d4aa;
  display: inline-block;
}

.privacy-section h4 {
  color: #000000;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 20px 0 10px 0;
}

.privacy-section p {
  color: #000000;
  line-height: 1.7;
  margin-bottom: 15px;
  font-size: 1rem;
}

.privacy-section ul {
  margin: 15px 0;
  padding-left: 0;
}

.privacy-section li {
  color: #000000;
  line-height: 1.6;
  margin-bottom: 8px;
  padding-left: 25px;
  position: relative;
  list-style: none;
}

.privacy-section li::before {
  content: '•';
  color: #00d4aa;
  font-size: 1.2rem;
  font-weight: bold;
  position: absolute;
  left: 0;
  top: 0;
}

.contact-info-privacy {
  background: rgba(0, 212, 170, 0.1);
  border-radius: 10px;
  padding: 20px;
  margin-top: 15px;
  text-align: center;
  border: 2px solid rgba(0, 212, 170, 0.3);
}

.contact-info-privacy a {
  color: #00d4aa;
  text-decoration: none;
  font-weight: 600;
}

.contact-info-privacy a:hover {
  color: #008f96;
  text-decoration: underline;
}

.back-to-home {
  text-align: center;
  margin-top: 40px;
}

.back-to-home a {
  display: inline-block;
  background: #00d4aa;
  color: #ffffff;
  padding: 12px 30px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.back-to-home a:hover {
  background: #ffffff;
  color: #008f96;
  text-decoration: none;
}

/* Desktop Navigation - Hide hamburger menu on desktop */
@media (min-width: 769px) {
  .navbar-toggler {
    display: none !important;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .privacy-policy-page {
    padding: 100px 0 60px 0;
  }
  
  .privacy-content {
    padding: 25px 20px;
    margin: 0 15px 20px 15px;
  }
  
  .privacy-policy-page .section_title h1 {
    font-size: 2.2rem;
  }
  
  .privacy-section h3 {
    font-size: 1.2rem;
  }
  
  .privacy-section h4 {
    font-size: 1rem;
  }
}
</style>

</head>
<body>
  <!-- Preloader -->
  <div id="preloader">
    <div id="loader"></div>
  </div>
  
  <!-- Header Start -->
  <header class="site-header">
    <!-- container start -->
    <div class="container px-0">
      <!-- navigation bar -->
      <nav class="navbar navbar-expand-lg">
        <a class="navbar-brand" href="index.html">
          <img src="images/logo-white.png" class="logo-white" alt="Logo">
          <img src="images/logo.png" class="logo-prime" alt="Logo">
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
          aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon">
            <span class="toggle-wrap">
              <span class="toggle-bar"></span>
            </span>
          </span>
        </button>

        <div class="collapse navbar-collapse" id="navbarSupportedContent">
          <ul class="navbar-nav mx-auto">
            <!-- secondery menu start -->
            <li class="nav-item"><a class="nav-link" href="index.html#home">Home</a> </li>
            <!-- secondery menu end -->
            
            <li class="nav-item">
              <a class="nav-link" href="index.html#about">About us</a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="index.html#features">Features</a>
            </li>

            <li class="nav-item">
              <a class="nav-link active" href="privacy-policy.html">Privacy Policy</a>
            </li>

            <li class="nav-item">
              <a class="nav-link" href="index.html#contact">Contact Us</a>
            </li>

            <li class="nav-item d-block d-lg-none">
              <div class="btn_block">
                <a class="dark_btn" href="index.html#register">Registration</a>
              </div>
            </li>
          </ul>
          <div class="btn_block d-none d-lg-block">
            <a class="dark_btn" href="index.html#register">Registration</a>
          </div>
        </div>
      </nav>
      <!-- navigation bar -->
    </div>
    <!-- container end -->
  </header>
  <!-- Header End -->

  <!-- Privacy Policy Page Content -->
  <section class="privacy-policy-page">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <div class="section_title" data-aos="fade-up" data-aos-duration="1500" data-aos-delay="100">
            <h1>Privacy Policy</h1>
            <div class="privacy-effective-date">
              <p><strong>Effective Date: December 01, 2024</strong></p>
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-12">
          <div class="privacy-content" data-aos="fade-up" data-aos-duration="1500" data-aos-delay="200">

            <div class="privacy-intro">
              <p>At <strong>BookMe</strong>, your privacy is important to us. This Privacy Policy outlines how we collect, use, disclose, and protect your personal information when you use the <strong>BookMe</strong> mobile application (the "App"). By using the App, you agree to the terms of this Privacy Policy.</p>
            </div>

            <div class="privacy-section">
              <h3>1. Information We Collect</h3>
              <p>We collect the following types of information to provide and improve our services:</p>

              <h4>1.1 Personal Information:</h4>
              <ul>
                <li>Name, email address, phone number, and other contact details.</li>
                <li>Payment information, including credit card details (processed securely by third-party payment processors).</li>
                <li>Profile information such as your preferences and settings.</li>
              </ul>

              <h4>1.2 Usage Information:</h4>
              <ul>
                <li>Details of your interactions with the App, including bookings, cancellations, and reschedules.</li>
                <li>Device information, such as your IP address, operating system, and browser type.</li>
                <li>Usage patterns and preferences related to the App.</li>
              </ul>

              <h4>1.3 Location Information:</h4>
              <ul>
                <li>With your consent, we may collect location data to recommend nearby service providers.</li>
              </ul>

              <h4>1.4 Cookies and Similar Technologies:</h4>
              <ul>
                <li>We use cookies and similar technologies to enhance your experience and analyze usage trends.</li>
              </ul>
            </div>

            <div class="privacy-section">
              <h3>2. How We Use Your Information</h3>
              <p>We use the information we collect for the following purposes:</p>
              <ul>
                <li>To facilitate appointment scheduling and payment processing.</li>
                <li>To connect you with service providers and share relevant details of your booking.</li>
                <li>To personalize your experience by tailoring recommendations and features.</li>
                <li>To send notifications about appointments, updates, promotions, and offers.</li>
                <li>To improve the functionality and security of the App.</li>
              </ul>
            </div>

            <div class="privacy-section">
              <h3>3. How We Share Your Information</h3>
              <p>We may share your information in the following circumstances:</p>
              <ul>
                <li><strong>With Service Providers:</strong> To ensure bookings are processed and appointments are fulfilled.</li>
                <li><strong>With Third-Party Payment Processors:</strong> To securely handle payments.</li>
                <li><strong>For Legal Obligations:</strong> When required to comply with laws, regulations, or legal processes.</li>
                <li><strong>With Your Consent:</strong> In situations where you have explicitly granted permission.</li>
              </ul>
              <p>We do not sell or rent your personal information to third parties.</p>
            </div>

            <div class="privacy-section">
              <h3>4. Data Retention</h3>
              <p>We retain your information only as long as necessary to provide our services, comply with legal obligations, resolve disputes, and enforce our agreements.</p>
            </div>

            <div class="privacy-section">
              <h3>5. Data Security</h3>
              <p>We implement industry-standard measures to protect your personal information from unauthorized access, alteration, disclosure, or destruction. However, no system is completely secure, and we cannot guarantee absolute security.</p>
            </div>

            <div class="privacy-section">
              <h3>6. Your Rights</h3>
              <p>Depending on your location, you may have the following rights:</p>
              <ul>
                <li>Access, update, or delete your personal information.</li>
                <li>Opt-out of receiving promotional communications.</li>
                <li>Restrict or object to certain data processing activities.</li>
                <li>Request a copy of your personal data in a portable format.</li>
              </ul>
              <p>To exercise these rights, contact us at the information provided below.</p>
            </div>

            <div class="privacy-section">
              <h3>7. Children's Privacy</h3>
              <p>The App is not intended for individuals under 18 years of age. We do not knowingly collect personal information from children.</p>
            </div>

            <div class="privacy-section">
              <h3>8. Third-Party Links</h3>
              <p>The App may contain links to third-party websites or services. We are not responsible for the privacy practices of these third parties.</p>
            </div>

            <div class="privacy-section">
              <h3>9. Changes to This Privacy Policy</h3>
              <p>We may update this Privacy Policy from time to time. Any significant changes will be communicated to you, and your continued use of the App indicates your acceptance of the updated Privacy Policy.</p>
            </div>

            <div class="privacy-section">
              <h3>10. Contact Us</h3>
              <p>If you have questions or concerns about this Privacy Policy, please contact us at:</p>
              <div class="contact-info-privacy">
                <p><a href="mailto:<EMAIL>"><strong><EMAIL></strong></a> or visit <a href="https://www.appbookme.com"><strong>https://www.appbookme.com</strong></a></p>
              </div>
            </div>

            <div class="back-to-home">
              <a href="index.html">← Back to Home</a>
            </div>

          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer-Section start -->
  <footer class="white_text">
    <div class="footer_bottom">
      <div class="container">
        <div class="ft_inner">
          <div class="copy_text">
            <p>&copy; Copyrights 2025. All rights reserved.</p>
          </div>

          <div class="design_by">
            <p>Design & developed by<a href="https://www.chrisansgroup.com/" target="_blank"> Chrisans Web Solutions</a></p>
          </div>
        </div>
      </div>
    </div>
  </footer>
  <!-- Footer-Section end -->

  <!-- go top button -->
  <div class="go_top" id="Gotop">
    <span><i class="icofont-arrow-up"></i></span>
  </div>

  <!-- Scripts -->
  <script src="js/jquery.js"></script>
  <script src="js/jquerymin.js"></script>
  <script src="js/owl.carousel.min.js"></script>
  <script src="js/bootstrap.min.js"></script>
  <script src="js/aos.js"></script>
  <script src='js/typed.min.js'></script>
  <script src="js/main.js"></script>

  <!-- Initialize AOS -->
  <script>
    AOS.init({
      duration: 1000,
      once: true
    });
  </script>

  <!-- Hide preloader -->
  <script>
    // Fallback to hide preloader immediately if jQuery is not loaded
    setTimeout(function() {
      var preloader = document.getElementById('preloader');
      if (preloader) {
        preloader.style.display = 'none';
      }
    }, 100);

    // jQuery-based preloader hiding (when jQuery loads)
    $(document).ready(function() {
      // Hide preloader immediately when DOM is ready
      $('#preloader').fadeOut('slow');

      // Also hide on window load as backup
      $(window).on('load', function() {
        $('#preloader').fadeOut('slow');
      });

      // Navbar scroll effect
      $(window).scroll(function() {
        if ($(this).scrollTop() > 50) {
          $('.site-header').addClass('scrolled');
        } else {
          $('.site-header').removeClass('scrolled');
        }
      });
    });
  </script>

</body>
</html>
