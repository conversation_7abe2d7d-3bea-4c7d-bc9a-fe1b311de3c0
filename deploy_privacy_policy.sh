#!/bin/bash

# BookMe Website Privacy Policy Enhancement Deployment Script
# This script helps deploy the enhanced website with Privacy Policy section

set -e  # Exit on any error

# Configuration
PRODUCTION_SERVER="*************"
SSH_KEY="~/.ssh/cloudpanel_key"
SSH_USER="developer-chrisans"
BACKUP_DIR="/tmp/bookme_backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to execute SSH commands
ssh_exec() {
    ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no ${SSH_USER}@${PRODUCTION_SERVER} "$1"
}

# Function to create backup
create_backup() {
    print_status "Creating backup of current website..."
    
    # Create backup directory
    mkdir -p ${BACKUP_DIR}
    
    # Download current website
    curl -s https://www.appbookme.com > "${BACKUP_DIR}/website_backup_${TIMESTAMP}.html"
    
    if [ $? -eq 0 ]; then
        print_success "Backup created: ${BACKUP_DIR}/website_backup_${TIMESTAMP}.html"
    else
        print_error "Failed to create backup"
        exit 1
    fi
}

# Function to verify database content
verify_database_content() {
    print_status "Verifying privacy policy content in database..."
    
    DB_CONTENT=$(ssh_exec "cd /home/<USER>/htdocs/app.appbookme.com/public && mysql -h 127.0.0.1 -u appbookme-user49 -p'wcj6PZu59ongVyIq21Qe#@38' appbookme-database49 -e \"SELECT Title FROM privacypolicy WHERE Privacy_ID = 1;\" 2>/dev/null")
    
    if [[ $DB_CONTENT == *"Privacy Policy"* ]]; then
        print_success "Database content verified"
    else
        print_warning "Database content may need verification"
    fi
}

# Function to test website accessibility
test_website() {
    print_status "Testing website accessibility..."
    
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://www.appbookme.com)
    
    if [ "$HTTP_STATUS" = "200" ]; then
        print_success "Website is accessible (HTTP $HTTP_STATUS)"
    else
        print_error "Website accessibility issue (HTTP $HTTP_STATUS)"
        exit 1
    fi
}

# Function to validate enhanced HTML
validate_html() {
    print_status "Validating enhanced HTML file..."
    
    if [ ! -f "enhanced_website.html" ]; then
        print_error "enhanced_website.html not found in current directory"
        exit 1
    fi
    
    # Check for Privacy Policy section
    if grep -q "privacy-policy" enhanced_website.html; then
        print_success "Privacy Policy section found in HTML"
    else
        print_error "Privacy Policy section not found in HTML"
        exit 1
    fi
    
    # Check for navigation link
    if grep -q "Privacy Policy" enhanced_website.html; then
        print_success "Privacy Policy navigation link found"
    else
        print_error "Privacy Policy navigation link not found"
        exit 1
    fi
}

# Function to find website files location
find_website_files() {
    print_status "Attempting to locate static website files..."
    
    # Try common web directories
    POSSIBLE_LOCATIONS=(
        "/var/www/html"
        "/home/<USER>/htdocs"
        "/home/<USER>/public_html"
        "/home/<USER>/htdocs/www.appbookme.com"
        "/usr/share/nginx/html"
    )
    
    for location in "${POSSIBLE_LOCATIONS[@]}"; do
        print_status "Checking: $location"
        RESULT=$(ssh_exec "ls -la $location 2>/dev/null | head -5" 2>/dev/null || echo "not_found")
        if [[ $RESULT != "not_found" ]]; then
            print_success "Found directory: $location"
            echo "$RESULT"
        fi
    done
}

# Function to display deployment summary
show_deployment_summary() {
    echo ""
    echo "=================================="
    echo "DEPLOYMENT SUMMARY"
    echo "=================================="
    echo "✅ Enhanced website file created: enhanced_website.html"
    echo "✅ Privacy Policy content retrieved from database"
    echo "✅ Navigation menu updated with Privacy Policy link"
    echo "✅ Responsive design implemented"
    echo "✅ Backup created: ${BACKUP_DIR}/website_backup_${TIMESTAMP}.html"
    echo ""
    echo "MANUAL STEPS REQUIRED:"
    echo "1. Locate the actual static website files on production server"
    echo "2. Replace current index.html with enhanced_website.html"
    echo "3. Test the website thoroughly"
    echo "4. Verify all functionality works correctly"
    echo ""
    echo "FILES CREATED:"
    echo "- enhanced_website.html (Enhanced website with Privacy Policy)"
    echo "- deployment_instructions.md (Detailed deployment guide)"
    echo "- deploy_privacy_policy.sh (This deployment script)"
    echo ""
}

# Main deployment process
main() {
    echo "========================================"
    echo "BookMe Privacy Policy Enhancement Deploy"
    echo "========================================"
    echo ""
    
    # Step 1: Validate prerequisites
    print_status "Starting deployment process..."
    
    # Step 2: Test current website
    test_website
    
    # Step 3: Create backup
    create_backup
    
    # Step 4: Verify database content
    verify_database_content
    
    # Step 5: Validate enhanced HTML
    validate_html
    
    # Step 6: Find website files location
    find_website_files
    
    # Step 7: Show summary
    show_deployment_summary
    
    print_success "Pre-deployment checks completed successfully!"
    print_warning "Manual deployment of enhanced_website.html is required."
    print_status "Please refer to deployment_instructions.md for detailed steps."
}

# Check if script is being run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
