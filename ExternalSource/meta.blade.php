<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="format-detection" content="telephone=no">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <title>{{ $news->title_en }}</title>

  <!-- OG Metadata -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="{{ url()->current() }}">
  <meta property="og:title" content="{{ $news->title_en }}">
  <meta property="og:description" content="{{ \Illuminate\Support\Str::limit($news->content_en, 150, '...') }}">
  <meta property="og:image" content="{{ $news->featuredImage->low }}">
  <meta property="og:image:width" content="1200">
  <meta property="og:image:height" content="630">

  <!-- Twitter Metadata -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="{{ $news->title_en }}">
  <meta name="twitter:description" content="{{ \Illuminate\Support\Str::limit($news->content_en, 150, '...') }}">
  <meta name="twitter:image" content="{{ $news->featuredImage->low }}">

  <!-- Fonts and Swiper CSS -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"/>

  <style>
    /* Mobile-first responsive design */
    * {
      box-sizing: border-box;
    }

    body {
      margin: 0;
      font-family: 'Inter', sans-serif;
      background: #f2f3f5;
      color: #1c1c1e;
      font-size: 1rem;
      line-height: 1.6;
    }

    /* Mobile-first: App Banner */
    .app-banner {
      position: sticky;
      top: 0;
      z-index: 1000;
      background: #fff;
      padding: clamp(0.75rem, 2vw, 1rem) clamp(1rem, 4vw, 1.5rem);
      text-align: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .app-banner h2 {
      margin: 0 0 0.5rem;
      font-size: clamp(1rem, 3vw, 1.125rem);
      font-weight: 600;
    }

    .store-buttons {
      display: flex;
      justify-content: center;
      gap: clamp(0.75rem, 2vw, 1rem);
      flex-wrap: wrap;
      align-items: center;
    }

    .store-buttons a {
      display: inline-block;
      min-height: 44px;
      min-width: 44px;
      padding: 0.25rem;
      border-radius: 4px;
      transition: transform 0.2s ease;
    }

    .store-buttons a:hover,
    .store-buttons a:focus {
      transform: scale(1.05);
      outline: 2px solid #007AFF;
      outline-offset: 2px;
    }

    .store-buttons img {
      height: clamp(40px, 8vw, 48px);
      width: auto;
      display: block;
    }

    /* Mobile-first: Container */
    .container {
      max-width: 100%;
      margin: clamp(1rem, 3vw, 1.5rem) clamp(1rem, 4vw, 1.5rem);
      background: #fff;
      padding: clamp(1rem, 4vw, 1.5rem);
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    /* Mobile-first: Typography */
    .news-title {
      font-size: clamp(1.25rem, 5vw, 1.625rem);
      font-weight: 600;
      margin-bottom: clamp(0.75rem, 2vw, 1rem);
      line-height: 1.3;
    }

    .news-date {
      font-size: clamp(0.875rem, 2.5vw, 1rem);
      color: #666;
      margin: clamp(0.5rem, 1vw, 0.75rem) 0 clamp(1rem, 3vw, 1.5rem);
      text-align: left;
    }

    /* Enhanced content formatting and typography */
    .news-content {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: clamp(1rem, 2.5vw, 1.125rem);
      line-height: 1.7;
      color: #2c2c2e;
      max-width: none;
      word-wrap: break-word;
      overflow-wrap: break-word;
      hyphens: auto;
    }

    /* Paragraph styling */
    .news-content p {
      margin: 0 0 clamp(1.2rem, 3vw, 1.8rem) 0;
      text-align: justify;
      text-justify: inter-word;
    }

    .news-content p:last-child {
      margin-bottom: 0;
    }

    .news-content p:first-child {
      margin-top: 0;
      font-size: clamp(1.1rem, 2.8vw, 1.25rem);
      font-weight: 500;
      color: #1c1c1e;
    }

    /* Headings hierarchy */
    .news-content h1,
    .news-content h2,
    .news-content h3,
    .news-content h4,
    .news-content h5,
    .news-content h6 {
      font-weight: 600;
      margin: clamp(2rem, 5vw, 3rem) 0 clamp(0.8rem, 2vw, 1.2rem) 0;
      line-height: 1.3;
      color: #1c1c1e;
    }

    .news-content h1 {
      font-size: clamp(1.6rem, 4.5vw, 2.2rem);
      border-bottom: 3px solid #007AFF;
      padding-bottom: clamp(0.5rem, 1.5vw, 0.8rem);
      margin-bottom: clamp(1.5rem, 3vw, 2rem);
    }

    .news-content h2 {
      font-size: clamp(1.4rem, 4vw, 1.9rem);
      border-left: 4px solid #007AFF;
      padding-left: clamp(0.8rem, 2vw, 1rem);
    }

    .news-content h3 {
      font-size: clamp(1.25rem, 3.5vw, 1.6rem);
      color: #007AFF;
    }

    .news-content h4 {
      font-size: clamp(1.1rem, 3vw, 1.4rem);
    }

    .news-content h5,
    .news-content h6 {
      font-size: clamp(1rem, 2.5vw, 1.2rem);
    }

    /* Lists styling */
    .news-content ul,
    .news-content ol {
      margin: clamp(1.2rem, 3vw, 1.8rem) 0;
      padding-left: clamp(1.5rem, 4vw, 2.5rem);
    }

    .news-content li {
      margin-bottom: clamp(0.6rem, 1.5vw, 0.9rem);
      line-height: 1.6;
    }

    .news-content li:last-child {
      margin-bottom: 0;
    }

    .news-content ul li {
      list-style-type: none;
      position: relative;
    }

    .news-content ul li::before {
      content: '•';
      color: #007AFF;
      font-weight: bold;
      position: absolute;
      left: -1.2rem;
      font-size: 1.2em;
    }

    .news-content ol li {
      padding-left: 0.5rem;
    }

    /* Blockquotes */
    .news-content blockquote {
      margin: clamp(1.5rem, 4vw, 2.5rem) 0;
      padding: clamp(1.2rem, 3vw, 2rem);
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-left: 5px solid #007AFF;
      border-radius: 0 12px 12px 0;
      font-style: italic;
      position: relative;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .news-content blockquote::before {
      content: '"';
      font-size: clamp(2.5rem, 6vw, 4rem);
      color: #007AFF;
      position: absolute;
      top: clamp(-0.5rem, -1vw, -1rem);
      left: clamp(0.5rem, 1vw, 1rem);
      opacity: 0.3;
      font-family: Georgia, serif;
    }

    .news-content blockquote p {
      margin-bottom: 0;
      font-size: clamp(1.05rem, 2.8vw, 1.2rem);
    }

    /* Links */
    .news-content a {
      color: #007AFF;
      text-decoration: none;
      border-bottom: 1px solid transparent;
      transition: all 0.2s ease;
      font-weight: 500;
    }

    .news-content a:hover,
    .news-content a:focus {
      border-bottom-color: #007AFF;
      background-color: rgba(0, 122, 255, 0.1);
      padding: 0.1em 0.2em;
      border-radius: 3px;
      outline: none;
    }

    /* Emphasis and strong text */
    .news-content strong,
    .news-content b {
      font-weight: 600;
      color: #1c1c1e;
      background: linear-gradient(120deg, transparent 0%, rgba(255, 193, 7, 0.3) 0%, rgba(255, 193, 7, 0.3) 100%, transparent 100%);
      padding: 0.1em 0.2em;
      border-radius: 3px;
    }

    .news-content em,
    .news-content i {
      font-style: italic;
      color: #48484a;
    }

    /* Code styling */
    .news-content code {
      background: #f1f3f4;
      padding: 0.3em 0.5em;
      border-radius: 6px;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
      font-size: 0.9em;
      border: 1px solid #e1e5e9;
    }

    .news-content pre {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: clamp(1.2rem, 3vw, 2rem);
      border-radius: 12px;
      overflow-x: auto;
      margin: clamp(1.5rem, 4vw, 2.5rem) 0;
      border: 1px solid #e1e5e9;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .news-content pre code {
      background: none;
      padding: 0;
      border: none;
    }

    /* Tables */
    .news-content table {
      width: 100%;
      border-collapse: collapse;
      margin: clamp(1.2rem, 3vw, 1.8rem) 0;
      font-size: clamp(0.9rem, 2.2vw, 1rem);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      border-radius: 8px;
      overflow: hidden;
    }

    .news-content th,
    .news-content td {
      padding: clamp(0.6rem, 2vw, 1rem);
      text-align: left;
      border-bottom: 1px solid #e1e5e9;
    }

    .news-content th {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      font-weight: 600;
      color: #1c1c1e;
    }

    /* Horizontal rules */
    .news-content hr {
      border: none;
      height: 3px;
      background: linear-gradient(to right, transparent, #007AFF, transparent);
      margin: clamp(2.5rem, 6vw, 4rem) 0;
      border-radius: 2px;
    }

    /* Special content highlighting */
    .news-content .highlight {
      background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
      border: 1px solid #ffc107;
      border-radius: 8px;
      padding: clamp(1rem, 3vw, 1.5rem);
      margin: clamp(1.2rem, 3vw, 1.8rem) 0;
      border-left: 4px solid #ffc107;
    }

    .news-content .info-box {
      background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
      border: 1px solid #17a2b8;
      border-radius: 8px;
      padding: clamp(1rem, 3vw, 1.5rem);
      margin: clamp(1.2rem, 3vw, 1.8rem) 0;
      border-left: 4px solid #17a2b8;
    }



    /* Mobile-first: Swiper */
    .swiper {
      width: 100%;
      height: auto;
      margin-bottom: clamp(1rem, 3vw, 1.5rem);
      border-radius: 8px;
      overflow: hidden;
    }

    .swiper-slide img {
      width: 100%;
      height: auto;
      object-fit: cover;
      display: block;
    }

    /* Small phones (320px+) */
    @media (min-width: 20rem) {
      .container {
        margin: clamp(1.25rem, 4vw, 2rem) auto;
        max-width: calc(100% - 2rem);
      }
    }

    /* Large phones (480px+) */
    @media (min-width: 30rem) {
      .app-banner {
        padding: 1rem 1.5rem;
      }

      .store-buttons {
        gap: 1rem;
      }

      .container {
        padding: 1.5rem;
        max-width: calc(100% - 3rem);
      }
    }

    /* Tablets (768px+) */
    @media (min-width: 48rem) {
      .container {
        max-width: 700px;
        margin: 2rem auto;
        padding: 2rem;
      }

      .news-title {
        font-size: 1.75rem;
      }

      .store-buttons img {
        height: 48px;
      }
    }

    /* Desktop (1024px+) */
    @media (min-width: 64rem) {
      .container {
        max-width: 800px;
        margin: 2.5rem auto;
        padding: 2.5rem;
      }

      .news-title {
        font-size: 2rem;
      }

      .news-content {
        font-size: 1.125rem;
      }
    }

    /* Large desktop (1200px+) */
    @media (min-width: 75rem) {
      .container {
        max-width: 900px;
        padding: 3rem;
      }
    }

    /* Focus and accessibility improvements */
    a:focus,
    button:focus {
      outline: 2px solid #007AFF;
      outline-offset: 2px;
    }

    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
      .store-buttons a {
        transition: none;
      }

      .store-buttons a:hover,
      .store-buttons a:focus {
        transform: none;
      }
    }
  </style>
        <!-- Content Processing Function -->
  <script>
    // Enhanced content formatting function
    function formatNewsContent(content) {
      if (!content) return '';

      // Escape HTML first for security
      const escapeHtml = (text) => {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
      };

      let formattedContent = escapeHtml(content);

      // Convert double line breaks to paragraph breaks
      formattedContent = formattedContent.replace(/\n\s*\n/g, '</p><p>');

      // Wrap in paragraphs
      formattedContent = '<p>' + formattedContent + '</p>';

      // Clean up empty paragraphs
      formattedContent = formattedContent.replace(/<p>\s*<\/p>/g, '');

      // Format headings (lines starting with # or all caps)
      formattedContent = formattedContent.replace(/<p>([A-Z][A-Z\s]{10,})<\/p>/g, '<h2>$1</h2>');
      formattedContent = formattedContent.replace(/<p>#{3}\s*(.+?)<\/p>/g, '<h3>$1</h3>');
      formattedContent = formattedContent.replace(/<p>#{2}\s*(.+?)<\/p>/g, '<h2>$1</h2>');
      formattedContent = formattedContent.replace(/<p>#{1}\s*(.+?)<\/p>/g, '<h1>$1</h1>');

      // Format lists (lines starting with - or * or numbers)
      formattedContent = formattedContent.replace(/<p>([•\-\*]\s*.+?)<\/p>/g, '<li>$1</li>');
      formattedContent = formattedContent.replace(/<p>(\d+\.\s*.+?)<\/p>/g, '<li>$1</li>');

      // Wrap consecutive list items in ul/ol
      formattedContent = formattedContent.replace(/(<li>[•\-\*]\s*.+?<\/li>)+/g, (match) => {
        const items = match.replace(/[•\-\*]\s*/g, '');
        return '<ul>' + items + '</ul>';
      });

      formattedContent = formattedContent.replace(/(<li>\d+\.\s*.+?<\/li>)+/g, (match) => {
        const items = match.replace(/\d+\.\s*/g, '');
        return '<ol>' + items + '</ol>';
      });

      // Format quotes (lines starting with > or wrapped in quotes)
      formattedContent = formattedContent.replace(/<p>&gt;\s*(.+?)<\/p>/g, '<blockquote><p>$1</p></blockquote>');
      formattedContent = formattedContent.replace(/<p>&quot;(.+?)&quot;<\/p>/g, '<blockquote><p>$1</p></blockquote>');

      // Format emphasis
      formattedContent = formattedContent.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
      formattedContent = formattedContent.replace(/\*(.+?)\*/g, '<em>$1</em>');
      formattedContent = formattedContent.replace(/__(.+?)__/g, '<strong>$1</strong>');
      formattedContent = formattedContent.replace(/_(.+?)_/g, '<em>$1</em>');

      // Format code
      formattedContent = formattedContent.replace(/`(.+?)`/g, '<code>$1</code>');

      // Format horizontal rules
      formattedContent = formattedContent.replace(/<p>[-=]{3,}<\/p>/g, '<hr>');

      // Format URLs
      const urlRegex = /(https?:\/\/[^\s<>"]+)/g;
      formattedContent = formattedContent.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');

      // Add special formatting for common patterns
      formattedContent = formattedContent.replace(/<p>(IMPORTANT|NOTE|WARNING|ATTENTION):\s*(.+?)<\/p>/gi,
        '<div class="highlight"><strong>$1:</strong> $2</div>');

      formattedContent = formattedContent.replace(/<p>(INFO|TIP|FACT):\s*(.+?)<\/p>/gi,
        '<div class="info-box"><strong>$1:</strong> $2</div>');

      return formattedContent;
    }
  </script>

        <!-- Optimized Mobile App Promotion -->
  <script>
    // More user-friendly app promotion without aggressive redirects
    document.addEventListener('DOMContentLoaded', function () {
      const isAndroid = /Android/i.test(navigator.userAgent);
      const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);

      // Only show app promotion banner, don't auto-redirect
      // This allows users to read the content while still promoting the app
      if (isIOS || isAndroid) {
        // Add a subtle animation to draw attention to the app banner
        const banner = document.querySelector('.app-banner');
        if (banner) {
          banner.style.animation = 'subtle-pulse 2s ease-in-out';
        }

        // Optional: Add app store smart banner for iOS
        if (isIOS) {
          const smartBanner = document.createElement('meta');
          smartBanner.name = 'apple-itunes-app';
          smartBanner.content = 'app-id=1473798584';
          document.head.appendChild(smartBanner);
        }
      }
    });

    // Add subtle animation keyframes
    const style = document.createElement('style');
    style.textContent = `
      @keyframes subtle-pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.02); }
      }
    `;
    document.head.appendChild(style);
  </script>
</head>
<body>

  <!-- Sticky App Download Banner -->
  <div class="app-banner" role="banner">
    <h2>Download Our App</h2>
    <div class="store-buttons" role="group" aria-label="App store download links">
      <a href="https://apps.apple.com/in/app/kuwait-sports-club-for-deaf/id1473798584"
         target="_blank"
         rel="noopener noreferrer"
         aria-label="Download Kuwait Sports Club for Deaf app from Apple App Store">
        <img src="https://developer.apple.com/assets/elements/badges/download-on-the-app-store.svg"
             alt="Download on the App Store"
             loading="lazy">
      </a>
      <a href="https://play.google.com/store/apps/details?id=com.kscd.mobile.app"
         target="_blank"
         rel="noopener noreferrer"
         aria-label="Download Kuwait Sports Club for Deaf app from Google Play Store">
        <img src="https://upload.wikimedia.org/wikipedia/commons/7/78/Google_Play_Store_badge_EN.svg"
             alt="Get it on Google Play"
             loading="lazy">
      </a>
    </div>
  </div>

  <!-- News Article -->
  <div class="container">
    <h1 class="news-title">{{ $news->title_en }}</h1>

    <!-- Image Slider -->
    @if (!empty($image) && count($image))
      <div class="swiper">
        <div class="swiper-wrapper">
          @foreach ($image as $index => $img)
            @if (!empty($img->original))
              <div class="swiper-slide">
                <img src="{{ $img->original }}"
                     alt="News image {{ $index + 1 }}"
                     loading="{{ $index === 0 ? 'eager' : 'lazy' }}"
                     decoding="async">
              </div>
            @endif
          @endforeach
        </div>
        <!-- Optional: add arrows or pagination -->
        <div class="swiper-pagination"></div>
      </div>
    @endif

    <!-- Date -->
    @if (!empty($news->date_en))
      <div class="news-date">{{ $news->date_en }}</div>
    @endif

    <!-- Enhanced Content -->
    <div class="news-content" id="news-content">
      <!-- Content will be formatted by JavaScript -->
    </div>

    <!-- Hidden content for processing -->
    <script type="text/plain" id="raw-content">{{ $news->content_en }}</script>
  </div>

  <!-- Swiper JS -->
  <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
  <script>
    // Initialize Swiper
    const swiper = new Swiper('.swiper', {
      loop: true,
      pagination: {
        el: '.swiper-pagination',
        clickable: true,
      },
    });

    // Process and display formatted content
    document.addEventListener('DOMContentLoaded', function() {
      const rawContent = document.getElementById('raw-content');
      const contentContainer = document.getElementById('news-content');

      if (rawContent && contentContainer) {
        const content = rawContent.textContent || rawContent.innerText;
        const formattedContent = formatNewsContent(content);
        contentContainer.innerHTML = formattedContent;
      }
    });
  </script>
</body>
</html>