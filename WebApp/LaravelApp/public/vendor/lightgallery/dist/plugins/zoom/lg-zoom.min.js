/**
 * lightgallery | 2.5.0 | June 13th 2022
 * http://www.lightgalleryjs.com/
 * Copyright (c) 2020 Sachin Neravath;
 * @license GPLv3
 */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).lgZoom=e()}(this,(function(){"use strict";var t=function(){return(t=Object.assign||function(t){for(var e,o=1,i=arguments.length;o<i;o++)for(var s in e=arguments[o])Object.prototype.hasOwnProperty.call(e,s)&&(t[s]=e[s]);return t}).apply(this,arguments)},e={scale:1,zoom:!0,actualSize:!0,showZoomInOutIcons:!1,actualSizeIcons:{zoomIn:"lg-zoom-in",zoomOut:"lg-zoom-out"},enableZoomAfter:300,zoomPluginStrings:{zoomIn:"Zoom in",zoomOut:"Zoom out",viewActualSize:"View actual size"}},o="lgContainerResize",i="lgBeforeOpen",s="lgAfterOpen",a="lgSlideItemLoad",r="lgAfterSlide",n="lgRotateLeft",l="lgRotateRight",g="lgFlipHorizontal",c="lgFlipVertical";return function(){function h(o,i){return this.core=o,this.$LG=i,this.settings=t(t({},e),this.core.settings),this}return h.prototype.buildTemplates=function(){var t=this.settings.showZoomInOutIcons?'<button id="'+this.core.getIdName("lg-zoom-in")+'" type="button" aria-label="'+this.settings.zoomPluginStrings.zoomIn+'" class="lg-zoom-in lg-icon"></button><button id="'+this.core.getIdName("lg-zoom-out")+'" type="button" aria-label="'+this.settings.zoomPluginStrings.zoomIn+'" class="lg-zoom-out lg-icon"></button>':"";this.settings.actualSize&&(t+='<button id="'+this.core.getIdName("lg-actual-size")+'" type="button" aria-label="'+this.settings.zoomPluginStrings.viewActualSize+'" class="'+this.settings.actualSizeIcons.zoomIn+' lg-icon"></button>'),this.core.outer.addClass("lg-use-transition-for-zoom"),this.core.$toolbar.first().append(t)},h.prototype.enableZoom=function(t){var e=this,o=this.settings.enableZoomAfter+t.detail.delay;this.$LG("body").first().hasClass("lg-from-hash")&&t.detail.delay?o=0:this.$LG("body").first().removeClass("lg-from-hash"),this.zoomableTimeout=setTimeout((function(){e.isImageSlide()&&(e.core.getSlideItem(t.detail.index).addClass("lg-zoomable"),t.detail.index===e.core.index&&e.setZoomEssentials())}),o+30)},h.prototype.enableZoomOnSlideItemLoad=function(){this.core.LGel.on(a+".zoom",this.enableZoom.bind(this))},h.prototype.getModifier=function(t,e,o){var i=t;t=Math.abs(t);var s=this.getCurrentTransform(o);if(!s)return 1;var a=1;if("X"===e){var r=Math.sign(parseFloat(s[0]));0===t||180===t?a=1:90===t&&(a=-90===i&&1===r||90===i&&-1===r?-1:1),a*=r}else{var n=Math.sign(parseFloat(s[3]));if(0===t||180===t)a=1;else if(90===t){var l=parseFloat(s[1]),g=parseFloat(s[2]);a=Math.sign(l*g*i*n)}a*=n}return a},h.prototype.getImageSize=function(t,e,o){return 90===Math.abs(e)&&(o="x"===o?"y":"x"),t[{y:"offsetHeight",x:"offsetWidth"}[o]]},h.prototype.getDragCords=function(t,e){return 90===e?{x:t.pageY,y:t.pageX}:{x:t.pageX,y:t.pageY}},h.prototype.getSwipeCords=function(t,e){var o=t.targetTouches[0].pageX,i=t.targetTouches[0].pageY;return 90===e?{x:i,y:o}:{x:o,y:i}},h.prototype.getDragAllowedAxises=function(t,e){e=e||this.scale||1;var o=this.imageYSize*e>this.containerRect.height,i=this.imageXSize*e>this.containerRect.width;return 90===t?{allowX:o,allowY:i}:{allowX:i,allowY:o}},h.prototype.getCurrentTransform=function(t){if(t){var e=window.getComputedStyle(t,null),o=e.getPropertyValue("-webkit-transform")||e.getPropertyValue("-moz-transform")||e.getPropertyValue("-ms-transform")||e.getPropertyValue("-o-transform")||e.getPropertyValue("transform")||"none";return"none"!==o?o.split("(")[1].split(")")[0].split(","):void 0}},h.prototype.getCurrentRotation=function(t){if(!t)return 0;var e=this.getCurrentTransform(t);return e?Math.round(Math.atan2(parseFloat(e[1]),parseFloat(e[0]))*(180/Math.PI)):0},h.prototype.setZoomEssentials=function(){var t=this.core.getSlideItem(this.core.index).find(".lg-image").first(),e=this.core.getSlideItem(this.core.index).find(".lg-img-rotate").first().get();this.rotateValue=this.getCurrentRotation(e),this.imageYSize=this.getImageSize(t.get(),this.rotateValue,"y"),this.imageXSize=this.getImageSize(t.get(),this.rotateValue,"x"),this.containerRect=this.core.outer.get().getBoundingClientRect(),this.modifierX=this.getModifier(this.rotateValue,"X",e),this.modifierY=this.getModifier(this.rotateValue,"Y",e)},h.prototype.zoomImage=function(t){var e,o,i=(this.containerRect.width-this.imageXSize)/2+this.containerRect.left,s=this.core.mediaContainerPosition,a=s.top,r=s.bottom,n=Math.abs(a-r)/2,l=(this.containerRect.height-this.imageYSize-n*this.modifierX)/2+this.scrollTop+this.containerRect.top;1===t&&(this.positionChanged=!1);var g=this.getDragAllowedAxises(Math.abs(this.rotateValue),t),c=g.allowY,h=g.allowX;this.positionChanged&&(e=this.left/(this.scale-1),o=this.top/(this.scale-1),this.pageX=Math.abs(e)+i,this.pageY=Math.abs(o)+l,this.positionChanged=!1);var m=this.getPossibleSwipeDragCords(this.rotateValue,t),u=(t-1)*(i-this.pageX),d=(t-1)*(l-this.pageY);h?this.isBeyondPossibleLeft(u,m.minX)?u=m.minX:this.isBeyondPossibleRight(u,m.maxX)&&(u=m.maxX):t>1&&(u<m.minX?u=m.minX:u>m.maxX&&(u=m.maxX)),c?this.isBeyondPossibleTop(d,m.minY)?d=m.minY:this.isBeyondPossibleBottom(d,m.maxY)&&(d=m.maxY):t>1&&(d<m.minY?d=m.minY:d>m.maxY&&(d=m.maxY)),this.setZoomStyles({x:u,y:d,scale:t})},h.prototype.setZoomStyles=function(t){var e=this.core.getSlideItem(this.core.index).find(".lg-image").first(),o=this.core.outer.find(".lg-current .lg-dummy-img").first(),i=e.parent();this.scale=t.scale,e.css("transform","scale3d("+t.scale+", "+t.scale+", 1)"),o.css("transform","scale3d("+t.scale+", "+t.scale+", 1)");var s="translate3d("+t.x+"px, "+t.y+"px, 0)";i.css("transform",s),this.left=t.x,this.top=t.y},h.prototype.setActualSize=function(t,e){var o=this;if(this.isImageSlide()&&!this.core.outer.hasClass("lg-first-slide-loading")){var i=this.getCurrentImageActualSizeScale();this.core.outer.hasClass("lg-zoomed")?this.scale=1:this.scale=this.getScale(i),this.setPageCords(e),this.beginZoom(this.scale),this.zoomImage(this.scale),setTimeout((function(){o.core.outer.removeClass("lg-grabbing").addClass("lg-grab")}),10)}},h.prototype.getNaturalWidth=function(t){var e=this.core.getSlideItem(t).find(".lg-image").first(),o=this.core.galleryItems[t].width;return o?parseFloat(o):e.get().naturalWidth},h.prototype.getActualSizeScale=function(t,e){return t>e?t/e||2:1},h.prototype.getCurrentImageActualSizeScale=function(){var t=this.core.getSlideItem(this.core.index).find(".lg-image").first().get().offsetWidth,e=this.getNaturalWidth(this.core.index)||t;return this.getActualSizeScale(e,t)},h.prototype.getPageCords=function(t){var e={};if(t)e.x=t.pageX||t.targetTouches[0].pageX,e.y=t.pageY||t.targetTouches[0].pageY;else{var o=this.core.outer.get().getBoundingClientRect();e.x=o.width/2+o.left,e.y=o.height/2+this.scrollTop+o.top}return e},h.prototype.setPageCords=function(t){var e=this.getPageCords(t);this.pageX=e.x,this.pageY=e.y},h.prototype.beginZoom=function(t){(this.core.outer.removeClass("lg-zoom-drag-transition lg-zoom-dragging"),t>1)?(this.core.outer.addClass("lg-zoomed"),this.core.getElementById("lg-actual-size").removeClass(this.settings.actualSizeIcons.zoomIn).addClass(this.settings.actualSizeIcons.zoomOut)):this.resetZoom();return t>1},h.prototype.getScale=function(t){var e=this.getCurrentImageActualSizeScale();return t<1?t=1:t>e&&(t=e),t},h.prototype.init=function(){var t=this;if(this.settings.zoom){this.buildTemplates(),this.enableZoomOnSlideItemLoad();var e=null;this.core.outer.on("dblclick.lg",(function(e){t.$LG(e.target).hasClass("lg-image")&&t.setActualSize(t.core.index,e)})),this.core.outer.on("touchstart.lg",(function(o){var i=t.$LG(o.target);1===o.targetTouches.length&&i.hasClass("lg-image")&&(e?(clearTimeout(e),e=null,o.preventDefault(),t.setActualSize(t.core.index,o)):e=setTimeout((function(){e=null}),300))})),this.core.LGel.on(o+".zoom "+l+".zoom "+n+".zoom "+g+".zoom "+c+".zoom",(function(){t.core.lgOpened&&t.isImageSlide()&&(t.setPageCords(),t.setZoomEssentials(),t.zoomImage(t.scale))})),this.$LG(window).on("scroll.lg.zoom.global"+this.core.lgId,(function(){t.core.lgOpened&&(t.scrollTop=t.$LG(window).scrollTop())})),this.core.getElementById("lg-zoom-out").on("click.lg",(function(){t.core.outer.find(".lg-current .lg-image").get()&&(t.scale-=t.settings.scale,t.scale=t.getScale(t.scale),t.beginZoom(t.scale),t.zoomImage(t.scale))})),this.core.getElementById("lg-zoom-in").on("click.lg",(function(){t.zoomIn()})),this.core.getElementById("lg-actual-size").on("click.lg",(function(){t.setActualSize(t.core.index)})),this.core.LGel.on(i+".zoom",(function(){t.core.outer.find(".lg-item").removeClass("lg-zoomable")})),this.core.LGel.on(s+".zoom",(function(){t.scrollTop=t.$LG(window).scrollTop(),t.pageX=t.core.outer.width()/2,t.pageY=t.core.outer.height()/2+t.scrollTop,t.scale=1})),this.core.LGel.on(r+".zoom",(function(e){var o=e.detail.prevIndex;t.scale=1,t.positionChanged=!1,t.resetZoom(o),t.isImageSlide()&&t.setZoomEssentials()})),this.zoomDrag(),this.pinchZoom(),this.zoomSwipe(),this.zoomableTimeout=!1,this.positionChanged=!1}},h.prototype.zoomIn=function(t){this.isImageSlide()&&(t?this.scale=t:this.scale+=this.settings.scale,this.scale=this.getScale(this.scale),this.beginZoom(this.scale),this.zoomImage(this.scale))},h.prototype.resetZoom=function(t){this.core.outer.removeClass("lg-zoomed lg-zoom-drag-transition");var e=this.core.getElementById("lg-actual-size"),o=this.core.getSlideItem(void 0!==t?t:this.core.index);e.removeClass(this.settings.actualSizeIcons.zoomOut).addClass(this.settings.actualSizeIcons.zoomIn),o.find(".lg-img-wrap").first().removeAttr("style"),o.find(".lg-image").first().removeAttr("style"),this.scale=1,this.left=0,this.top=0,this.setPageCords()},h.prototype.getTouchDistance=function(t){return Math.sqrt((t.targetTouches[0].pageX-t.targetTouches[1].pageX)*(t.targetTouches[0].pageX-t.targetTouches[1].pageX)+(t.targetTouches[0].pageY-t.targetTouches[1].pageY)*(t.targetTouches[0].pageY-t.targetTouches[1].pageY))},h.prototype.pinchZoom=function(){var t=this,e=0,o=!1,i=1,s=this.core.getSlideItem(this.core.index);this.core.$inner.on("touchstart.lg",(function(o){s=t.core.getSlideItem(t.core.index),t.isImageSlide()&&(2!==o.targetTouches.length||t.core.outer.hasClass("lg-first-slide-loading")||!t.$LG(o.target).hasClass("lg-item")&&!s.get().contains(o.target)||(i=t.scale||1,t.core.outer.removeClass("lg-zoom-drag-transition lg-zoom-dragging"),t.core.touchAction="pinch",e=t.getTouchDistance(o)))})),this.core.$inner.on("touchmove.lg",(function(a){if(2===a.targetTouches.length&&"pinch"===t.core.touchAction&&(t.$LG(a.target).hasClass("lg-item")||s.get().contains(a.target))){a.preventDefault();var r=t.getTouchDistance(a),n=e-r;!o&&Math.abs(n)>5&&(o=!0),o&&(t.scale=Math.max(1,i+.008*-n),t.zoomImage(t.scale))}})),this.core.$inner.on("touchend.lg",(function(i){"pinch"===t.core.touchAction&&(t.$LG(i.target).hasClass("lg-item")||s.get().contains(i.target))&&(o=!1,e=0,t.scale<=1?t.resetZoom():(t.scale=t.getScale(t.scale),t.zoomImage(t.scale),t.core.outer.addClass("lg-zoomed")),t.core.touchAction=void 0)}))},h.prototype.touchendZoom=function(t,e,o,i,s,a){var r=e.x-t.x,n=e.y-t.y,l=Math.abs(r)/s+1,g=Math.abs(n)/s+1;l>2&&(l+=1),g>2&&(g+=1),r*=l,n*=g;var c=this.core.getSlideItem(this.core.index).find(".lg-img-wrap").first(),h={};h.x=this.left+r*this.modifierX,h.y=this.top+n*this.modifierY;var m=this.getPossibleSwipeDragCords(a);(Math.abs(r)>15||Math.abs(n)>15)&&(i&&(this.isBeyondPossibleTop(h.y,m.minY)?h.y=m.minY:this.isBeyondPossibleBottom(h.y,m.maxY)&&(h.y=m.maxY)),o&&(this.isBeyondPossibleLeft(h.x,m.minX)?h.x=m.minX:this.isBeyondPossibleRight(h.x,m.maxX)&&(h.x=m.maxX)),i?this.top=h.y:h.y=this.top,o?this.left=h.x:h.x=this.left,this.setZoomSwipeStyles(c,h),this.positionChanged=!0)},h.prototype.getZoomSwipeCords=function(t,e,o,i,s){var a={};if(i){if(a.y=this.top+(e.y-t.y)*this.modifierY,this.isBeyondPossibleTop(a.y,s.minY)){var r=s.minY-a.y;a.y=s.minY-r/6}else if(this.isBeyondPossibleBottom(a.y,s.maxY)){var n=a.y-s.maxY;a.y=s.maxY+n/6}}else a.y=this.top;if(o){if(a.x=this.left+(e.x-t.x)*this.modifierX,this.isBeyondPossibleLeft(a.x,s.minX)){var l=s.minX-a.x;a.x=s.minX-l/6}else if(this.isBeyondPossibleRight(a.x,s.maxX)){var g=a.x-s.maxX;a.x=s.maxX+g/6}}else a.x=this.left;return a},h.prototype.isBeyondPossibleLeft=function(t,e){return t>=e},h.prototype.isBeyondPossibleRight=function(t,e){return t<=e},h.prototype.isBeyondPossibleTop=function(t,e){return t>=e},h.prototype.isBeyondPossibleBottom=function(t,e){return t<=e},h.prototype.isImageSlide=function(){var t=this.core.galleryItems[this.core.index];return"image"===this.core.getSlideType(t)},h.prototype.getPossibleSwipeDragCords=function(t,e){var o=e||this.scale||1,i=Math.abs(o),s=this.core.mediaContainerPosition,a=s.top,r=s.bottom,n=Math.abs(a-r)/2,l=(this.imageYSize-this.containerRect.height)/2+n*this.modifierX,g=this.containerRect.height-this.imageYSize*i+l,c=(this.imageXSize-this.containerRect.width)/2,h=this.containerRect.width-this.imageXSize*i+c,m={minY:l,maxY:g,minX:c,maxX:h};return 90===Math.abs(t)&&(m={minY:c,maxY:h,minX:l,maxX:g}),m},h.prototype.setZoomSwipeStyles=function(t,e){t.css("transform","translate3d("+e.x+"px, "+e.y+"px, 0)")},h.prototype.zoomSwipe=function(){var t,e,o=this,i={},s={},a=!1,r=!1,n=!1,l=new Date,g=(new Date,this.core.getSlideItem(this.core.index));this.core.$inner.on("touchstart.lg",(function(s){if(o.isImageSlide()&&(g=o.core.getSlideItem(o.core.index),(o.$LG(s.target).hasClass("lg-item")||g.get().contains(s.target))&&1===s.targetTouches.length&&o.core.outer.hasClass("lg-zoomed"))){s.preventDefault(),l=new Date,o.core.touchAction="zoomSwipe",e=o.core.getSlideItem(o.core.index).find(".lg-img-wrap").first();var a=o.getDragAllowedAxises(Math.abs(o.rotateValue));n=a.allowY,((r=a.allowX)||n)&&(i=o.getSwipeCords(s,Math.abs(o.rotateValue))),t=o.getPossibleSwipeDragCords(o.rotateValue),o.core.outer.addClass("lg-zoom-dragging lg-zoom-drag-transition")}})),this.core.$inner.on("touchmove.lg",(function(l){if(1===l.targetTouches.length&&"zoomSwipe"===o.core.touchAction&&(o.$LG(l.target).hasClass("lg-item")||g.get().contains(l.target))){l.preventDefault(),o.core.touchAction="zoomSwipe",s=o.getSwipeCords(l,Math.abs(o.rotateValue));var c=o.getZoomSwipeCords(i,s,r,n,t);(Math.abs(s.x-i.x)>15||Math.abs(s.y-i.y)>15)&&(a=!0,o.setZoomSwipeStyles(e,c))}})),this.core.$inner.on("touchend.lg",(function(t){if("zoomSwipe"===o.core.touchAction&&(o.$LG(t.target).hasClass("lg-item")||g.get().contains(t.target))){if(o.core.touchAction=void 0,o.core.outer.removeClass("lg-zoom-dragging"),!a)return;a=!1;var e=(new Date).valueOf()-l.valueOf();o.touchendZoom(i,s,r,n,e,o.rotateValue)}}))},h.prototype.zoomDrag=function(){var t,e,o,i,s=this,a={},r={},n=!1,l=!1,g=!1,c=!1;this.core.outer.on("mousedown.lg.zoom",(function(e){if(s.isImageSlide()){var r=s.core.getSlideItem(s.core.index);if(s.$LG(e.target).hasClass("lg-item")||r.get().contains(e.target)){t=new Date,i=s.core.getSlideItem(s.core.index).find(".lg-img-wrap").first();var l=s.getDragAllowedAxises(Math.abs(s.rotateValue));c=l.allowY,g=l.allowX,s.core.outer.hasClass("lg-zoomed")&&s.$LG(e.target).hasClass("lg-object")&&(g||c)&&(e.preventDefault(),a=s.getDragCords(e,Math.abs(s.rotateValue)),o=s.getPossibleSwipeDragCords(s.rotateValue),n=!0,s.core.outer.get().scrollLeft+=1,s.core.outer.get().scrollLeft-=1,s.core.outer.removeClass("lg-grab").addClass("lg-grabbing lg-zoom-drag-transition lg-zoom-dragging"))}}})),this.$LG(window).on("mousemove.lg.zoom.global"+this.core.lgId,(function(t){if(n){l=!0,r=s.getDragCords(t,Math.abs(s.rotateValue));var e=s.getZoomSwipeCords(a,r,g,c,o);s.setZoomSwipeStyles(i,e)}})),this.$LG(window).on("mouseup.lg.zoom.global"+this.core.lgId,(function(o){if(n){if(e=new Date,n=!1,s.core.outer.removeClass("lg-zoom-dragging"),l&&(a.x!==r.x||a.y!==r.y)){r=s.getDragCords(o,Math.abs(s.rotateValue));var i=e.valueOf()-t.valueOf();s.touchendZoom(a,r,g,c,i,s.rotateValue)}l=!1}s.core.outer.removeClass("lg-grabbing").addClass("lg-grab")}))},h.prototype.closeGallery=function(){this.resetZoom()},h.prototype.destroy=function(){this.$LG(window).off(".lg.zoom.global"+this.core.lgId),this.core.LGel.off(".lg.zoom"),this.core.LGel.off(".zoom"),clearTimeout(this.zoomableTimeout),this.zoomableTimeout=!1},h}()}));
