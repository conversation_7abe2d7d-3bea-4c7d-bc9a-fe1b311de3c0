<?php

namespace App\Services;

use Illuminate\Support\Collection;
use Illuminate\Http\Request;

class LocalizationService
{
    /**
     * Replace fields dynamically based on Accept-Language header.
     */
    public function replaceFieldsForLanguage(Collection $data, Request $request, array $fieldsToReplace)
{
    $language = $request->header('Accept-Language', 'en');

    if ($language === 'ar') {
        return $data->map(function ($item) use ($fieldsToReplace) {
            foreach ($fieldsToReplace as $field => $localizedField) {
                if (!empty($item->$localizedField)) {
                    $item->$field = $item->$localizedField; // Replace with Arabic value if not empty
                }
                // unset($item->$localizedField); // Optional: Remove Arabic field after replacement
            }
            return $item;
        });
    }
    return $data;
  }
}