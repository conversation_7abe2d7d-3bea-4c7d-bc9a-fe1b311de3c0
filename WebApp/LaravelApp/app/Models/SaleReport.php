<?php

namespace App\Models;

use App\Models\Brand;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Cache;
class Refund implements FromCollection, WithMapping, WithHeadings
{
    public function collection()
    {
        $brands = Cache::remember('brands', 86400, function(){
            return Brand::select("*")->cursor();
        });
        return $brands;
    }

    public function headings(): array
    {
        return [
            'ID',
            'Name',
            'Image'
            
        ];
    }

    /**
    * @var Category $brands
    */
    public function map($brands): array
    {
        
       
        return [
            $brands->id,
            $brands->name,
            uploaded_asset($brands->logo) ,  
        ];
    }
    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                $event->sheet->getDelegate()
                    ->getStyle('A2:H2')
                    ->applyFromArray([ 'alignment' => ['wrapText' => true]]);
                    
            }
        ];
    }
}
