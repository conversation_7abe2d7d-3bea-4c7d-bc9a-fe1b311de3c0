<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class LanguageSwitcher
{
    public function handle(Request $request, Closure $next)
    {
        // Check if the lang parameter is set in the query string or header
        $lang = $request->get('lang', $request->header('Accept-Language', 'en'));
        
        // Set the locale
        App::setLocale($lang);
        
        return $next($request);
    }
}
