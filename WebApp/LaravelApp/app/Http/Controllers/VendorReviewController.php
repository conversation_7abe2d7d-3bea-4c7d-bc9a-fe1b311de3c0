<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;

class VendorReviewController extends Controller
{
    public function ViewVendorReviews(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor=Session::get('cityid');
			
	$vendor_id = Session::get('cityid');
	$reviews=DB::table('review')->select('review.*','users.id','users.name','users.image','users.created_at as date1')->join('users', 'users.id', '=','review.user_id')->where('vendor_id',$vendor)->get();
			
			
			return view('vendor.reviews',compact('reviews'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	  public function CommentVendorReview(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor=Session::get('cityid');
			
			$vendor_id = Session::get('cityid');
			$commentID=$request->id;
			$comments=$request->comments;
			$insert1 = DB::table('review')->where('vendor_id', $vendor_id)->where('id', $commentID)
                                ->update
    				(['comments'=>$comments]);
			
			
			return redirect()->back()->with('message', 'Comments Added Successfully');
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	public function EnableVendorReview(Request $request)
	{
		if(Session::has('cityvendor'))
		{
			$vendor_id = Session::get('cityid');
			$reviewID=$request->id;
			
			 $getfile=DB::table('review')
                ->where('id',$request->id)->where('vendor_id',$vendor_id)
                ->first();
	
       if($getfile->active==1)
	   {
		   $enable=0;
	   }
	    if($getfile->active==0)
	   {
		   $enable=1;
	   }

    	$update = DB::table('review')->where('id',$request->id)->where('vendor_id',$vendor_id)
                                  
                                ->update(['active'=>$enable]);
        if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Update' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull Enable'); 
        }
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	public function ReportVendorReview(Request $request)
	{
		if(Session::has('cityvendor'))
		{
		$report_ID=$request->id;
		$reviews=DB::table('reporreviews')->select('reporreviews.*','users.id','users.name','users.image')->join('users', 'users.id', '=','reporreviews.user_id')->where('review_id',$report_ID)->get();
			$html='<table id="example" border=1 class="display table-bordered table">
                                        <thead>
                                            <tr>
                                                <th></th>
                                               
                                                <th>USER NAME</th>
                                                <th>MESSAGE</th>
                                               
                                                
                                            </tr>
                                        </thead>
                                        <tbody>';
										$i=1;
			foreach($reviews as $review)
			{
				$img='';
				//if($review->image!='')
					//$img='<img class="rounded-circle" width="35" src="{{url($review->image)}}" alt="">';
				
				$html.='<tr><td>'.$i.'</td>
				<td>'.$review->name.'</td><td>'.$review->notes.'</td></tr>';
				$i++;
			}
			$html.="  </tbody>
                                    </table>";
									echo $html;
		}
	}	
	
}
