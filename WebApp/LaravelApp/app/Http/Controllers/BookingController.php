<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use PDF;
use Carbon\Carbon;
use Illuminate\Support\Str;

class BookingController extends Controller
{
    public function downloadPDF($bookingId)
    {
        // Retrieve booking details
        $booking =  DB::table('orders')->select('orders.*','vendor.*','areas.*','users.id as userid','users.user_phone','users.name','users.email','users.address','users.aboutme')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')
        ->join('users', 'orders.user_id', '=','users.id')->join('areas', 'vendor.area', '=','areas.Area_id')->where('orders.id',$bookingId)->first();
      $bookingdetails = DB::table('order_cart')->where('book_ID',$bookingId)->get();
        // Generate PDF content
        $pdf = PDF::loadView('admin.bookingpdf', compact('bookingdetails','booking'));

        // Return PDF as a response
        return $pdf->download("booking_{$bookingId}.pdf");
    }
		public function BookingList(Request $request)
		{
				if(Session::has('cityadmin'))
			{
				$governarates=DB::table('governarate')
                ->where('enabled',1)
                ->get();
				$html='<option value="">Choose Location</option>';
				foreach($governarates as $governarate)
				{
					$html.='<optgroup label="'.$governarate->Gov_Title.'">';
					$areas=DB::table('areas')
                ->where('enable',1)->where('Gove_ID', '=',$governarate->Gov_ID )
                ->get();
				foreach($areas as $area)
				{
					$html.='<option value="'.$area->Area_Title.'">'.$area->Area_Title.'</option>';
					
				}
				
				$html.='</optgroup>';	
				}
					$vendors=DB::table('vendor')
                ->where('enabled',1)
                ->get();
				$vendorhtml='<option value="">Filter by vendor</option>';
				foreach($vendors as $vendor)
				{
				
					$vendorhtml.='<option value="'.$vendor->vendor_name.'">'.$vendor->vendor_name.'</option>';
				}
				 $booking = DB::table('orders')->select('orders.*','vendor.*','areas.*','users.id as userid','users.user_phone','users.name','users.email','users.address','users.aboutme','orders.Bookid')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')->join('users', 'orders.user_id', '=','users.id')->join('areas', 'vendor.area', '=','areas.Area_id')->get();
				 return view('admin.bookinglist',compact('booking','html','vendorhtml'));
			}
			else 
			{
					return redirect()->route('login')->withErrors('please login first');
				
			}	
			
			
		}
		
		public function BookingCancelled(Request $request)
		{
				if(Session::has('cityadmin'))
			{
				$governarates=DB::table('governarate')
                ->where('enabled',1)
                ->get();
				$html='<option value="">Choose Location</option>';
				foreach($governarates as $governarate)
				{
					$html.='<optgroup label="'.$governarate->Gov_Title.'">';
					$areas=DB::table('areas')
                ->where('enable',1)->where('Gove_ID', '=',$governarate->Gov_ID )
                ->get();
				foreach($areas as $area)
				{
					$html.='<option value="'.$area->Area_Title.'">'.$area->Area_Title.'</option>';
					
				}
				
				$html.='</optgroup>';	
				}
				 $booking = DB::table('orders')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')->join('users', 'orders.user_id', '=','users.id')->join('areas', 'vendor.area', '=','areas.Area_id')->where('status', 4)->get();
				 return view('admin.cancelledbooking',compact('booking','html'));
			}
			else 
			{
					return redirect()->route('login')->withErrors('please login first');
				
			}	
			
			
		}
		public function BookingListDetails(Request $request)
		{
					if(Session::has('cityadmin'))
			{ 
				$bookingdetails = DB::table('order_cart')->where('book_ID',$request->id)->get();
				$booking = DB::table('orders')->select('orders.*','vendor.*','areas.*','users.id as userid','users.user_phone','users.name','users.email','users.address','users.aboutme')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')->join('users', 'orders.user_id', '=','users.id')->join('areas', 'vendor.area', '=','areas.Area_id')->where('orders.id',$request->id)->first();
				return view('admin.bookinglistdetail',compact('booking','bookingdetails'));
			}
			else
			{
			return redirect()->route('login')->withErrors('please login first');
			}
			
			
			
			
		}
		
		
		
		
		
		
		
		public function BookingStatus(Request $request)
		{
					if(Session::has('cityadmin'))
			{ 
		$status=$request->status;
		
				$update=DB::table('orders')->where('id', $request->id)
                                ->update
    				(['status'=>$status]);
					 if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Update' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }
			}
			else
			{
			return redirect()->route('login')->withErrors('please login first');
			}
			
			
			
			
		}
}
