<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Config;
use App\Mail\VendorMail;
use Mail;
class VendorController extends Controller
{
    
    
        public function metaPage($vendor_id)
    {
        $vendor = \DB::table('vendor')->where('vendor_id', $vendor_id)->first();
    
        if (!$vendor) {
            abort(404, 'Vendor not found');
        }
    
        return view('vendor.meta', ['vendor' => $vendor]);
    }
    
    
    public function addVendor(Request $request)
	{
		if(Session::has('cityadmin'))
		{
		 
    	     $servicecategory = DB::table('servicecategory')->orderBy('Ordering', 'ASC')->get();          
    	     $types = DB::table('vendortypes')->orderBy('orders', 'ASC')->get();
$ameneties = DB::table('ameneties')->get();
			 $governarates=DB::table('governarate')
                ->where('enabled',1)
                ->get();
				$html='<option value="">Choose Location</option>';
				foreach($governarates as $governarate)
				{
					$html.='<optgroup label="'.$governarate->Gov_Title.'">';
					$areas=DB::table('areas')
                ->where('enable',1)->where('Gove_ID', '=',$governarate->Gov_ID )
                ->get();
				foreach($areas as $area)
				{
					$html.='<option value="'.$area->Area_id.'">'.$area->Area_Title.'</option>';
				}
				$html.='</optgroup>';	
				}
		return view('admin.add-vendor',compact('types','html','ameneties','servicecategory'));
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
		}	
		
		
	}
	
	 public function saveVendor(Request $request)
	{
		if(Session::has('cityadmin'))
		{
		 
		 $comission=$request->comission;
		  $CatID=$request->CatID;
		 $name=$request->name;
		  $name_ar=$request->name_ar;
		 $email=$request->email;
		 $mobile=$request->mobile;
		 $type=$request->type;
		 $address=$request->address;
		 $address_ar=$request->address_ar;
		 $area=$request->area;
		 $block=$request->block;
		
		 $street=$request->street;
		  $street_ar=$request->street_ar;
		 $avenue=$request->avenue;
		 $avenue_ar=$request->avenue_ar;
		 $zipcode=$request->zipcode;
		 $instagram=$request->instagram;
		 $facebook=$request->facebook;
		  $YouTube=$request->YouTube;
		  $X=$request->X;
		  $LinkedIn=$request->LinkedIn;
		  $Snapchat=$request->Snapchat;
		  $Pininterest=$request->Pininterest;
		  $Wechat=$request->Wechat;
		  $Tiktok=$request->Tiktok;
		 $contractstartdate=$request->contractstartdate;
		 $contractenddate=$request->contractenddate;
		 $contractdescription=$request->contractdescription;
		  $days=$request->days;
		  $openingtime=$request->openingtime;
		  $closingtime=$request->closingtime;
		  $status=$request->status;
		  $lat=$request->lat;
		  $long=$request->long;
		  $description=$request->description;
		  $attatchmentsphoto=$request->attatchmentsphoto;
		  $attatchments=$request->attatchments;
		  $attatchmentsfeatured=$request->attatchmentsfeatured;
		  $ameneties=$request->ameneties;
		  $iban=$request->iban;
		  $cod=$request->cod;
		  $this->validate(
         $request,
         [		'description'=>'required',
         		'name'=>'required',
				'name_ar'=>'required',
         		'email'=>'required',
				'mobile'=>'required',
				'type'=>'required',
				'address'=>'required',
				'address_ar'=>'required',
				
				'block'=>'required',
				'area'=>'required',
				'street'=>'required',
				'street_ar'=>'required',
				'avenue'=>'required',
				'avenue_ar'=>'required',
				'ameneties.*'=>'required',
				'contractstartdate'=>'required',
				'contractenddate'=>'required',
				'description'=>'required',
				'contractdescription'=>'required',
				'days' => 'required|array|min:1',
				'openingtime'=>'required',
				'closingtime'=>'required',
				'status'=>'required',
				'lat'=>'required',
				'long'=>'required',
				'attatchmentsphoto'=>'required',
				'attatchments'=>'required',
				'attatchmentsfeatured'=>'required',
				'comission'=>'required',
				'CatID'=>'required',
				'iban'=>'required',
				'cod'=>'required',
				
         		
         ],[
            'lat.required' => 'Please Select Google Map',
            'long.required' => 'Please Select Google Map'
        ]

);
 $contractstartdate=date('y-m-d',strtotime($request->contractstartdate));
		 $contractenddate=date('y-m-d',strtotime($request->contractenddate));

$pass=Str::random(10);

$password=Hash::make($pass);

$date = date('d-m-Y');
	

$featuredimage=rtrim($attatchmentsfeatured,",");	
		$insert = DB::table('vendor')
    				->insertGetId(['vendor_name'=>$name,'vendor_name_ar'=>$name_ar,'vendor_email'=>$email,'vendor_phone'=>$mobile,'shop_type'=>$type,'vendor_loc'=>$address,'vendor_loc_ar'=>$address_ar,'area'=>$area,'block'=>$block,'street'=>$street,'street_ar'=>$street_ar,'avenue_ar'=>$avenue_ar,'zipcode'=>$zipcode,'instagram'=>$instagram,'facebook'=>$facebook,'YouTube'=>$YouTube,'X'=>$X,'LinkedIn'=>$LinkedIn,'Snapchat'=>$Snapchat,'Pininterest'=>$Pininterest,'Wechat'=>$Wechat,'Tiktok'=>$Tiktok,'contractstartdate'=>$contractstartdate,'contractenddate'=>$contractenddate,'contractdescription'=>$contractdescription,'opening_time'=>$openingtime,'closing_time'=>$closingtime,'pstatus'=>$status,'lat'=>$lat,'lng'=>
					$long,'vendor_logo'=>$featuredimage,'description'=>$description,'vendor_pass'=>$password,'comission'=>$comission,'CatID'=>$CatID,'iban'=>$iban,'cod'=>$cod]);
					 if($insert!=''){
					$attatchmentsphoto=rtrim($attatchmentsphoto,",");	 
		$photos=explode(",",$attatchmentsphoto);
            foreach($photos as $photo)
            {
                
				DB::table('vendor_photos')
    				->insert(['vendor_id'=>$insert,'photo'=>$photo]);
				
            }
      $attatchment=rtrim($attatchment,",");
		$attatchment=explode(",",$attatchments);
            foreach($attatchment as $contractattachment)
            {
                
				DB::table('vendor_attachment')
    				->insert(['Vendor_Id'=>$insert,'Vendor_attachment'=>$contractattachment]);
				
            }
       
		if ($request->days){
			 foreach($days as $key => $day)
            {
				DB::table('time_slot')
    				->insert(['open_hour'=>$openingtime,'close_hour'=>$closingtime,'days'=>$day,'vendor_id'=>$insert,'status'=>1]);
			}
			
		}
		if($ameneties)
		{
		 foreach($ameneties as $key => $amenety)
            {
				DB::table('vendor_ameneties')
    				->insert(['ameneties_id'=>$amenety,'vendor_id'=>$insert]);
			}	
		}
		
			$body = [
            'name'=>$name,
         
           'email'=>$email,
		   'password'=>$pass
		   
		   
        ];
 $app_name = "BookMe";
        $settings=DB::table('smtp_settings')
    	                   ->where('SMTP_ID',1)
    	                   ->first();
						   
						   $config = array(
        'driver'     => $settings->type,
        'host'       => $settings->mail_host,
        'port'       => $settings->mail_port,
        'from'       => array('address' =>$settings->mail_from_address, 'name' =>$settings->mail_from_name ),
        'encryption' => $settings->mail_encryption,
        'username'   => $settings->mail_username,
        'password'   => $settings->mail_password
      );
      Config::set('mail', $config);
        Mail::to($email)->send(new VendorMail($body));
		
        return redirect()->back()->with('message', 'Added Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }
    	    
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
		}	
		
		
	}
	
	 public function listVendors(Request $request)
	{
		if(Session::has('cityadmin'))
		{
		 
    	               
    	     $types = DB::table('vendortypes')->get();

			 $governarates=DB::table('governarate')
                ->where('enabled',1)
                ->get();
				$html='<option value="">Choose Location</option>';
				foreach($governarates as $governarate)
				{
					$html.='<optgroup label="'.$governarate->Gov_Title.'">';
					$areas=DB::table('areas')
                ->where('enable',1)->where('Gove_ID', '=',$governarate->Gov_ID )
                ->get();
				foreach($areas as $area)
				{
					$html.='<option value="'.$area->Area_Title.'">'.$area->Area_Title.'</option>';
					
				}
				
				$html.='</optgroup>';	
				}
				$vendors = DB::table('vendor')->join('areas', 'vendor.area', '=','areas.Area_id')->join('vendortypes', 'vendor.shop_type', '=','vendortypes.Vendor_Type')->get();
		return view('admin.all-vendors',compact('types','html','vendors'));
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
		}	
		
		
	}
	public function EditVendor(Request $request)
	{
		if(Session::has('cityadmin'))
		{
			$servicecategory = DB::table('servicecategory')->orderBy('Ordering', 'ASC')->get();
			  $types = DB::table('vendortypes')->orderBy('orders', 'ASC')->get();
$vendors = DB::table('vendor')->where('vendor_id',$request->id)->first();
$ameneties = DB::table('ameneties')->get();
$amenetiessaved1 = DB::table('vendor_ameneties')->select('ameneties_id')->where('vendor_id',$request->id)->get()->toArray();
		$amenetiessaved=array();
		foreach($amenetiessaved1 as $amenetiessaved2)
				{
				$amenetiessaved[]=$amenetiessaved2->ameneties_id;
				}					
			 $governarates=DB::table('governarate')
                ->where('enabled',1)
                ->get();
				$html='<option value="">Choose Location</option>';
				foreach($governarates as $governarate)
				{
					$html.='<optgroup label="'.$governarate->Gov_Title.'">';
					$areas=DB::table('areas')
                ->where('enable',1)->where('Gove_ID', '=',$governarate->Gov_ID )
                ->get();
				$selected="";
				foreach($areas as $area)
				{
					if($area->Area_id==$vendors->area) {$selected="selected";}
					$html.='<option value="'.$area->Area_id.'" '.$selected.'>'.$area->Area_Title.'</option>';
					
				$selected='';
				}
				$html.='</optgroup>';	
				}
				
				$vendor_photos = DB::table('vendor_photos')->where('vendor_id',$request->id)->get();
				$vendor_attachment = DB::table('vendor_attachment')->where('Vendor_Id',$request->id)->get();
				$time_slot = DB::table('time_slot')->where('vendor_id',$request->id)->get();
		return view('admin.edit-vendor',compact('types','html','vendor_photos','vendor_attachment','time_slot','vendors','ameneties','amenetiessaved','servicecategory'));
			
			
			
			
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}	
		
		
		
		
	}


		 public function saveEditVendor(Request $request)
	{
		if(Session::has('cityadmin'))
		{
			$comission=$request->comission;
		  $CatID=$request->CatID;
		 $name=$request->name;
		 $name_ar=$request->name_ar;
		 $email=$request->email;
		 $mobile=$request->mobile;
		 $type=$request->type;
		 $area=$request->area;
		 $block=$request->block;
		 $address=$request->address;
		 $street=$request->street;
		 $avenue=$request->avenue;
		 $address_ar=$request->address_ar;
		 $street_ar=$request->street_ar;
		 $avenue_ar=$request->avenue_ar;
		 $zipcode=$request->zipcode;
		 $instagram=$request->instagram;
		 $facebook=$request->facebook;
		 $YouTube=$request->YouTube;
		  $X=$request->X;
		  $LinkedIn=$request->LinkedIn;
		  $Snapchat=$request->Snapchat;
		  $Pininterest=$request->Pininterest;
		  $Wechat=$request->Wechat;
		  $Tiktok=$request->Tiktok;
		 $contractstartdate=$request->contractstartdate;
		 $contractenddate=$request->contractenddate;
		 $contractdescription=$request->contractdescription;
		  $days=$request->days;
		  $openingtime=$request->openingtime;
		  $closingtime=$request->closingtime;
		  $status=$request->status;
		  $lat=$request->lat;
		  $long=$request->long;
		  $description=$request->description;
		  $attatchmentsphoto=$request->attatchmentsphoto;
		  $attatchments=$request->attatchments;
		  $attatchmentsfeatured=$request->attatchmentsfeatured;
		  $ameneties=$request->ameneties;
		  $iban=$request->iban;
		  $cod=$request->cod;
		  $this->validate(
         $request,
         [		'description'=>'required',
         		'name'=>'required',
				'name_ar'=>'required',
         		'email'=>'required',
				'mobile'=>'required',
				'type'=>'required',
				'area'=>'required',
				'block'=>'required',
				
				'street'=>'required',
				'avenue'=>'required',
			
				'address'=>'required',
				'street_ar'=>'required',
				'avenue_ar'=>'required',
			
				'address_ar'=>'required',
				'contractstartdate'=>'required',
				'contractenddate'=>'required',
				'description'=>'required',
				'contractdescription'=>'required',
				'days' => 'required|array|min:1',
				'openingtime'=>'required',
				'closingtime'=>'required',
				'status'=>'required',
				'lat'=>'required',
				'long'=>'required',
				'comission'=>'required',
				'CatID'=>'required',
				'iban'=>'required',
				'cod'=>'required',
				
				'ameneties.*'=>'required',
         		
         ],[
            'lat.required' => 'Please Select Google Map',
            'long.required' => 'Please Select Google Map'
        ]

);
 $contractstartdate=date('y-m-d',strtotime($request->contractstartdate));
		 $contractenddate=date('y-m-d',strtotime($request->contractenddate));
$password=Hash::make(Str::random(10));

$date = date('d-m-Y');
$featuredimage=rtrim($attatchmentsfeatured,",");
	if($featuredimage!=''){
           
			DB::table('vendor')->where('vendor_id', $request->id)
                                ->update
    				(['vendor_logo'=>$featuredimage]);
        }
		
		$insert = DB::table('vendor')->where('vendor_id', $request->id)
                                ->update
    				(['vendor_name'=>$name,'vendor_name_ar'=>$name_ar,'vendor_email'=>$email,'vendor_phone'=>$mobile,'shop_type'=>$type,'vendor_loc'=>$address,'vendor_loc_ar'=>$address_ar,'area'=>$area,'block'=>$block,'street'=>$street,'street_ar'=>$street_ar,'avenue'=>$avenue,'avenue_ar'=>$avenue_ar,'zipcode'=>$zipcode,'instagram'=>$instagram,'facebook'=>$facebook,'YouTube'=>$YouTube,'X'=>$X,'LinkedIn'=>$LinkedIn,'Snapchat'=>$Snapchat,'Pininterest'=>$Pininterest,'Wechat'=>$Wechat,'Tiktok'=>$Tiktok,'contractstartdate'=>$contractstartdate,'contractenddate'=>$contractenddate,'contractdescription'=>$contractdescription,'opening_time'=>$openingtime,'closing_time'=>$closingtime,'pstatus'=>$status,'lat'=>$lat,'lng'=>
					$long,'description'=>$description,'comission'=>$comission,'CatID'=>$CatID,'iban'=>$iban,'cod'=>$cod]);
					$attatchmentsphoto=rtrim($attatchmentsphoto,","); 
		$photos=explode(",",rtrim($attatchmentsphoto,","));
		
            foreach($photos as $photo)
            {
                
				DB::table('vendor_photos')
    				->insert(['vendor_id'=>$request->id,'photo'=>$photo]);
				
            }
       $attatchments=rtrim($attatchments,","); 
		$attatchment=explode(",",rtrim($attatchments,","));
		
            foreach($attatchment as $contractattachment)
            {
                
				DB::table('vendor_attachment')
    				->insert(['Vendor_Id'=>$request->id,'Vendor_attachment'=>$contractattachment]);
				
            }
		
		if ($request->days){
			 foreach($days as $key => $day)
            {
				$time_slot = DB::table('time_slot')->where('vendor_id',$request->id)->where('days',$day)->first();
				
				DB::table('time_slot')
    				->where('vendor_id', $request->id)->where('days',$day)
                                ->update(['open_hour'=>$openingtime,'close_hour'=>$closingtime,'days'=>$day,'status'=>1]);
			}
			
		}
		if($ameneties)
		{
			$delete=DB::table('vendor_ameneties')->where('vendor_id',$request->id)->delete();
		 foreach($ameneties as $key => $amenety)
            {
				DB::table('vendor_ameneties')
    				->insert(['ameneties_id'=>$amenety,'vendor_id'=>$request->id]);
			}	
		}
        return redirect()->back()->with('message', 'Updated Successfully');
    
		
    	    
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
		}	
		
		
	}
	
	
	 public function DeletePhoto(Request $request)
	{
		if(Session::has('cityadmin'))
		{
			$delete=DB::table('vendor_photos')->where('Vendor_photoID',$request->id)->delete();
        if($delete)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Delete' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}	
		
		
	}
	
	 public function DeleteAttachment(Request $request)
	{
		if(Session::has('cityadmin'))
		{
			$delete=DB::table('vendor_attachment')->where('Vendor_AttachmentID',$request->id)->delete();
        if($delete)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Delete' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}	
		
		
	}
	
	public function EnableVendor(Request $request)
	{
	 if(Session::has('cityadmin'))
     {   
    
        $id=$request->id;

        $getfile=DB::table('vendor')
                ->where('vendor_id',$request->id)
                ->first();
	
       if($getfile->enabled==1)
	   {
		   $enable=0;
	   }
	    if($getfile->enabled==0)
	   {
		   $enable=1;
	   }

    	$update = DB::table('vendor')
                                ->where('vendor_id', $request->id)
                                ->update(['enabled'=>$enable]);
        if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Enable' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull Enable'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }	
		
		
		
		
	}
		public function ViewVendor(Request $request)
		{
			
			if(Session::has('cityadmin'))
		{
			
			 $types = DB::table('vendortypes')->get();
			 $vendors = DB::table('vendor')->join('areas', 'vendor.area', '=','areas.Area_id')->join('vendortypes', 'vendor.shop_type', '=','vendortypes.Vendor_Type')->where('vendor.vendor_id',$request->id)->first();
			
				
				$vendor_photos = DB::table('vendor_photos')->where('vendor_id',$request->id)->get();
				$vendor_attachment = DB::table('vendor_attachment')->where('Vendor_Id',$request->id)->get();
				$time_slot = DB::table('time_slot')->where('vendor_id',$request->id)->get();
				$reviews = DB::table('review')->join('users', 'review.user_id', '=','users.id')->where('review.vendor_id',$request->id)->get();
				
				
				
		  
            
 $orders1 = DB::table('orders')->select(
            DB::raw('sum(total_price) as sums'), 
            DB::raw("DATE_FORMAT(service_date,'%m') as monthKey")
  )
  ->where('vendor_id',$request->id)
  ->whereYear('service_date', date('Y'))
  ->groupBy('service_date')
  ->orderBy('service_date', 'desc')
  ->get();
$ordertotal = [0,0,0,0,0,0,0,0,0,0,0,0];

foreach($orders1 as $order){
    $ordertotal[$order->monthKey-1] = $order->sums;
}  
$max=max($ordertotal);
$totalSales=implode(",",$ordertotal);
		
				 
				
		return view('admin.view-vendor',compact('types','vendor_photos','vendor_attachment','time_slot','vendors','reviews', 'totalSales'));
			
			
			
			
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}	
			
			
		}




		public function EditVendorContract(Request $request)
		{
				if(Session::has('cityadmin'))
				{
					$contractstartdate=$request->contractstartdate;
					$contractenddate=$request->contractenddate;
					$contractdescription=$request->contractdescription;
					 $this->validate(
         $request,
         [		
				
				'contractstartdate'=>'required',
				'contractenddate'=>'required',
				
				'contractdescription'=>'required',
				
				
				'contractattachment.*'=>'mimes:jpeg,jpg,png',
				
         		
         ]

);
		$contractstartdate=date('y-m-d',strtotime($request->contractstartdate));
		 $contractenddate=date('y-m-d',strtotime($request->contractenddate));
		 if ($request->file('contractattachment')){
            foreach($request->file('contractattachment') as $key => $file)
            {
                $fileName = time().rand(1,99).'.'.$file->extension();  
                $file->move('vendor/images/'.$date.'/', $fileName);
                $contractattachment='vendor/images/'.$date.'/'.$fileName;
				DB::table('vendor_attachment')
    				->insert(['Vendor_Id'=>$request->vendorid,'Vendor_attachment'=>$contractattachment]);
				
            }
        }

$insert = DB::table('vendor')->where('vendor_id', $request->vendorid)
                                ->update
    				(['contractstartdate'=>$contractstartdate,'contractenddate'=>$contractenddate,'contractdescription'=>$contractdescription]);
return redirect()->back()->with('message', 'Updated Successfully');
				}
				else
				{
				return redirect()->route('login')->withErrors('please login first');
				}	
			
			
			
			
			
		}
		
		 public function vendorsecretlogin(Request $request)
    {
        $id=$request->id;
        $checkcityadminLogin = DB::table('vendor')
    	                   ->where('vendor_id',$id)
    	                   ->first();

    	if($checkcityadminLogin){
 session::put('cityid',$checkcityadminLogin->vendor_id);
           session::put('cityvendor',$checkcityadminLogin->vendor_email);
		    session::put('admin_name',$checkcityadminLogin->vendor_name);
		   session::put('admin_image',$checkcityadminLogin->vendor_logo);
		   session::put('admin_phone',$checkcityadminLogin->vendor_phone);
		   
		   
		    session::put('dashboard',$checkcityadminLogin->dashboard);
		   session::put('calendar',$checkcityadminLogin->calendar);
		   session::put('booking',$checkcityadminLogin->booking);
		   session::put('addbooking',$checkcityadminLogin->addbooking);
		   session::put('clientlist',$checkcityadminLogin->clientlist);
		   session::put('addclient',$checkcityadminLogin->addclient);
		   session::put('editclient',$checkcityadminLogin->editclient);
		   session::put('deleteclient',$checkcityadminLogin->deleteclient);
		    session::put('viewclient',$checkcityadminLogin->viewclient);
		   session::put('employeelist',$checkcityadminLogin->employeelist);
		   session::put('addemployee',$checkcityadminLogin->addemployee);
		   session::put('editemployee',$checkcityadminLogin->editemployee);
		   session::put('viewemployee',$checkcityadminLogin->viewemployee);
		   session::put('serviceslist',$checkcityadminLogin->serviceslist);
		   session::put('addservice',$checkcityadminLogin->addservice);
		   session::put('serviceedit',$checkcityadminLogin->serviceedit);
		   session::put('servicedelete',$checkcityadminLogin->servicedelete);
		   session::put('reviewslist',$checkcityadminLogin->reviewslist);
		   session::put('viewreviews',$checkcityadminLogin->viewreviews);
		    session::put('settings',$checkcityadminLogin->settings);
		   
          
           return redirect()->route('vendorDashboard');

    	}else
         {
         	return redirect()->route('cityadmin')->withErrors('Something Wents Wrong');
         }
    }
	
	 public function vendorpermission(Request $request)
    {
        $id=$request->id;
        $vendor = DB::table('vendor')
    	                   ->where('vendor_id',$id)
    	                   ->first();

    	if($vendor){
 return view('admin.seller-permission',compact('vendor'));

    	}else
         {
         	return redirect()->route('cityadmin')->withErrors('Something Went Wrong');
         }
    }
	
	public function PermissionUpdateVendor(Request $request)
	{
	 if(Session::has('cityadmin'))
     {   
    
        $id=$request->id;
		$field=$request->field;
        $getfile=DB::table('vendor')
                ->where('vendor_id',$request->id)
                ->first();
	
       if($getfile->$field==1)
	   {
		   $enable=0;
	   }
	    if($getfile->$field==0)
	   {
		   $enable=1;
	   }

    	$update = DB::table('vendor')
                                ->where('vendor_id', $request->id)
                                ->update([$field=>$enable]);
        if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Enable' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull Enable'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }	
		
		
		
		
	}
	
	
	 public function vendoradminmanage(Request $request)
    {
        $id=$request->id;
        $vendorservices = DB::table('vendor_services')->join('vendor', 'vendor_services.vendor_id', '=','vendor.vendor_id')
    	                   ->where('vendor_services.vendor_id',$id)
    	                   ->get();

    	if($vendorservices){
 return view('admin.vendor-adminmanage',compact('vendorservices'));

    	}else
         {
         	return redirect()->route('cityadmin')->withErrors('Something Went Wrong');
         }
    }
	
	
	public function UpdateVendorCommision(Request $request)
	{
	 if(Session::has('cityadmin'))
     {   
    
        $id=$request->id;
		$vendor_id=$request->vendor;
        $commision=$request->commision;

    	$update = DB::table('vendor_services')
                                ->where('vendor_id', $vendor_id)->where('ServicesID', $id)
                                ->update(['commision'=>$commision]);
        if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Enable' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull Enable'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }	
		
		
		
		
	}
	
	public function PopularArtist(Request $request)
	{
	 if(Session::has('cityadmin'))
		{   
			$vendorslist = DB::table('vendor')->get();
			$vendors = DB::table('staff_profile')->join('vendor', 'staff_profile.vendor_id', '=','vendor.vendor_id')->get();
			return view('admin.popular-artist',compact('vendorslist','vendors'));


		}
	}
	public function PopularArtistEdit(Request $request)
	{
	 if(Session::has('cityadmin'))
		{   
			$id=$request->id;
			$staff = DB::table('staff_profile')->join('vendor', 'staff_profile.vendor_id', '=','vendor.vendor_id')->where('staff_id',$id)->get();
			
			return view('admin.popular-artistedit',compact('staff'));


		}
	}
	public function PopularArtistEditUpdate(Request $request)
	{
	 if(Session::has('cityadmin'))
		{   
			$id=$request->id;
			$position=$request->position;
			$update = DB::table('staff_profile')
                                ->where('staff_id', $id)
                                ->update(['position'=>$position]);
								 return redirect()->route('PopularArtist');
			

		}
	}
	public function PopularArtistAjax(Request $request)
	{
	 if(Session::has('cityadmin'))
		{ 
			$id=$request->id;
			 $getfile=DB::table('staff_profile')
                ->where('staff_id',$request->id)
                ->first();
	
       if($getfile->popular==1)
	   {
		   $enable=0;
	   }
	    if($getfile->popular==0)
	   {
		   $enable=1;
	   }

    	$update = DB::table('staff_profile')
                                ->where('staff_id', $request->id)
                                ->update(['popular'=>$enable]);
        if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Enable' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull Enable'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }	
		}

		
	
}
