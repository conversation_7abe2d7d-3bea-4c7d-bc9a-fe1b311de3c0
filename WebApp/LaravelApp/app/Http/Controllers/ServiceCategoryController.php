<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;

class ServiceCategoryController extends Controller
{
    public function addServiceCategory(Request $request)
	
	{
		$created_at = Carbon::Now();
    if(Session::has('cityadmin'))
		{
		 $parentcategories = DB::table('servicecategory')->get();
    	                  
    	                   
		return view('admin.add-service-category',compact('parentcategories'));
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
	
		}
	}
	
	 public function ServiceCategorySave(Request $request)
	
	{
		$created_at = Carbon::Now();
   if(Session::has('cityadmin'))
		{
		$servicename=$request->servicename;
		$servicename_ar=$request->servicename_ar;
		$Parent=$request->Parent;
		$ordering=$request->ordering;
		$Icon=$request->Icon;
			$this->validate($request,[
				'servicename_ar'=>'required',
         		'servicename'=>'required',
				'ordering'=>'required',
				'Icon' => 'required',
         		
         		
         ]

	);
	$date = date('d-m-Y');
	if($request->Icon){
           $Icon=rtrim($Icon,",");
        }
		
		
			$insert = DB::table('servicecategory')
    				->insert(['Cat_Name'=>$servicename,'Cat_Name_ar'=>$servicename_ar,'Icon'=>$Icon,'Parent'=>$Parent,'Ordering'=>$ordering]);
					 if($insert){

        return redirect()->back()->with('message', 'Added Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }
	}
	}
	
	public function ServiceCategory(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$categories = DB::table('servicecategory')->select('servicecategory.*', 'qgt.Cat_Name as parent_name')

->leftjoin('servicecategory as qgt', 'qgt.Cat_ID', '=', 'servicecategory.Parent')

->get();
    	                   
    	                   
		return view('admin.service-categories',compact('categories'));	
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	
	public function EditServiceCategory(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$categories = DB::table('servicecategory')->where('Cat_ID',$request->id)->first();
    	                   
			$parentcategories = DB::table('servicecategory')->get();
    	                  
    	                   
		return view('admin.edit-service-category',compact('parentcategories','categories'));	                   
		
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	
	public function UpdateServiceCategory(Request $request)
	
	{
		$created_at = Carbon::Now();
   if(Session::has('cityadmin'))
		{
			$servicename_ar=$request->servicename_ar;
		$servicename=$request->servicename;
		$Parent=$request->Parent;
		$ordering=$request->ordering;
		$Icon=$request->Icon;
			$this->validate($request,[
         		'servicename'=>'required',
				'servicename_ar'=>'required',
				'ordering'=>'required',
				
         		
         		
         ]

	);
	$date = date('d-m-Y');
	if($request->Icon){
          
           $Icon=rtrim($Icon,",");
      
			$update = DB::table('servicecategory')
                                ->where('Cat_ID', $request->id)
                                ->update(['Icon'=>$Icon]);
        }
		
		
			$update = DB::table('servicecategory')
                                ->where('Cat_ID', $request->id)
                                ->update(['Cat_Name'=>$servicename,'Cat_Name_ar'=>$servicename_ar,'Parent'=>$Parent,'ordering'=>$ordering]);
					 if($update){

        return redirect()->back()->with('message', 'Updated Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }
	}
	}
	
	
	
	public function DeleteServiceCategory(Request $request)
	{
		
		 if(Session::has('cityadmin'))
     {   
    
       

        $getfile=DB::table('servicecategory')
                ->where('Cat_ID',$request->id)
                ->first();

       

    	$delete=DB::table('servicecategory')->where('Cat_ID',$request->id)->delete();
        if($delete)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Delete' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }
		
		
	}
	
}
