<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;
use Twilio\Rest\Client;
class SendSmsController extends Controller
{
    public function SendSms()
	{
	if(Session::has('cityadmin'))
		{
		 $users = DB::table('users')->get();
    	               
    	                  
		return view('admin.send-sms',compact('users'));
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
		}	
	}
	
	
	
	public function SendSmsSend(Request $request)
	{
	if(Session::has('cityadmin'))
		{
		 
			$users=$request->users;
			
			$subject=$request->subject;
			$message=$request->message;
			

    	$this->validate(
         $request,
         [
         		'users'=>'required',
         		'subject'=>'required',
				'message'=>'required',
				
         		
         		
         ]

);
if($users!='ALL')
{
 $user = DB::table('users')
    	                   ->where('id',$users)
    	                   ->first();

	$phone1=$user->user_phone;
	$phone2=$user->email;
	$twilsid = "**********************************";  
	$twiltoken = "2fd046d0426a7127df23bb0c8a68145d"; 
	$twilphone = "+***********"; 
	  // send SMS
	 // Your Account SID and Auth Token from twilio.com/console
	 $sid = $twilsid;
	 $token = $twiltoken;
	 $client = new Client($sid, $token);

	 $user = "+965".$phone1;
	 // Use the client to do fun stuff like send text messages!
	  if($phone1!='')
	  {
	 $client->messages->create(
		 // the number you'd like to send the message to
		 $user,
		 array(
			 // A Twilio phone number you purchased at twilio.com/console
			 'from' => $twilphone,
			 // the body of the text message you'd like to send
			 'body' => $subject."\n".$message
			
		 )
	 );
	  }
	 	 $user = "+965".$phone2;
	 	 if(is_numeric($phone2))
	 	 {
	 $client->messages->create(
		// the number you'd like to send the message to
		$user,
		array(
			// A Twilio phone number you purchased at twilio.com/console
			'from' => $twilphone,
			// the body of the text message you'd like to send
			'body' => $subject."\n".$message
		   
		)
	);
	 	 }
	}
	else
	{
		$users = DB::table('users')
    	                   ->get();
    	                   
foreach($users as $user)
{
	$phone1=$user->user_phone;
	$phone2=$user->email;
	$twilsid = "**********************************";  
	$twiltoken = "2fd046d0426a7127df23bb0c8a68145d"; 
	$twilphone = "+***********"; 
	  // send SMS
	 // Your Account SID and Auth Token from twilio.com/console
	 $sid = $twilsid;
	 $token = $twiltoken;
	 $client = new Client($sid, $token);
	 $user = "+965".$phone1;
	 // Use the client to do fun stuff like send text messages!
	 if($phone1!='')
	 {
	 $client->messages->create(
		 // the number you'd like to send the message to
		 $user,
		 array(
			 // A Twilio phone number you purchased at twilio.com/console
			 'from' => $twilphone,
			 // the body of the text message you'd like to send
			 'body' => $subject."\n".$message
			
		 )
	 );
}
	  if(is_numeric($phone2))
	 	 {
	 	      $user = "+965".$phone2;
	 $client->messages->create(
		// the number you'd like to send the message to
		$user,
		array(
			// A Twilio phone number you purchased at twilio.com/console
			'from' => $twilphone,
			// the body of the text message you'd like to send
			'body' => $subject."\n".$message
		   
		)
	);
	 	 }

	}
}
		return redirect()->back()->with('message', 'SMS Sent Successfully');
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
		}	
	}
	
}
