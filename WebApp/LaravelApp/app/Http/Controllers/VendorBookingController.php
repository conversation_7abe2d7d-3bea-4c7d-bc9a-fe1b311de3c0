<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;
use Illuminate\Support\Str;
use DateTime;

class VendorBookingController extends Controller
{
	
	public function fetch()
{
	if(Session::has('cityvendor'))
		{
			$vendor_id = Session::get('cityid');
    $notifications =DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->where('user_id',180)->where('readed',0)->join('users', 'orders.user_id', '=','users.id')->orderBy('orders.id', 'desc')
    	                   ->get();
    return response()->json($notifications);
		}
}

public function markAsRead(Request $request)
{
	$vendor = Session::get('cityid');
    DB::table('orders')->whereIn('id', $request->ids)->where('vendor_id',$vendor)
                                ->update(['readed'=>1]);
    				
    return response()->json(['status' => 'success']);
}
    public function vendorBooking(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor_id = Session::get('cityid');
			$vendor = Session::get('cityid');
			$booking = DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->join('users', 'orders.user_id', '=','users.id')->orderBy('orders.id', 'desc')
    	                   ->get();
						   //$staffs=DB::table('staff_profile')->where('vendor_id',$vendor)->get();
			$users1 = DB::table('users')->distinct()->select('name','users.id','orders.id as oid')
    	                   ->where('orders.vendor_id',$vendor_id)->join('orders', 'users.id', '=','orders.user_id')->orderBy('oid', 'desc')
    	                   ->get();
							$user2=DB::table('users')->distinct()->select('name','users.id')
    	                   ->where('vendorid',$vendor_id)->orderBy('id', 'desc')
    	                   ->get();
						  
				 $employees=DB::table('staff_profile')
               ->where('vendor_id',$vendor_id)
                ->get();		   
			$vendor_services = DB::table('vendor_services')
    	                   ->where('vendor_id',$vendor_id)->orderBy('Name', 'asc')
    	                   ->get();		   
						  

$collection = collect($users1);
    $merged     = $collection->merge($user2)->unique('*.id');
    $users   = $merged->all();
				
			return view('vendor.bookinglist',compact('booking','employees','users','vendor_services'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	public function vendorBreaktime(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor_id = Session::get('cityid');
			$vendor = Session::get('cityid');
			$booking = DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->where('user_id',180)->join('users', 'orders.user_id', '=','users.id')->orderBy('orders.id', 'desc')
    	                   ->get();
						   //$staffs=DB::table('staff_profile')->where('vendor_id',$vendor)->get();
			/*
			$users1 = DB::table('users')->distinct()->select('name','users.id','orders.id as oid')
    	                   ->where('orders.vendor_id',$vendor_id)->join('orders', 'users.id', '=','orders.user_id')->orderBy('oid', 'desc')
    	                   ->get();
							$user2=DB::table('users')->distinct()->select('name','users.id')
    	                   ->where('vendorid',$vendor_id)->orderBy('id', 'desc')
    	                   ->get();
						  
				 $employees=DB::table('staff_profile')
               ->where('vendor_id',$vendor_id)
                ->get();		   
			$vendor_services = DB::table('vendor_services')
    	                   ->where('vendor_id',$vendor_id)->orderBy('Name', 'asc')
    	                   ->get();		   
						  

$collection = collect($users1);
    $merged     = $collection->merge($user2)->unique('*.id');
    $users   = $merged->all();
	*/
				
			return view('vendor.bookingbreak',compact('booking'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	public function ViewVendorBooking(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor=Session::get('cityid');
			
	$vendor_id = Session::get('cityid');
	$staffs=DB::table('staff_profile')->where('vendor_id',$vendor)->get();
			$bookingdetails = DB::table('order_cart')->where('book_ID',$request->id)->where('vendor_id',$vendor)->get();
			$bookings = DB::table('orders')->select('orders.*','vendor.*','areas.*','users.id as userid','users.user_phone','users.name','users.email','users.address','users.aboutme','orders.Bookid')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')->join('users', 'orders.user_id', '=','users.id')->join('areas', 'vendor.area', '=','areas.Area_id')->where('orders.id',$request->id)->where('orders.vendor_id',$vendor)->first();
			
			return view('vendor.bookinglistdetail',compact('bookings','bookingdetails','staffs'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	public function ChangeStaff(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor=Session::get('cityid');
			$id=$request->id;
			$staffid=$request->staffid;
				$update=DB::table('order_cart')->where('order_cart_id', $id)->where('vendor_id',$vendor)
                                ->update
    				(['staff_id'=>$staffid]);
					 if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Update' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }
		
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	
	public function ChangeStatus(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor=Session::get('cityid');
			$id=$request->id;
			$status=$request->status;
				$update=DB::table('orders')->where('id', $id)->where('vendor_id',$vendor)
                                ->update
    				(['status'=>$status]);
					$update=DB::table('order_cart')->where('book_ID', $id)->where('vendor_id',$vendor)
                                ->update
    				(['status'=>$status]);
					 if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Update' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }
		
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	/*
	public function Showtimeslots(Request $request)
	{
		if(Session::has('cityvendor'))
		{
		
		
		}
		
	}
	*/
	public function SaveBookingSlot(Request $request)
	
	{
		if(Session::has('cityvendor'))
		{
	$staff=$request->staff;
	$vendor=Session::get('cityid');
	$vendor_id=	Session::get('cityid');
	$date=$request->date;
	$serviceat=$request->serviceat;
	$client=$request->clients;
	$services=$request->services;
	$time1=$request->time;
	$paymentstatus=$request->status;
	$paymentmethod=$request->paymentmethod;
	
	$this->validate($request,[
         		'staff'=>'required',
				'date'=>'required',
				'serviceat'=>'required',
				'clients'=>'required',
				'services.*'=>'required',
				'time'=>'required',
				'status'=>'required',
				'paymentmethod'=>'required']

	);
	
	$datecreated = date('d-m-Y');
	
	$date=date('Y-m-d',strtotime($date));
	
	$day = date('l', strtotime($date));
		
		$time=DB::table('employee_time_slot')->where('vendor_id',$vendor)->where('staff_id',$staff)->where('days',$day)->first();
		
		$openhour=$time->open_hour;
		$closehour=$time->close_hour;
		
		if($openhour!='' and $closehour!='')
		{
		$slots=$this->getTimeSlot(15,$openhour,$closehour);
		}
		else
			
		{
		$slots="No Slots";
				
		}
		$bookings=DB::table('order_cart')->join('orders', 'orders.id', '=','order_cart.book_ID')
                ->where('staff_id',$request->id)->where('order_cart.vendor_id',$vendor_id)
                ->get();
				$slotnew=[];
				foreach($bookings as $booking)
				{
					$times=$booking->service_time;
					$timesslots=explode("-",$times);
					$timesslot1=$timesslots[0];
					$timesslot2=$timesslots[1];
					$timesslot1=date("h:i A", strtotime($timesslot1));
					$timesslot2=date("h:i A", strtotime($timesslot2));
					$slotnew=$this->getTimeSlot(30,$timesslot1,$timesslot2);
				}
				$totaltime=0;
				$pricetotal=0;
				foreach($slots as $slot)
				{
				 if (false !== $key = array_search($slot, $slotnew)) {
					 unset($slots[$key]);	
					}
				}
			$key = array_search($time1, $slots);
if ($key !== false) {
   
foreach($services as $service)
					{
						
					$price=DB::table('vendor_services')
                ->where('vendor_id',$vendor_id)->where('ServicesID',$service)
                ->first();
				$totaltime=$totaltime+$price->Service_Time;
				$pricetotal=$pricetotal+$price->Service_Price;
					}
					$endtime = date('h:i A',strtotime('+'.$totaltime.' minutes',strtotime($time1)));
					$slotnew1=$this->getTimeSlot(30,$time1,$endtime);
					
					$count=count($services);
					
					$totime=end($slotnew1);
					$totime=date("h:i A",strtotime($totime));
					$fromtime=$time1;
					$fromtime=date("h:i A",strtotime($fromtime));
					$newtime=$fromtime."-".$totime;
					$bookID="BOOK".rand().time();
					//$pricetotal=0;
					
					$mobile=DB::table('users')
                ->where('id',$client)
                ->first();
			
					$bookdate=date('Y-m-d');
					$bookdate1=date('Y-m-d h:i:s');
					if($client==180)
					{
					$pricetotal=0;	
					}
					$insert = DB::table('orders')
    				->insertGetId(['user_id'=>$client,'vendor_id'=> $vendor_id,'Bookid'=>$bookID,'total_price'=>$pricetotal,'service_date'=>$date,'service_time'=>$newtime,'payment_method'=>$paymentmethod,'payment_status'=>$paymentstatus,'status'=>1,'mobile'=>$mobile->user_phone,'payment_gateway'=>$paymentmethod,'bookdate'=>$bookdate,'created_at'=>$bookdate1,'updated_at'=>$bookdate1,'Bookingat'=>$serviceat]);
					if($insert!='')
					{
						foreach($services as $key=>$service)
					{
						$newqueryservice=DB::table('vendor_services')
                ->where('vendor_id',$vendor_id)->where('ServicesID',$service)
                ->first();
				if($key==0)
				{
				$new_datetime=$date." ".$time1;
        		$newtime=date('Y-m-d H:i:s',strtotime($new_datetime));
				}
				else {
					//$new_datetime=$newtime;
        		$newtime=date('Y-m-d H:i:s',strtotime('+'.$newqueryservice->Service_Time.' minutes', strtotime($newtime)));
				}
				if($client==180)
				{
				$insert1 = DB::table('order_cart')
    				->insert(['status'=>1,'service_name'=>$newqueryservice->Name,'service_id'=>$newqueryservice->ServicesID,'price'=>0,'user_id'=>$client,'vendor_id'=>$vendor_id,'staff_id'=>$staff,'book_ID'=>$insert,'start_date'=>$newtime,'duration'=>$newqueryservice->Service_Time,'sequence'=>$key]);	
				}
				else
				{
				$insert1 = DB::table('order_cart')
    				->insert(['status'=>1,'service_name'=>$newqueryservice->Name,'service_id'=>$newqueryservice->ServicesID,'price'=>$newqueryservice->Service_Price,'user_id'=>$client,'vendor_id'=>$vendor_id,'staff_id'=>$staff,'book_ID'=>$insert,'start_date'=>$newtime,'duration'=>$newqueryservice->Service_Time,'sequence'=>$key]);	
				}
				
						//$insert1 = DB::table('order_cart')
    				//->insert(['service_name'=>$newqueryservice->Name,'service_id'=>$newqueryservice->ServicesID,'price'=>$newqueryservice->Service_Price,'user_id'=>$client,'vendor_id'=>$vendor_id,'staff_id'=>$staff,'book_ID'=>$insert]);	
					}
					}
					
					return redirect()->back()->with('message', 'Appointment Added Successfully');
				}
				else
				{
				  return redirect()->back()->withErrors('Already Booked Same date and Time'); 	
				}
				
				
		
	}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}

		
	}
	
	public function getTimeSlot($interval, $start_time, $end_time)
{
   
    $i=0;
    $time = [];
	$add_mins  = $interval*60;
	 $array_of_time = array ();
	 
	 $start_time=strtotime($start_time);
	 $end_time=strtotime($end_time);
	
	while ($start_time <= $end_time) // loop between time
    {
       $array_of_time[] = date ("h:i A", $start_time);
	   
       $start_time += $add_mins; // to check endtie=me
    }

    $new_array_of_time = array ();
    for($i = 0; $i < count($array_of_time) - 1; $i++)
    {
        $new_array_of_time[] = $array_of_time[$i];
		$new_array_of_time[] = $array_of_time[$i+1];
    }
	
	
    return  array_unique($new_array_of_time);
}
public function getTimeSlotnew($interval, $start_time, $end_time)
{
   
    $i=0;
    $time = [];
	$add_mins  = $interval*60;
	 $array_of_time = array ();
	 
	 $start_time=strtotime($start_time);
	 $end_time=strtotime($end_time);
	
	while ($start_time <= $end_time) // loop between time
    {
       $array_of_time[] = date ("h:i A", $start_time);
	   
       $start_time += $add_mins; // to check endtie=me
    }

    $new_array_of_time = array ();
    for($i = 0; $i < count($array_of_time) - 1; $i++)
    {
        $new_array_of_time[] = $array_of_time[$i];
		$new_array_of_time[] = $array_of_time[$i+1];
    }
	
	
    return  array_unique($new_array_of_time);
}
public function Showtimeslots(Request $request)
{	
$vendor_id=Session::get('cityid');
$date=date('Y-m-d',strtotime($request->date1));
$staff_id=$request->id;
$service=$request->service_id;
$day= date('l',strtotime($request->date1));
 $slots=array();
			if($staff_id==null)
			{	
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$service)->first();
				$staff1=DB::table('staff_profile')->select('staff_id','staff_name','staff_image')->where('staff_id',$vendorservice->staff_id)->first();
				$staff_id=$staff1->staff_id;
				$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			}
			else
			{
				$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			}
			if (isset($time_slot)) {
				
			$start_time = strtotime($time_slot->open_hour);
            $end_time = strtotime($time_slot->close_hour);
            $slot_duration = DB::table('vendor_services')->select('Service_Time')
               ->where('vendor_id',$vendor_id)->where('ServicesID',$service)
              
               ->first();

            //$slot_parts = explode(':', $slot_duration);
            //$slot_hours = intval($slot_parts[0]);
            //$slot_minutes = intval($slot_parts[1]);

            //$slot_duration_minutes =$slot_duration->Service_Time;
			$slot_duration_minutes =15;
            $current_time = $start_time;
            
            $now = strtotime(date('H:i'));
            
				while ($current_time < $end_time) {
                // Skip slots that overlap with break hours
                

                $slot_start = $current_time;
                $current_time += $slot_duration_minutes * 60;

                $startDateTime = date('Y-m-d', strtotime($date)).' '.date('H:i:s', $slot_start);
                $startTimestamp = strtotime($startDateTime);

                $endTimestamp = $startTimestamp + ($slot_duration_minutes * 60);
                
				if ($date === date('Y-m-d') &&  $slot_start < $now) {
                continue;
                 }

                // Check if the slot overlaps with any existing appointments
                $is_booked = false;
				
				$arraytimes=array();
                if ($staff_id) {
                    $existingAppointments = DB::table('order_cart')->where('staff_id', $staff_id)
                        ->whereDate('start_date',$date)->whereNotIn('status',[3,4])
                        ->get();

                    foreach ($existingAppointments as $appointment) {
						$existingAppointments1 = DB::table('orders')->where('id', $appointment->book_ID)
                       
                        ->first();
						$timesnew1=explode("-",$existingAppointments1->service_time);
						$slotnew=$this->getTimeSlotnew(15,$timesnew1[0],$timesnew1[1]);
						
						foreach($slotnew as $slots1)
				{
				$arraytimes[]=$slots1;
				}
                        @$appointment_start = strtotime($appointment->start_date);
						
                        $appointment_end = @$appointment_start + @$appointment->duration;

                        if ($startTimestamp >= $appointment_start && $startTimestamp < $appointment_end) {
                            $is_booked = true;
                            break;
                        }
                    }
                   
                }
              
                if (! $is_booked) {
					
					$slot =  date('h:i A', $slot_start);
					
					if(!in_array($slot,$arraytimes))
					{
                    $slot = [
                       
                      date('h:i A', $slot_start)
                        
                    ];
                    $slots[] = $slot;
					}
                }
            }
			
             
        }
		
      $sel=' <select required  required name="time" class="form-control default-select" id="sel2">';
                                     foreach($slots as $slot)
									 {
								$sel.='<option>'.$slot[0].'</option>';
									 }
                                   
                                $sel.='</select>';
								
								
								
			return response()->json(['view' => 'success','slots'=>$sel]);
				
			}
/*
	public function getTimeSlot($interval, $start_time, $end_time)
{
    $start = new DateTime($start_time);
    $end = new DateTime($end_time);
    $startTime = $start->format('H:i');
    $endTime = $end->format('H:i');
    $i=0;
    $time = [];
    while(strtotime($startTime) <= strtotime($endTime)){
        $start = $startTime;
        $end = date('H:i',strtotime('+'.$interval.' minutes',strtotime($startTime)));
        $startTime = date('H:i',strtotime('+'.$interval.' minutes',strtotime($startTime)));
        $i++;
        if(strtotime($startTime) <= strtotime($endTime)){
            $time[$i] = $start;
            $time[$i] = $end;
        }
    }
    return $time;
}
*/
	
}
