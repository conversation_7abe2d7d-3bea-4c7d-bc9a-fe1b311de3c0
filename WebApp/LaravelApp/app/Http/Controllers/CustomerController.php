<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;

class CustomerController extends Controller
{
     public function CustomerList(Request $request)
	
	{
		$created_at = Carbon::Now();
    if(Session::has('cityadmin'))
		{
		 $users = DB::table('users')->select('users.*','vendor.vendor_name')->join('vendor','vendor.vendor_id','=','users.vendorid','left')->get();
    	    $governarates=DB::table('governarate')
                ->where('enabled',1)
                ->get();
				$html='<option value="">Choose Location</option>';
				foreach($governarates as $governarate)
				{
					$html.='<optgroup label="'.$governarate->Gov_Title.'">';
					$areas=DB::table('areas')
                ->where('enable',1)->where('Gove_ID', '=',$governarate->Gov_ID )
                ->get();
				foreach($areas as $area)
				{
					$html.='<option value="'.$area->Area_Title.'">'.$area->Area_Title.'</option>';
					
				}
				
				$html.='</optgroup>';	
				}  
				$vendors=DB::table('vendor')
                ->where('enabled',1)
                ->get();
    	                   
		return view('admin.customerlist',compact('users','html','vendors'));
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
	
		}
	}
	
	
	public function CustomerProfile(Request $request)
	
	{
		$created_at = Carbon::Now();
    if(Session::has('cityadmin'))
		{
		 $user=DB::table('users')
                ->where('id',$request->id)
                ->first();
    	   $orders = DB::table('orders')->select('orders.*','vendor.*','areas.*','users.id as userid','users.user_phone','users.name','users.email','users.address','users.aboutme')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')->join('users', 'orders.user_id', '=','users.id')->join('areas', 'vendor.area', '=','areas.Area_id')->where('user_id',$request->id)->get();              
               
    	                   
		return view('admin.customerprofile',compact('user','orders'));
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
	
		}
	}
	
	public function Enablecustomer(Request $request)
	{
		
		 if(Session::has('cityadmin'))
     {   
    
        $userid=$request->id;

        $getfile=DB::table('users')
                ->where('id',$request->id)
                ->first();
	
       if($getfile->block==1)
	   {
		   $enable=0;
	   }
	    if($getfile->block==0)
	   {
		   $enable=1;
	   }

    	$update = DB::table('users')
                                ->where('id', $request->id)
                                ->update(['block'=>$enable]);
        if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Enable' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull Enable'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }
		
		
	}
	
}
