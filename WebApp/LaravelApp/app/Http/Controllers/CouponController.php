<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;

class CouponController extends Controller
{
    public function AddCoupon(Request $request)
	{
		 if(Session::has('cityadmin'))
		{
			$users = DB::table('users')->get();
			$vendors = DB::table('vendor')->get();
			return view('admin.add-coupon',compact('users','vendors'));
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
	
		}
		
	}
	
	 public function SaveCoupon(Request $request)
	{
		 if(Session::has('cityadmin'))
		{
		$title=$request->title;
		$code=$request->code;
		$applicable=$request->applicable;
		$users=$request->users;
		$startdate=$request->startdate;
		$enddate=$request->enddate;
		$amount=$request->amount;
		$offertype=$request->offertype;
		$applicable_vendor=$request->applicable_vendor;
		$vendors=($request->vendors) ? implode(',', $request->vendors) : 0;
		
		$this->validate($request,[
         		'title'=>'required',
				'applicable'=>'required',
				'startdate'=>'required',
				'enddate'=>'required',
				'amount'=>'required',
				'offertype'=>'required',
				'code'=>'required'
				
         		
         		
         ]

	);
	$start_date=date('Y-m-d',strtotime($startdate));
	$end_date=date('Y-m-d',strtotime($enddate));
	
			$insert = DB::table('coupon')
    				->insert(['coupon_name'=>$title,'coupon_code'=>$code,'start_date'=>$start_date,'end_date'=>$end_date,
    				'amount'=>$amount,'type'=>$offertype,'user_id'=>$users,'applicable'=>$applicable,'available_for_vendors'=>$applicable_vendor,'available_vendors'=>$vendors]);
					 if($insert){

        return redirect()->back()->with('message', 'Promo Code Added Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
	
		}
		
	}
	
	public function ListCoupon(Request $request)
	{
		 if(Session::has('cityadmin'))
		{
			$users = DB::table('users')->get();
			$coupons=DB::table('coupon')->get();
			return view('admin.list-coupon',compact('users','coupons'));
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
	
		}
		
	}
	
	
	public function EditCoupon(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$users = DB::table('users')->get();
    	    $coupons=DB::table('coupon')->where('coupon_id',$request->id)->first();           
			
    	   $vendors = DB::table('vendor')->get();    
    	                   
		return view('admin.edit-coupon',compact('users','coupons','vendors'));	                   
		
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	
	public function UpdateCoupon(Request $request)
	
	{
		$created_at = Carbon::Now();
   if(Session::has('cityadmin'))
		{
		$title=$request->title;
		$code=$request->code;
		$applicable=$request->applicable;
		$users=$request->users;
		$startdate=$request->startdate;
		$enddate=$request->enddate;
		$amount=$request->amount;
		$offertype=$request->offertype;
		$applicable_vendor=$request->applicable_vendor;
		$vendors=($request->vendors) ? implode(',', $request->vendors) : 0;
		
		$this->validate($request,[
         		'title'=>'required',
				'applicable'=>'required',
				'startdate'=>'required',
				'enddate'=>'required',
				'amount'=>'required',
				'offertype'=>'required',
				'code'=>'required'
				
         		
         		
         ]

	);
	
		
		$start_date=date('Y-m-d',strtotime($startdate));
	$end_date=date('Y-m-d',strtotime($enddate));
			$update = DB::table('coupon')
                                ->where('coupon_id', $request->id)
                                ->update(['coupon_name'=>$title,'coupon_code'=>$code,'start_date'=>$start_date,'end_date'=>$end_date,'amount'=>$amount,'type'=>$offertype,
                                'user_id'=>$users,'applicable'=>$applicable,'available_for_vendors'=>$applicable_vendor,'available_vendors'=>$vendors]);
					 if($update){

        return redirect()->back()->with('message', 'Updated Coupon Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }
	}
	}
	
	
	
	
	public function DeleteCoupon(Request $request)
	{
		
		 if(Session::has('cityadmin'))
     {   
    
       

        

       

    	$delete=DB::table('coupon')->where('coupon_id',$request->id)->delete();
        if($delete)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Delete' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }
		
		
	}
	
	
	public function EnableCoupon(Request $request)
	{
	 if(Session::has('cityadmin'))
     {   
    
        $id=$request->id;

        $getfile=DB::table('coupon')
                ->where('coupon_id',$request->id)
                ->first();
	
       if($getfile->enabled==1)
	   {
		   $enable=0;
	   }
	    if($getfile->enabled==0)
	   {
		   $enable=1;
	   }

    	$update = DB::table('coupon')
                                ->where('coupon_id', $request->id)
                                ->update(['enabled'=>$enable]);
        if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Enable' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull Enable'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }	
		
		
		
		
	}
	
}
