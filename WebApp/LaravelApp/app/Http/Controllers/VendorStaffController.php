<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;
use Illuminate\Support\Str;
use DateTime;

class VendorStaffController extends Controller
{
      public function Login()
	{
		
		
		return view('staff.login');
	}
	public function checkstaffLogin(Request $request)
    {
    	$admin_email=$request->admin_email;
    	$admin_pass=$request->admin_pass;

    	$this->validate(
         $request,
         [
         		'admin_email'=>'required',
         		'admin_pass'=>'required',
         ],
         [

         	'admin_email.required'=>'Enter E-Mail',
         	'admin_pass.required'=>'Enter the password',
         ]

);
    	$adminLogin = DB::table('staff_profile')
    	                   ->where('email',$admin_email)
    	                   ->first();

if($adminLogin->enabled==0)
					{
					return redirect()->route('staff-login')->withErrors('Your account has been disabled by Vendor Administrator. Please Contact Administrator.');
exit();					
					}
    	if($adminLogin){

         if(Hash::check($admin_pass,$adminLogin->staff_pass)){
			   session::put('citystaffid',$adminLogin->staff_id);
			    session::put('staffvendor',$adminLogin->vendor_id);
           session::put('citystaff',$adminLogin->email);
		    session::put('admin_name',$adminLogin->staff_name);
		   session::put('admin_image',$adminLogin->staff_image);
		   session::put('admin_phone',$adminLogin->phone);
		   
		 
			
		 
           return redirect()->route('staffDashboard');
         }
         else
         {
         	return redirect()->route('staff-login')->withErrors('wrong password');
         }
    	}
    	else
    	{
             return redirect()->route('staff-login')->withErrors('invalid email and password');
    	}

    }
	public function staffDashboard(Request $request)
    {
		if(Session::has('citystaff'))
		{
			$email = Session::get('citystaff');
			$vendor_id = Session::get('staffvendor');
				$users1 = DB::table('users')->distinct()
    	                   ->where('orders.vendor_id',$vendor_id)->join('orders', 'users.id', '=','orders.user_id')->orderBy('orders.id', 'desc')
    	                   ->get();
							$user2=DB::table('users')->distinct()
    	                   ->where('vendorid',$vendor_id)->orderBy('id', 'desc')
    	                   ->get();
						  

$collection = collect($users1)->unique('users.id');
    $merged     = $collection->merge($user2);
    $users   = $merged->all();
						  
			$clients=count($users);
			/*
			$client = DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->join('users', 'orders.user_id', '=','users.id')->orderBy('orders.id', 'desc')
    	                   ->get();
						   
			$clients=count($client);
			*/
				$services=DB::table('vendor_services')->where('vendor_id',$vendor_id)->get();
				$service1=count($services);
				$total = DB::table('orders')
                ->select(DB::raw('SUM(total_price) as total'))->where('vendor_id',$vendor_id)
                ->get();
				$staff = DB::table('staff_profile')
               ->where('vendor_id',$vendor_id)
                ->get();
				$staffs=count($staff);
				$vendorenddate=DB::table('vendor')->where('vendor_id',$vendor_id)->first();
				
				$vendor_id = Session::get('staffvendor');
		
		if($request->staff=='')
		{
		
		$staff=Session::get('cityid');
		}
		else
		{
		$staff=$request->staff;
		}
		
		$staffbooking=DB::table('order_cart')->where('staff_id',$staff)
    	                   ->pluck('book_ID')->toArray();
					//$vendor_id=DB::table('order_cart')->where('staff_id',$staff)
    	                   //->pluck('vendor_id')->first();
				   $staffbooking=array_unique($staffbooking);
				  
			$employees=DB::table('staff_profile')->where('staff_profile.vendor_id',$vendor_id)
    	                   ->get();
				  
						    $service=array();
						   foreach($employees as $employee)
						   {
						   $servicenames=DB::table('vendor_services')->join('staff_services', 'staff_services.Service_ID', '=','vendor_services.ServicesID')->where('vendor_services.vendor_id',$vendor_id)->where('staff_services.staff_id',$employee->staff_id)
    	                   ->get();
						   $services='';
						    foreach($servicenames as $servicename)
						   {
						   $services.=$servicename->Name.",";
						   }
						   $services=rtrim($services,",");
						   $service[$employee->staff_id]=$services;
						   }
						  
			$vendor = $vendor_id;
			if($staff=='')
			{
			$booking = DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->join('users', 'orders.user_id', '=','users.id')->orderBy('orders.id', 'desc')
    	                   ->get();
						   
			}
			else
			{
			$booking = DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->whereIn('orders.id',  $staffbooking)->join('users', 'orders.user_id', '=','users.id')->orderBy('orders.id', 'desc')
    	                  ->get();
					
			}
						   //$staffs=DB::table('staff_profile')->where('vendor_id',$vendor)->get();
			$users1 = DB::table('users')->distinct()
    	                   ->where('orders.vendor_id',$vendor_id)->join('orders', 'users.id', '=','orders.user_id')->orderBy('orders.id', 'desc')
    	                   ->get();
							$user2=DB::table('users')->distinct()
    	                   ->where('vendorid',$vendor_id)->orderBy('id', 'desc')
    	                   ->get();
				 $employees1=DB::table('staff_profile')
               ->where('vendor_id',$vendor_id)
                ->get();		   
			$vendor_services = DB::table('vendor_services')
    	                   ->where('vendor_id',$vendor_id)->orderBy('Name', 'asc')
    	                   ->get();		   
						  

$collection = collect($users1);
    $merged     = $collection->merge($user2);
    $users   = $merged->all();
			
$bookingdates=array();

for($i=1;$i<=31;$i++)
{
	$day=$i;
	$month=date('m');
	$year=date('Y');
	$date=$year."-".$month."-".$day;
	$orders=DB::table('orders')->where('vendor_id',$vendor_id)->where('service_date',$date)->count();
	$bookingdates[]=$orders;
	
}
$bookingdate=implode(",",$bookingdates);
$orders = DB::table('orders')->select(
            DB::raw('sum(total_price) as sums'), 
            DB::raw("DATE_FORMAT(service_date,'%m') as monthKey")
  )
  ->whereYear('service_date', date('Y'))
   ->where('vendor_id',$vendor_id)
  ->groupBy('service_date')
  ->orderBy('service_date', 'ASC')
  ->get();
$ordertotal = [0,0,0,0,0,0,0,0,0,0,0,0];

foreach($orders as $order){
    $ordertotal[$order->monthKey-1] = $order->sums;
}  
$max=max($ordertotal);
$ordertotals=implode(",",$ordertotal);


$bookings = DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->join('users', 'orders.user_id', '=','users.id')->where('service_date', '>=', date('Y-m-d'))->orderBy('orders.id', 'desc')
    	                   ->get();
		return view('staff.dashboard',compact('bookings','max','ordertotals','bookingdate','employees1','clients','service1','total','staffs','vendorenddate','booking'));
		}
		else
		{
		return redirect()->route('staff-login')->withErrors('please login first');	
		}
		
	}
	
	
	public function SaveCustomer(Request $request)
	
	{
		$created_at = Carbon::Now();
   if(Session::has('citystaff'))
		{
		$firstname=$request->firstname;
		$lastname=$request->lastname;
		$email=$request->email;
		$phone=$request->phone;
		$gender=$request->gender;
		$address=$request->address;
		$country=$request->country;
		$password=$request->password;
		$this->validate(
         $request,
         [		'firstname'=>'required',
         		'lastname'=>'required',
				 'email' => 'required|email|unique:users,email',
				 'gender'=>'required',
				 'address'=>'required',
				 'country'=>'required',
				 'password'=>'required|min:6',
				 'phone'=>'required'
		 ]

);

$password=Hash::make($password);
$name=$firstname." ".$lastname;
$vendor_id = Session::get('cityid');
$insert = DB::table('users')
    				->insertGetId(['name'=>$name,'user_phone'=>$phone,'firstname'=>$firstname,'lastname'=>$lastname,'email'=>$email,'gender'=>$gender,'address'=>$address,'country'=>$country,'password'=>$password,'vendorid'=>$vendor_id]);
 if($insert!=''){
	 return redirect()->back()->with('message', 'Added Successfully'); 
 }
 else
 {
return redirect()->back()->withErrors('Nothing to Add');	 
 }
		
		}
	else{
		return redirect()->route('staff-login')->withErrors('please login first');
	
		}
	}
	
	
	
	public function SaveBookingSlot(Request $request)
	
	{
		if(Session::has('citystaff'))
		{
			
	$staff=Session::get('citystaffid');
	$vendor=Session::get('staffvendor');
	
	$vendor_id=	Session::get('staffvendor');
	$date=$request->date;
	$serviceat=$request->serviceat;
	$client=$request->clients;
	$services=$request->services;
	$time1=$request->time;
	$time2=$request->time1;
	$paymentstatus=$request->status;
	$paymentmethod=$request->paymentmethod;
	if($client==180)
	{
		$permission=DB::table('staff_profile')->where('staff_id',$staff)->first();
		$breaktime=$permission->breaktime;
		if($breaktime==0)
		{
		return redirect()->back()->withErrors('There is no permission for Adding Breaktime'); 		
		}
	$this->validate($request,[
         		
				'date'=>'required',
				
				'clients'=>'required',
				'services.*'=>'required',
				'time'=>'required'
				
				]

	);
	}
	else
	{
	$this->validate($request,[
         		
				'date'=>'required',
				'serviceat'=>'required',
				'clients'=>'required',
				'services.*'=>'required',
				'time'=>'required',
				'status'=>'required',
				'paymentmethod'=>'required']

	);	
		
	}
	$datecreated = date('d-m-Y');
	
	$date=date('Y-m-d',strtotime($date));
	
	$day = date('l', strtotime($date));
		
		$time=DB::table('employee_time_slot')->where('vendor_id',$vendor)->where('staff_id',$staff)->where('days',$day)->first();
		


		$openhour=$time->open_hour;
		$closehour=$time->close_hour;
		
		if($openhour!='' and $closehour!='')
		{
		$slots=$this->getTimeSlot(30,$openhour,$closehour);
		}
		else
			
		{
		$slots="No Slots";
				
		}
		$bookings=DB::table('order_cart')->join('orders', 'orders.id', '=','order_cart.book_ID')
                ->where('staff_id',$request->id)->where('order_cart.vendor_id',$vendor_id)->where('orders.status','!=',3)->where('orders.status','!=',4)
                ->get();
				$slotnew=[];
				foreach($bookings as $booking)
				{
					$times=$booking->service_time;
					$timesslots=explode("-",$times);
					$timesslot1=$timesslots[0];
					$timesslot2=$timesslots[1];
					$timesslot1=date("H:i", strtotime($timesslot1));
					$timesslot2=date("H:i", strtotime($timesslot2));
					$slotnew=$this->getTimeSlot(30,$timesslot1,$timesslot2);
				}
				$totaltime=0;
				$pricetotal=0;
				foreach($slots as $slot)
				{
				 if (false !== $key = array_search($slot, $slotnew)) {
					 unset($slots[$key]);	
					}
				}
				if($client==180)
				{
					$selectedslots=$this->getTimeSlot(30,$time1,$time2);
					foreach($selectedslots as $selslot)
					{
						$key = array_search($selslot, $slots);
						if ($key == false) {
						return redirect()->back()->withErrors('Already Booked Same date and Time');	
						}
					}
				}
			$key = array_search($time1, $slots);
if ($key !== false) {
   
foreach($services as $service)
					{
						
					$price=DB::table('vendor_services')
                ->where('vendor_id',$vendor_id)->where('ServicesID',$service)
                ->first();
				
				$totaltime=$totaltime+$price->Service_Time;
				$pricetotal=$pricetotal+$price->Service_Price;
					}
					$endtime = date('H:i',strtotime('+'.$totaltime.' minutes',strtotime($time1)));
					if($client=180)
					{
					$slotnew1=$this->getTimeSlot(30,$time1,$time2);
					}
					else
					{
					$slotnew1=$this->getTimeSlot(30,$time1,$endtime);	
					}
					$count=count($services);
					
					$totime=end($slotnew1);
					$totime=date("h:i A",strtotime($totime));
					$fromtime=$time1;
					$fromtime=date("h:i A",strtotime($fromtime));
					$newtime=$fromtime."-".$totime;
					$bookID="BOOK".rand().time();
					$pricetotal=0;
					
					$mobile=DB::table('users')
                ->where('id',$client)
                ->first();
				
					$bookdate=date('Y-m-d');
					if($client==180)
				{
					$serviceat='Salon';
					$insert = DB::table('orders')
    				->insertGetId(['user_id'=>$client,'vendor_id'=> $vendor_id,'Bookid'=>$bookID,'total_price'=>0,'service_date'=>$date,'service_time'=>$newtime,'payment_method'=>$paymentmethod,'payment_status'=>'Paid','status'=>1,'mobile'=>$mobile->user_phone,'payment_gateway'=>$paymentmethod,'bookdate'=>$bookdate,'created_at'=>$bookdate,'updated_at'=>$bookdate,'Bookingat'=>$serviceat]);
				}
				else
				{
				$insert = DB::table('orders')
    				->insertGetId(['user_id'=>$client,'vendor_id'=> $vendor_id,'Bookid'=>$bookID,'total_price'=>$pricetotal,'service_date'=>$date,'service_time'=>$newtime,'payment_method'=>$paymentmethod,'payment_status'=>$paymentstatus,'status'=>1,'mobile'=>$mobile->user_phone,'payment_gateway'=>$paymentmethod,'bookdate'=>$bookdate,'created_at'=>$bookdate,'updated_at'=>$bookdate,'Bookingat'=>$serviceat]);	
				}
					if($insert!='')
					{
						foreach($services as $key=>$service)
					{
						$newqueryservice=DB::table('vendor_services')
                ->where('vendor_id',$vendor_id)->where('ServicesID',$service)
                ->first();
				$fromtime1=date('Y-m-d H:i:s',strtotime($date." ".$fromtime));
						$insert1 = DB::table('order_cart')
    				->insert(['service_name'=>$newqueryservice->Name,'service_id'=>$newqueryservice->ServicesID,'price'=>$newqueryservice->Service_Price,'user_id'=>$client,'vendor_id'=>$vendor_id,'staff_id'=>$staff,'book_ID'=>$insert,'start_date'=>$fromtime1,'duration'=>$totaltime,'sequence'=>$key,'status'=>1]);	
					}
					}
					
					return redirect()->back()->with('message', 'Appointment Added Successfully');
				}
				else
				{
				  return redirect()->back()->withErrors('Already Booked Same date and Time'); 	
				}
				
				
		
	}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}

		
	}
	
	
	public function getTimeSlot($interval, $start_time, $end_time)
{
   
    $i=0;
    $time = [];
	$add_mins  = $interval*60;
	 $array_of_time = array ();
	 
	 $start_time=strtotime($start_time);
	 $end_time=strtotime($end_time);
	
	while ($start_time <= $end_time) // loop between time
    {
       $array_of_time[] = date ("h:i A", $start_time);
	   
       $start_time += $add_mins; // to check endtie=me
    }

    $new_array_of_time = array ();
    for($i = 0; $i < count($array_of_time) - 1; $i++)
    {
        $new_array_of_time[] = $array_of_time[$i];
		$new_array_of_time[] = $array_of_time[$i+1];
    }
	
	
    return  array_unique($new_array_of_time);
}
public function getTimeSlotnew($interval, $start_time, $end_time)
{
   
    $i=0;
    $time = [];
	$add_mins  = $interval*60;
	 $array_of_time = array ();
	 
	 $start_time=strtotime($start_time);
	 $end_time=strtotime($end_time);
	
	while ($start_time <= $end_time) // loop between time
    {
       $array_of_time[] = date ("h:i A", $start_time);
	   
       $start_time += $add_mins; // to check endtie=me
    }

    $new_array_of_time = array ();
    for($i = 0; $i < count($array_of_time) - 1; $i++)
    {
        $new_array_of_time[] = $array_of_time[$i];
		$new_array_of_time[] = $array_of_time[$i+1];
    }
	
	
    return  array_unique($new_array_of_time);
}
	 public function StaffLogout(Request $request)
     {
		  
		   
		   
		 $request->session()->forget('citystaffid');
	  $request->session()->forget('staffvendor');
	   $request->session()->forget('citystaff');
	  $request->session()->forget('admin_name');
	  $request->session()->forget('admin_image');
	  $request->session()->forget('admin_phone'); 
	  
	    return redirect()->route('staff-login')->withErrors("Logged Out Successfully");
	 }
	
	 public function Calendar(Request $request)
	{
		$vendor_id = Session::get('staffvendor');
		
		if($request->staff=='')
		{
		
		$staff=Session::get('cityid');
		}
		else
		{
		$staff=$request->staff;
		}
		
		$staffbooking=DB::table('order_cart')->where('staff_id',$staff)
    	                   ->pluck('book_ID')->toArray();
					//$vendor_id=DB::table('order_cart')->where('staff_id',$staff)
    	                   //->pluck('vendor_id')->first();
				   $staffbooking=array_unique($staffbooking);
				  
			$employees=DB::table('staff_profile')->where('staff_profile.vendor_id',$vendor_id)
    	                   ->get();
				  
						    $service=array();
						   foreach($employees as $employee)
						   {
						   $servicenames=DB::table('vendor_services')->join('staff_services', 'staff_services.Service_ID', '=','vendor_services.ServicesID')->where('vendor_services.vendor_id',$vendor_id)->where('staff_services.staff_id',$employee->staff_id)
    	                   ->get();
						   $services='';
						    foreach($servicenames as $servicename)
						   {
						   $services.=$servicename->Name.",";
						   }
						   $services=rtrim($services,",");
						   $service[$employee->staff_id]=$services;
						   }
						  
			$vendor = $vendor_id;
			if($staff=='')
			{
			$booking = DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->join('users', 'orders.user_id', '=','users.id')->orderBy('orders.id', 'desc')
    	                   ->get();
						   
			}
			else
			{
			$booking = DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->whereIn('orders.id',  $staffbooking)->join('users', 'orders.user_id', '=','users.id')->orderBy('orders.id', 'desc')
    	                  ->get();
					
			}
						   //$staffs=DB::table('staff_profile')->where('vendor_id',$vendor)->get();
			$users1 = DB::table('users')->distinct()
    	                   ->where('orders.vendor_id',$vendor_id)->join('orders', 'users.id', '=','orders.user_id')->orderBy('orders.id', 'desc')
    	                   ->get();
							$user2=DB::table('users')->distinct()
    	                   ->where('vendorid',$vendor_id)->orderBy('id', 'desc')
    	                   ->get();
				 $employees1=DB::table('staff_profile')
               ->where('vendor_id',$vendor_id)
                ->get();		   
			$vendor_services = DB::table('vendor_services')
    	                   ->where('vendor_id',$vendor_id)->orderBy('Name', 'asc')
    	                   ->get();		   
						  

$collection = collect($users1);
    $merged     = $collection->merge($user2);
    $users   = $merged->all();
		
		return view('staff.calendar',compact('employees','service','booking','employees1','users','vendor_services'));
	}
	
	
	
	 public function vendorBooking(Request $request)
    {
		if(Session::has('citystaffid'))
		{
			$vendor_id = Session::get('staffvendor');
			$vendor =Session::get('staffvendor');
			$staff=Session::get('citystaffid');
			$bookings = DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->join('users', 'orders.user_id', '=','users.id')->orderBy('orders.id', 'desc')
    	                   ->get();
						   //$staffs=DB::table('staff_profile')->where('vendor_id',$vendor)->get();
			$users1 = DB::table('users')->distinct()
    	                   ->where('orders.vendor_id',$vendor_id)->join('orders', 'users.id', '=','orders.user_id')->orderBy('users.id', 'desc')->select('users.*','orders.id as id1')
    	                   ->get();
							$user2=DB::table('users')->distinct()
    	                   ->where('vendorid',$vendor_id)->orderBy('users.id', 'desc')->select('users.*')
    	                   ->get();
				 $employees=DB::table('staff_profile')
               ->where('vendor_id',$vendor_id)
                ->get();		   
			$vendor_services = DB::table('vendor_services as vs')
    ->join('staff_services as ss', 'vs.ServicesID', '=', 'ss.Service_ID')
    ->where('ss.staff_id', $staff)
    ->select('vs.*')
    ->get();		   
						  

$collection = collect($users1)->unique('users.id');
    $merged     = $collection->merge($user2);
    $users   = $merged->all();
				
			return view('staff.bookinglist',compact('bookings','employees','users','vendor_services'));
		}
		else
		{
		return redirect()->route('staff-login')->withErrors('please login first');	
		}
		
	}
	
	public function ViewVendorBooking(Request $request)
    {
		if(Session::has('citystaffid'))
		{
			$vendor=Session::get('staffvendor');
			
	$vendor_id = Session::get('staffvendor');
	$staffs=DB::table('staff_profile')->where('vendor_id',$vendor)->get();
			$bookingdetails = DB::table('order_cart')->where('book_ID',$request->id)->where('vendor_id',$vendor)->get();
			$bookings = DB::table('orders')->select('orders.*','vendor.*','areas.*','users.id as userid','users.user_phone','users.name','users.email','users.address','users.aboutme')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')->join('users', 'orders.user_id', '=','users.id')->join('areas', 'vendor.area', '=','areas.Area_id')->where('orders.id',$request->id)->where('orders.vendor_id',$vendor)->first();
			
			return view('staff.bookinglistdetail',compact('bookings','bookingdetails','staffs'));
		}
		else
		{
		return redirect()->route('staff-login')->withErrors('please login first');	
		}
		
	}
	
	public function ChangeStaff(Request $request)
    {
		if(Session::has('citystaffid'))
		{
			$vendor=Session::get('staffvendor');
			$id=$request->id;
			$staffid=$request->staffid;
				$update=DB::table('order_cart')->where('order_cart_id', $id)->where('vendor_id',$vendor)
                                ->update
    				(['staff_id'=>$staffid]);
					 if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Update' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }
		
		}
		else
		{
		return redirect()->route('staff-login')->withErrors('please login first');	
		}
		
	}
	
	
	public function ChangeStatus(Request $request)
    {
		if(Session::has('citystaffid'))
		{
			$vendor=Session::get('staffvendor');
			$id=$request->id;
			$status=$request->status;
				$update=DB::table('orders')->where('id', $id)->where('vendor_id',$vendor)
                                ->update
    				(['status'=>$status]);
					$update=DB::table('order_cart')->where('book_ID', $id)->where('vendor_id',$vendor)
                                ->update
    				(['status'=>$status]);
					 if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Update' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }
		
		}
		else
		{
		return redirect()->route('staff-login')->withErrors('please login first');	
		}
		
	}
	public function Showtimeslots(Request $request)
	{
		
		if(Session::has('citystaffid'))
		{
		$staff=Session::get('citystaffid');
		$staff_id=Session::get('citystaffid');
		$vendor=Session::get('staffvendor');
		$vendor_id=Session::get('staffvendor');
		$service=$request->service_id;
		$date=date('Y-m-d',strtotime($request->date1));
		$day= date('l',strtotime($request->date1));
		$slots=array();
		if($staff_id==null)
			{	
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$service)->first();
				$staff1=DB::table('staff_profile')->select('staff_id','staff_name','staff_image')->where('staff_id',$vendorservice->staff_id)->first();
				$staff_id=$staff1->staff_id;
				$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			}
			else
			{
				$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			}
			
			if (isset($time_slot)) {
				
			$start_time = strtotime($time_slot->open_hour);
            $end_time = strtotime($time_slot->close_hour);
            $slot_duration = DB::table('vendor_services')->select('Service_Time')
               ->where('vendor_id',$vendor_id)->where('ServicesID',$service)
              
               ->first();

            //$slot_parts = explode(':', $slot_duration);
            //$slot_hours = intval($slot_parts[0]);
            //$slot_minutes = intval($slot_parts[1]);

            //$slot_duration_minutes =$slot_duration->Service_Time;
			$slot_duration_minutes =15;
            $current_time = $start_time;
            
            $now = strtotime(date('H:i'));
            
				while ($current_time < $end_time) {
                // Skip slots that overlap with break hours
                

                $slot_start = $current_time;
                $current_time += $slot_duration_minutes * 60;

                $startDateTime = date('Y-m-d', strtotime($date)).' '.date('H:i:s', $slot_start);
                $startTimestamp = strtotime($startDateTime);

                $endTimestamp = $startTimestamp + ($slot_duration_minutes * 60);
                
				if ($date === date('Y-m-d') &&  $slot_start < $now) {
                continue;
                 }

                // Check if the slot overlaps with any existing appointments
                $is_booked = false;
				
				$arraytimes=array();
                if ($staff_id) {
                    $existingAppointments = DB::table('order_cart')->where('staff_id', $staff_id)
                        ->whereDate('start_date',$date)->whereNotIn('status',[3,4])
                        ->get();

                    foreach ($existingAppointments as $appointment) {
						$existingAppointments1 = DB::table('orders')->where('id', $appointment->book_ID)
                       
                        ->first();
						$timesnew1=explode("-",$existingAppointments1->service_time);
						$slotnew=$this->getTimeSlotnew(15,$timesnew1[0],$timesnew1[1]);
						
						foreach($slotnew as $slots1)
				{
				$arraytimes[]=$slots1;
				}
                        @$appointment_start = strtotime($appointment->start_date);
						
                        $appointment_end = @$appointment_start + @$appointment->duration;

                        if ($startTimestamp >= $appointment_start && $startTimestamp < $appointment_end) {
                            $is_booked = true;
                            break;
                        }
                    }
                   
                }
              
                if (! $is_booked) {
					
					$slot =  date('h:i A', $slot_start);
					
					if(!in_array($slot,$arraytimes))
					{
                    $slot = [
                       
                      date('h:i A', $slot_start)
                        
                    ];
                    $slots[] = $slot;
					}
                }
            }
			
             
        }
		
      $sel=' <select required  required name="time" class="form-control default-select" id="sel7">';
                                     foreach($slots as $slot)
									 {
								$sel.='<option>'.$slot[0].'</option>';
									 }
                                   
                                $sel.='</select>';
								
								
								
			return response()->json(['view' => 'success','slots'=>$sel]);
		
		}
		
	else
		{
		return redirect()->route('staff-login')->withErrors('please login first');	
		}
		
	}
	
	
	public function Showtimeslots1(Request $request)
	{
		
		if(Session::has('citystaffid'))
		{
		$staff=Session::get('citystaffid');
		$staff_id=Session::get('citystaffid');
		$vendor=Session::get('staffvendor');
		$vendor_id=Session::get('staffvendor');
		$service=$request->service_id;
		$date=date('Y-m-d',strtotime($request->date1));
		$day= date('l',strtotime($request->date1));
		$slots=array();
		if($staff_id==null)
			{	
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$service)->first();
				$staff1=DB::table('staff_profile')->select('staff_id','staff_name','staff_image')->where('staff_id',$vendorservice->staff_id)->first();
				$staff_id=$staff1->staff_id;
				$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			}
			else
			{
				$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			}
			
			if (isset($time_slot)) {
				
			$start_time = strtotime($time_slot->open_hour);
            $end_time = strtotime($time_slot->close_hour);
            $slot_duration = DB::table('vendor_services')->select('Service_Time')
               ->where('vendor_id',$vendor_id)->where('ServicesID',$service)
              
               ->first();

            //$slot_parts = explode(':', $slot_duration);
            //$slot_hours = intval($slot_parts[0]);
            //$slot_minutes = intval($slot_parts[1]);

            //$slot_duration_minutes =$slot_duration->Service_Time;
			$slot_duration_minutes =15;
            $current_time = $start_time;
            
            $now = strtotime(date('H:i'));
            
				while ($current_time < $end_time) {
                // Skip slots that overlap with break hours
                

                $slot_start = $current_time;
                $current_time += $slot_duration_minutes * 60;

                $startDateTime = date('Y-m-d', strtotime($date)).' '.date('H:i:s', $slot_start);
                $startTimestamp = strtotime($startDateTime);

                $endTimestamp = $startTimestamp + ($slot_duration_minutes * 60);
                
				if ($date === date('Y-m-d') &&  $slot_start < $now) {
                continue;
                 }

                // Check if the slot overlaps with any existing appointments
                $is_booked = false;
				
				$arraytimes=array();
                if ($staff_id) {
                    $existingAppointments = DB::table('order_cart')->where('staff_id', $staff_id)
                        ->whereDate('start_date',$date)->whereNotIn('status',[3,4])
                        ->get();

                    foreach ($existingAppointments as $appointment) {
						$existingAppointments1 = DB::table('orders')->where('id', $appointment->book_ID)
                       
                        ->first();
						$timesnew1=explode("-",$existingAppointments1->service_time);
						$slotnew=$this->getTimeSlotnew(15,$timesnew1[0],$timesnew1[1]);
						
						foreach($slotnew as $slots1)
				{
				$arraytimes[]=$slots1;
				}
                        @$appointment_start = strtotime($appointment->start_date);
						
                        $appointment_end = @$appointment_start + @$appointment->duration;

                        if ($startTimestamp >= $appointment_start && $startTimestamp < $appointment_end) {
                            $is_booked = true;
                            break;
                        }
                    }
                   
                }
              
                if (! $is_booked) {
					
					$slot =  date('h:i A', $slot_start);
					
					if(!in_array($slot,$arraytimes))
					{
                    $slot = [
                       
                      date('h:i A', $slot_start)
                        
                    ];
                    $slots[] = $slot;
					}
                }
            }
			
             
        }
		
      $sel=' <select  name="time1" class="form-control default-select" id="sel8">';
                                     foreach($slots as $slot)
									 {
								$sel.='<option>'.$slot[0].'</option>';
									 }
                                   
                                $sel.='</select>';
								
								
								
			return response()->json(['view' => 'success','slots'=>$sel]);
		
		}
		
	else
		{
		return redirect()->route('staff-login')->withErrors('please login first');	
		}
		
	}
	
	public function Profile(Request $request)
	{
		if(Session::has('citystaffid'))
		{
			$vendor=Session::get('staffvendor');
			$vendor_id=Session::get('staffvendor');
			$staff=Session::get('citystaffid');
			$staffdetail=DB::table('staff_profile')->where('vendor_id',$vendor)->where('staff_id',$staff)->first();
			$timeslots=DB::table('employee_time_slot')->where('vendor_id',$vendor)->where('staff_id',$staff)->get();
			$staffservices=DB::table('vendor_services')->join('staff_services', 'staff_services.Service_ID', '=','vendor_services.ServicesID')->where('vendor_services.vendor_id',$vendor_id)->where('staff_services.staff_id',$staff)
    	                   ->get();
			return view('staff.profile',compact('timeslots','staffdetail','staffservices'));
		}
	else
		{
		return redirect()->route('staff-login')->withErrors('please login first');	
		}
		
	}
	 public function staffChangeaddress(Request $request)
    {
		if(Session::has('citystaffid'))
		{
			
		return view('staff.change-password');
		}
		else
		{
		return redirect()->route('staff-login')->withErrors('please login first');	
		}
		
	}
	
	 public function staffchangepassword(Request $request)
    {
		if(Session::has('citystaffid'))
		{
		$id = Session::get('citystaffid');
			$password=$request->password;
			$repassword=$request->repassword;
$this->validate(
         $request,
         [
         		'password' => 'required|repassword|min:6',
				
         ]

);
$password=Hash::make($password);

$update=DB::table('staff_profile')->where('staff_id ', $id)
                                ->update
    				(['staff_pass'=>$password]);
			
		return redirect()->back()->with('message', 'Updated Successfully');
		}
		else
		{
		return redirect()->route('staff-login')->withErrors('please login first');	
		}
		
	}
	
	
	
}
