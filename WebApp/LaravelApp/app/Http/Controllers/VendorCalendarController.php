<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;

class VendorCalendarController extends Controller
{
     public function Calendar(Request $request)
	{
		if(Session::has('cityvendor'))
		{
		$vendor_id = Session::get('cityid');
		$staff=$request->staff;
		$staffbooking=DB::table('order_cart')->where('vendor_id',$vendor_id)->where('staff_id',$staff)
    	                   ->pluck('book_ID')->toArray();
						   
			$employees=DB::table('staff_profile')->where('staff_profile.vendor_id',$vendor_id)
    	                   ->get();
						  
						    $service=array();
						   foreach($employees as $employee)
						   {
						   $servicenames=DB::table('vendor_services')->join('staff_services', 'staff_services.Service_ID', '=','vendor_services.ServicesID')->where('vendor_services.vendor_id',$vendor_id)->where('staff_services.staff_id',$employee->staff_id)
    	                   ->get();
						   $services='';
						    foreach($servicenames as $servicename)
						   {
						   $services.=$servicename->Name.",";
						   }
						   $services=rtrim($services,",");
						   $service[$employee->staff_id]=$services;
						   }
						   $vendor_id = Session::get('cityid');
			$vendor = Session::get('cityid');
			if($staff=='')
			{
			$booking = DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->join('users', 'orders.user_id', '=','users.id')->orderBy('orders.id', 'desc')
    	                   ->get();
			}
			else
			{
			$booking = DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->join('users', 'orders.user_id', '=','users.id')->orderBy('orders.id', 'desc')
    	                   ->whereIn('orders.id', $staffbooking)->get();	
			}
						   //$staffs=DB::table('staff_profile')->where('vendor_id',$vendor)->get();
			$users1 = DB::table('users')->distinct()
    	                   ->where('orders.vendor_id',$vendor_id)->join('orders', 'users.id', '=','orders.user_id')->orderBy('orders.id', 'desc')
    	                   ->get();
							$user2=DB::table('users')->distinct()
    	                   ->where('vendorid',$vendor_id)->orderBy('id', 'desc')
    	                   ->get();
				 $employees1=DB::table('staff_profile')
               ->where('vendor_id',$vendor_id)
                ->get();		   
			$vendor_services = DB::table('vendor_services')
    	                   ->where('vendor_id',$vendor_id)->orderBy('Name', 'asc')
    	                   ->get();		   
						  

$collection = collect($users1);
    $merged     = $collection->merge($user2);
    $users   = $merged->all();
		
		return view('vendor.calendar',compact('employees','service','booking','employees1','users','vendor_services'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
	}
	
	
	
}
