<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;
use Illuminate\Support\Str;
use PDF;
class ReportsController extends Controller
{
    public function SaleReport()
	{
			if(Session::has('cityadmin'))
			{
			    	$vendors=DB::table('vendor')
                    ->where('enabled',1)
                    ->get();
    				$vendorhtml='<option value="">Filter by vendor</option>';
    				foreach($vendors as $vendor)
    				{
    				
    					$vendorhtml.='<option value="'.$vendor->vendor_name.'">'.$vendor->vendor_name.'</option>';
    				}
					$booking = DB::table('orders')->select('orders.*','orders.id as orderId','vendor.*','users.*','areas.*')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')->join('users', 'orders.user_id', '=','users.id')->join('areas', 'vendor.area', '=','areas.Area_id')->get();
					 $orders1 = DB::table('orders')->select(
                                DB::raw('sum(total_price) as sums'), 
                                  DB::raw('COUNT(*) as sale_count'),
                                DB::raw("DATE_FORMAT(service_date,'%m') as monthKey")
                                              )
                              ->whereYear('service_date', date('Y'))
                              ->where('payment_status','Paid')
                              ->groupBy('service_date')
                              ->orderBy('service_date', 'desc')
                              ->get();
                       // Initialize an array with default values for each month
                    $ordertotals = array_fill(0, 12, ['sums' => 0, 'sale_count' => 0]);
                    
                    // Populate $ordertotals with actual values from $orders1
                    foreach ($orders1 as $order) {
                        $monthIndex = $order->monthKey - 1; // Convert month to zero-indexed
                        $ordertotals[$monthIndex] = [
                            'sums' => $order->sums,
                            'sale_count' => $order->sale_count,
                        ];
                    }
                    
                    // Optional: Pass $ordertotals as JSON directly if needed
                    $ordertotalsJson = json_encode($ordertotals);
					return view('admin.salereport',compact('booking','vendorhtml','ordertotalsJson'));
			}
			else
			{
				return redirect()->route('login')->withErrors('please login first');
			}
		
	}
	
	public function VendorReport()
	{
			if(Session::has('cityadmin'))
			{	
			    	$vendors=DB::table('vendor')
                    ->where('enabled',1)
                    ->get();
    				$vendorhtml='<option value="">Filter by vendor</option>';
    				foreach($vendors as $vendor)
    				{
    				
    					$vendorhtml.='<option value="'.$vendor->vendor_name.'">'.$vendor->vendor_name.'</option>';
    				}
			    $vendor =DB::table('orders')
    ->selectRaw('vendor.vendor_name,vendor.vendor_email,vendor.vendor_phone,vendor.vendor_id,vendor.enabled,vendor.vendor_logo, sum(`total_price`) as total,count(orders.id) as counter')
    ->join('vendor', 'orders.vendor_id', '=', 'vendor.vendor_id')
    ->groupBy('vendor.vendor_id')
   
    ->get();
   $vendortotalsJson = []; // Array to hold data for all vendors

foreach($vendors as $key=>$vend) {
    // Query to get monthly sales totals and counts for the current vendor
    $orders1 = DB::table('orders')->select(
                    DB::raw('SUM(total_price) as sums'), 
                    DB::raw('COUNT(*) as sale_count')
                )
                ->whereYear('service_date', date('Y'))
                 ->where('payment_status','Paid')
                ->where('vendor_id', $vend->vendor_id)
                ->get();

    // Populate $ordertotals with actual values from $orders1
    foreach ($orders1 as $order) {
        $ordertotals[$key] = [
            'sums' => !empty($order->sums)?$order->sums:0,
            'sale_count' => $order->sale_count,
        ];
    }
    $vendor_names[$key]=$vend->vendor_name;
}
$vendor_count=count($vendors);
$vendor_name=json_encode($vendor_names);
// Convert the full array to JSON if needed for use in JavaScript
$vendortotalsJson = json_encode($ordertotals);
					return view('admin.vendorreport',compact('vendor','vendorhtml','vendortotalsJson','vendor_name','vendor_count'));
			}
			else
			{
				return redirect()->route('login')->withErrors('please login first');
			}
		
	}
	
	
	public function UserReport()
	{
			if(Session::has('cityadmin'))
			{
			    	$vendors=DB::table('vendor')
                    ->where('enabled',1)
                    ->get();
    				$vendorhtml='<option value="">Filter by vendor</option>';
    				foreach($vendors as $vendor)
    				{
    				
    					$vendorhtml.='<option value="'.$vendor->vendor_name.'">'.$vendor->vendor_name.'</option>';
    				}
    				$users =DB::table('orders')
                    ->selectRaw('users.name,orders.user_id,users.email,users.user_phone,users.block, sum(`total_price`) as total,count(orders.id) as counter')
                    ->join('users', 'orders.user_id', '=', 'users.id')
                  
                    ->groupBy('users.id')
                   
                    ->get();
					
					return view('admin.userreport',compact('users','vendorhtml'));
			}
			else
			{
				return redirect()->route('login')->withErrors('please login first');
			}
		
	}
	public function sale_export(Request $request){
	    return PDF::loadView('admin.salereport',[],[],[])->download('sale_report.pdf');
	}
}
