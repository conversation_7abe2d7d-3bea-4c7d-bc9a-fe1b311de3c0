<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;
use File;

class SplashScreenController extends Controller
{
     public function AddSplashScreen()
	{
		if(Session::has('cityadmin'))
		{
		$splashscreens = DB::table('splashscreens')->get();
		return view('admin.addsplash',compact('splashscreens'));
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}	
	}
	
	 public function EditSplashScreen(Request $request)
	{
		if(Session::has('cityadmin'))
		{
		$splashscreens = DB::table('splashscreens')->get();
		$splashscreensedit = DB::table('splashscreens')->where('splash_id',$request->id)->first();
		return view('admin.editsplash',compact('splashscreens','splashscreensedit'));
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}	
	}
	
	 public function SaveSplashScreen(Request $request)
	{
		if(Session::has('cityadmin'))
		{
			$splash_title=$request->splash_title;
		 $splash_description=$request->splash_description;
		 $splash_titlear=$request->splash_titlear;
		 $splash_descriptionar=$request->splash_descriptionar;
		 $order1=$request->order1;
		 $splashimage=$request->splash_imagesfeatured;
		 $splashimages=rtrim($splashimage,",");
		  $this->validate(
         $request,
         [		
         		'order1'=>'required',
				'splash_imagesfeatured'=>'required',
				
				
         		
         ]

);
$date1=date('Y-m-d');
	$insert = DB::table('splashscreens')
    				->insertGetId(['splash_image'=>$splashimages,'splash_title'=>$splash_title,'splash_description'=> $splash_description,'splash_titlear'=>$splash_titlear,'splash_descriptionar'=> $splash_descriptionar,'enabled'=>1,'date1'=>$date1,'order1'=>$order1]);
					 if($insert!=''){
return redirect()->back()->with('message', 'Added Successfully');
					 }
else { 
 return redirect()->back()->withErrors('Nothing to Add');
}					 
		
		
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}	
	}
	
	
	 public function UpdateSplashScreen(Request $request)
	{
		if(Session::has('cityadmin'))
		{
			$splash_title=$request->splash_title;
		 $splash_description=$request->splash_description;
		 $splash_titlear=$request->splash_titlear;
		 $splash_descriptionar=$request->splash_descriptionar;
		 $order1=$request->order1;
		 $splashimage=$request->splash_imagesfeatured;
		 $splashimages=rtrim($splashimage,",");
		  $this->validate(
         $request,
         [		
         		'order1'=>'required',
				
				
				
         		
         ]

);
if($splashimage!='')
{
$update = DB::table('splashscreens')->where('splash_id',$request->id)
    				->update(['splash_image'=>$splashimages]);	
}
$date1=date('Y-m-d');
	$insert = DB::table('splashscreens')->where('splash_id',$request->id)
    				->update(['splash_title'=>$splash_title,'splash_description'=> $splash_description,'splash_titlear'=>$splash_titlear,'splash_descriptionar'=> $splash_descriptionar,'date1'=>$date1,'order1'=>$order1]);
					 if($insert!=''){
return redirect()->back()->with('message', 'Updated Successfully');
					 }
else { 
 return redirect()->back()->withErrors('Nothing to Update');
}					 
		
		
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}	
	}
	
	public function DeleteSplash(Request $request)
	{
		
		 if(Session::has('cityadmin'))
     {   
    
       

        

       

    	$delete=DB::table('splashscreens')->where('splash_id',$request->id)->delete();
        if($delete)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Delete' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }
		
		
	}
	
	public function EnableSplash(Request $request)
	{
		
		 if(Session::has('cityadmin'))
     {   
    
        $splashid=$request->id;

        $getfile=DB::table('splashscreens')
                ->where('splash_id',$request->id)
                ->first();
	
       if($getfile->enabled==1)
	   {
		   $enable=0;
	   }
	    if($getfile->enabled==0)
	   {
		   $enable=1;
	   }

    	$update = DB::table('splashscreens')
                                ->where('splash_id', $request->id)
                                ->update(['enabled'=>$enable]);
        if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Enable' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull Enable'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }
		
		
	}
	
	
}
