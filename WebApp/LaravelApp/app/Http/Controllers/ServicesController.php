<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;

class ServicesController extends Controller
{
    	public function Services(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$services = DB::table('services')->join('servicecategory', 'services.Services_ID', '=','servicecategory.Cat_ID')->get();
    	                   
    	                   
		return view('admin.services',compact('services'));	
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
	}
		
	public function addService(Request $request)
	{
	if(Session::has('cityadmin'))
		{
		$parentcategories = DB::table('servicecategory')->get();
    	                  
    	                   
		return view('admin.add-service',compact('parentcategories'));
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}		
		
		
	}
	
	public function ServiceSave(Request $request)
	
	{
		$created_at = Carbon::Now();
   if(Session::has('cityadmin'))
		{
		$Services_Name=$request->Services_Name;
		$Services_Category=$request->Services_Category;
		
		
			$this->validate($request,[
         		'Services_Name'=>'required',
				'Services_Category'=>'required',
			
         		
         		
         ]

	);
	$date = date('d-m-Y');
	
		
		
			$insert = DB::table('services')
    				->insert(['Services_Name'=>$Services_Name,'Services_Category'=>$Services_Category]);
					 if($insert){

        return redirect()->back()->with('message', 'Added Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }
	
	}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}	
	}
	
	
	public function EditService(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$services = DB::table('services')->where('Services_ID',$request->id)->first();
    	                   
			$parentcategories = DB::table('servicecategory')->get();
    	                  
    	                   
		return view('admin.edit-service',compact('parentcategories','services'));	                   
		
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	public function UpdateService(Request $request)
	
	{
		$created_at = Carbon::Now();
		 if(Session::has('cityadmin'))
		{
  $Services_Name=$request->Services_Name;
		$Services_Category=$request->Services_Category;
		
		
			$this->validate($request,[
         		'Services_Name'=>'required',
				'Services_Category'=>'required',
			
         		
         		
         ]

	);
		
		
			$update = DB::table('services')
                                ->where('Services_ID', $request->id)
                                ->update(['Services_Name'=>$Services_Name,'Services_Category'=>$Services_Category]);
					 if($update){

        return redirect()->back()->with('message', 'Updated Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }
	}
	}
	
	
	
	public function DeleteService(Request $request)
	{
		
		 if(Session::has('cityadmin'))
     {   
    
       

        $getfile=DB::table('services')
                ->where('Services_ID',$request->id)
                ->first();

       

    	$delete=DB::table('services')->where('Services_ID',$request->id)->delete();
        if($delete)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Delete' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }
		
		
	}
	
}
