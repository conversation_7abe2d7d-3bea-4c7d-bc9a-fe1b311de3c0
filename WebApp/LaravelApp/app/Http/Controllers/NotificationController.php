<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;

class NotificationController extends Controller
{
	public function getAccessToken($jsonFilePath) {
    // Load the service account JSON file
    $serviceAccount = json_decode(file_get_contents($jsonFilePath), true);

    $clientEmail = $serviceAccount['client_email'];
    $privateKey = str_replace('\\n', "\n", $serviceAccount['private_key']);
    $scope = 'https://www.googleapis.com/auth/firebase.messaging';

    // Create the JWT header
    $header = json_encode(['alg' => 'RS256', 'typ' => 'JWT']);
    
    // Create the JWT claim set
    $now = time();
    $claimSet = json_encode([
        'iss' => $clientEmail,
        'scope' => $scope,
        'aud' => 'https://oauth2.googleapis.com/token',
        'exp' => $now + 3600, // Token expires in 1 hour
        'iat' => $now,
    ]);

    // Encode the header and claim set
    $base64UrlHeader = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
    $base64UrlClaimSet = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($claimSet));

    // Create the signature
    $signatureInput = $base64UrlHeader . '.' . $base64UrlClaimSet;
    openssl_sign($signatureInput, $signature, $privateKey, OPENSSL_ALGO_SHA256);
    $base64UrlSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

    // Create the JWT
    $jwt = $base64UrlHeader . '.' . $base64UrlClaimSet . '.' . $base64UrlSignature;

    // Prepare the cURL request to get the access token
    $url = 'https://oauth2.googleapis.com/token';
    $data = [
        'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        'assertion' => $jwt,
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
    ]);

    // Execute the request
    $response = curl_exec($ch);
    
    if ($response === false) {
        echo 'Curl error: ' . curl_error($ch);
        return null;
    }

    curl_close($ch);
    
    $responseData = json_decode($response, true);
    return $responseData['access_token'] ?? null; // Return the access token or null
}
      public function adminNotification(Request $request)
    {
        if(Session::has('cityadmin'))
		{
        
                  
           $users = DB::table('users')
             ->select('users.name', 'users.id','users.user_phone')
             ->groupBy('users.name', 'users.id','users.user_phone')
             ->get();  
        return view('admin.send_notification',compact("users"));
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
	
		}
    }
	
	
	  public function adminNotificationSend(Request $request)
    {
        if(Session::has('cityadmin'))
		{
				
				  $this->validate(
            $request,
                [
                'title' => 'required',
                'text' => 'required',
               
                ],
                [
                'title.required' => 'Enter notification title.',
                'text.required' => 'Enter notification text.',
                ]
        );
        $jsonFilePath = storage_path("firebase/bookme-f44a4-c16fbc6aa3e0.json");
 // Replace with your JSON key file path
               $getFcmKey = $this->getAccessToken($jsonFilePath);
                
                
                
                
                $fcmUrl = 'https://fcm.googleapis.com/v1/projects/bookme-f44a4/messages:send';
               
        $date = date('d-m-Y');
         $user = $request->user;
        $countuser = count($user);
        $date = date('d-m-Y');
      
            $url_aws=url('/');
           
       
            $notify_image = "N/A";
      
         

        $notification_title = $request->title;
        $notification_text = $request->text;
        
        $date = date('d-m-Y');
          
        $created_at = Carbon::now();
       
       //$getFcm = DB::table('fcm_key')
                    //->where('unique_id', '1')
                    //->first();
                    
        //$getFcmKey = $getFcm->user_app_key;
        
          if($user[0]=='All'){
              $userin = DB::table('users')
             ->select('users.device_id','users.name','users.id')
             ->get();
          }
          else{
      $userin = DB::table('users')->select('device_id','name','id')
                        ->WhereIn('id', $user)
                        ->get();
          }         
        
        
        

         $get_device_id=array();
        foreach($userin as $us){
        $get_device_id[] = $us;
        }
        $loop =  count(array_chunk($get_device_id,600));  // count array chunk 1000
        $arrayChunk = array_chunk($get_device_id,600);   // devide array in 1000 chunk
        $device_id = array();
        
   $token='';
        for($i=0; $i<$loop ;$i++)
        {
            foreach($arrayChunk[$i] as $all_device_id)
            {       
                   
                        $device_id[] =  $all_device_id->device_id;
						if($all_device_id->device_id!='')
						{
						 $url = 'https://fcm.googleapis.com/v1/projects/bookme-f44a4/messages:send';
            $body=$notification_text;
            $customData=$url;
			
			
			 $data = [
    "message" => [
        "token" => $all_device_id->device_id, // Device FCM token
        "notification" => [
            "title" => $notification_title,
            "body" => $notification_text,
            "image" => $notify_image, // Add the image URL here
        ],
        
        "apns" => [
            "headers" => [
                "apns-collapse-id" => "solo_changed_administrator",
                "content-available" => "1",
                "apns-priority" => "10",
            ],
            "payload" => [
                "aps" => [
                    "sound" => "default", // Default sound for iOS
                    "badge" => 0, // Set a specific badge number
                ]
            ]
        ],
    ]
];	
			
			
            
       
        //api_key in Firebase Console -> Project Settings -> CLOUD MESSAGING -> Server key
        $server_key = $getFcmKey;
        //header with content_type api key
        $headers = [
            "Authorization: Bearer $server_key",
            'Content-Type: application/json',
        ];
        // CURL request to route notification to FCM connection server (provided by Google)
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            $result = curl_exec($ch);
            if ($result === FALSE) {
                die('Oops! FCM Send Error: ' . curl_error($ch));
            }
			
            curl_close($ch);
            // unset the array value 
                                    $insertNotification = DB::table('user_notification')
                                    ->insert([
                                        'user_id'=>$all_device_id->id,
                                        'noti_title'=>$notification_title,
                                         'image'=>$notify_image,
                                        'noti_message'=>$notification_text,
                                      
                                    ]);
						}
            }
            

        }
        return redirect()->back()->withSuccess('Notification Sent to user Successfully');
                  
         
         
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
	
		}
    }
	
	
}
