<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Carbon\Carbon;
use App\Models\User;
use JWTAuth;
use Tymon\JWTAuth\Exceptions\JWTException;
use Hash;
use App\Traits\SendMail;

class MapsetController extends Controller
{
    public function google_map(Request $request)
    {  
        $mapapi = DB::table('map_api')
                   ->first();
                   
        if($mapapi)   { 
            $message = array('status'=>'1', 'message'=>'Google map api', 'data'=>$mapapi);
            return $message;
        }
        else{
            $message = array('status'=>'1', 'message'=>'data not found', 'data'=>[]);
            return $message;
        }
    }
}
