<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Carbon\Carbon;
use App\Models\User;
use JWTAuth;
use <PERSON>mon\JWTAuth\Exceptions\JWTException;
use Hash;
use App\Traits\SendMail;


class promotionBannerController extends Controller
{
	public function promotionBanner()
	{
    $banners=DB::table('promotionbanner')->join('vendor_services', 'promotionbanner.ServiceID', '=','vendor_services.ServicesID')
                ->select(DB::raw("CONCAT('public/', PromoImage) AS PromoImage"),'title','subtitle','Name','ServicesID')->where('promotionbanner.enabled',1)
                ->get();
                   
        if($banners)   { 
		$message1=__('messages.promotionbanner'); 
            $message = array('status'=>'1', 'message'=>$message1, 'data'=>$banners);
            return $message;
        }
        else{
			$message1=__('messages.promotionbannernotfound'); 
            $message = array('status'=>'1', 'message'=>$message1, 'data'=>[]);
            return $message;
        }
	}
}
