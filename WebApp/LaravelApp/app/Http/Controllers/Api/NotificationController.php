<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Carbon\Carbon;

class NotificationController extends Controller
{
   public function notificationlist(Request $request)
    {  
        $user = $request->user_id;
        $notifyby = DB::table('user_notification')
                ->where('user_id',$user)
                ->orderBy('noti_id')
                ->get();
        
         if(count($notifyby)>0){
            $message = array('status'=>'1', 'message'=>'All Notifications', 'data'=>$notifyby);
            return $message;
            }
        else{
			$message1=__('messages.Notificationlistisemtpy');
            $message = array('status'=>'1', 'message'=>$message1);
            return $message;
        }
    }
    
    public function read_by_user(Request $request)
    {  
        $noti_id = $request->noti_id;
        $notifyby = DB::table('user_notification')
                ->where('noti_id',$noti_id)
                ->update(['read_by_user'=> 1]);
                
         if($notifyby){
            $message = array('status'=>'1', 'message'=>'Read by user','data'=>[]);
            return $message;
            }
        else{
            $message = array('status'=>'1', 'message'=>'Not Found' );
            return $message;
        }
    }
    
     public function mark_all_as_read(Request $request)
    {  
        $user_id = $request->user_id;
        $notifyby = DB::table('user_notification')
                ->where('user_id',$user_id)
                ->update(['read_by_user'=> 1]);
                
         if($notifyby){
            $message = array('status'=>'1', 'message'=>'Marked All as Read','data'=>[]);
            return $message;
            }
        else{
            $message = array('status'=>'1', 'message'=>'Not Found');
            return $message;
        }
    }
    
    
     public function delete_all(Request $request)
    {  
        $user_id = $request->user_id;
        $notifyby = DB::table('user_notification')
                ->where('user_id',$user_id)
                ->delete();
                
         if($notifyby){
			 $message1=__('messages.NotificationDeleted');
            $message = array('status'=>'1', 'message'=>$message1);
            return $message;
            }
        else{
			$message1=__('messages.Notificationlistisemtpy');
            $message = array('status'=>'1', 'message'=>$message1);
            return $message;
        }
    }
    
    
    
    public function notificationsettingsRead(Request $request)
{
    $user_id = $request->user_id;
    $notifyby = DB::table('users')->select('emailnotification','smsnotification','pushnotification')
                ->where('id',$user_id)
               
                ->get();
        
         if(count($notifyby)>0){
            $message = array('status'=>'1', 'message'=>'Notifications Settings', 'data'=>$notifyby);
            return $message;
            }
        else{
            $message = array('status'=>'1', 'message'=>'Notifications Settings');
            return $message;
        }


}
public function notificationsettingsUpdate(Request $request)
{
    $user_id = $request->user_id;
    $emailnotification=$request->emailnotification;
    $smsnotification=$request->smsnotification;
    $pushnotification=$request->pushnotification;

    $notifyby = DB::table('users')
                ->where('id',$user_id)
               
                ->update(['emailnotification'=>$emailnotification,'smsnotification'=>$smsnotification,'pushnotification'=>$pushnotification]);
        
       
            $message = array('status'=>'1', 'message'=>'Notifications Settings Updated');
            return $message;
           


}
 public function RemindernotificationUpdate(Request $request)
{
	$user_id = $request->user_id;
    $reminderenable=$request->reminderenable;
    $hours=$request->hours;
	 

    $notifyby = DB::table('users')
                ->where('id',$user_id)
               
                ->update(['reminderenable'=>$reminderenable,'hours'=>$hours]);
        
       
            $message = array('status'=>'1', 'message'=>'Notifications Settings Updated');
            return $message;
}	
  public function RemindernotificationRead(Request $request)
	{
		$user_id = $request->user_id;
	$notifyby = DB::table('users')->select('reminderenable','hours')
                ->where('id',$user_id)
               
                ->get();
        
         if(count($notifyby)>0){
            $message = array('status'=>'1', 'message'=>'Notifications Settings', 'data'=>$notifyby);
            return $message;
            }
        else{
            $message = array('status'=>'1', 'message'=>'Notifications Settings');
            return $message;
        }
	}	
    
}
