<?php

namespace App\Http\Controllers\Api;

use App\Services\LocalizationService;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Carbon\Carbon;
use App\Models\User;
use JWTAuth;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;
use Hash;
use App\Traits\SendMail;

class ServicesController extends Controller
{
     public function ServicesCategory(Request $request, LocalizationService $localizationService)
    {
		$ServicesCategory = DB::table('servicecategory')->select('Cat_ID','Cat_Name','Cat_Name_ar',DB::raw("CONCAT('public/', Icon) AS Icon"),'Ordering')
                   ->orderBy('Ordering', 'ASC')->get();
				   $ServicesCategories = $ServicesCategory->unique('Cat_Name');
				   $message1=__('messages.All');
				   $Services=['Cat_ID'=>'All','Cat_Name'=>$message1,'Cat_Name_ar'=>$message1,'Icon'=>"public/uploads/all-category.svg",'Ordering'=>1];
				    $ServicesCategories->prepend($Services);
				  
				  
				   $fieldsToReplace = [ 'Cat_Name' => 'Cat_Name_ar'];
                   $ServicesCategories = $localizationService->replaceFieldsForLanguage($ServicesCategories, $request, $fieldsToReplace);

				  
				   $message = array('status'=>'1', 'message'=>'Category List', 'data'=>$ServicesCategories);
				   return $message;
                   
		
	}
	
	public function services(Request $request,LocalizationService $localizationService)
    {
          $lat = $request->lat;
       $lng = $request->lng;
    
        $seachstring= $request->searchstring;
    
        $product = DB::table('vendor_services')
                   ->join('vendor','vendor_services.vendor_id','=','vendor.vendor_id')
                    ->select('Name','Name_ar',DB::raw("CONCAT('public/', Image) AS Image"),DB::raw("6371 * acos(cos(radians(".$lat . ")) 
                    * cos(radians(vendor.lat)) 
                    * cos(radians(vendor.lng) - radians(" . $lng . ")) 
                    + sin(radians(" .$lat. ")) 
                    * sin(radians(vendor.lat))) AS distance"))
                  ->orderBy('distance')
                  ->where('vendor.pstatus',1)
                  ->where('vendor.enabled',1)
                  ->where('vendor_services.Name', 'like', $seachstring.'%')
                  ->paginate(5);
  
       
             
     
           $nearbystore = $product->unique('Name');        
          
       
          $pr = NULL;
        foreach($nearbystore as $store)
        {
            if(15 >= $store->distance)  {  
           
                $pr[] = array('service_name'=>$store->Name,'service_name_ar'=>$store->Name_ar, 'service_image'=>$store->Image); 
            }
            
        } 
         if($pr != NULL){ 
		 $fieldsToReplace = [
        'service_name' => 'service_name_ar',
        
    ];

    $pr = $localizationService->replaceFieldsForLanguage($pr, $request, $fieldsToReplace); 
            $message = array('status'=>'1', 'message'=>'Services Found at your location', 'data'=>$pr);
            return $message;
           }
           else{
			    $message1=__('messages.NoServicesRegistered');
                $message = array('status'=>'1', 'message'=>$message1);
            return $message;
           }
                  
        
    }
	
	
	   public function popular_artist(Request $request, LocalizationService $localizationService)
    {
        $lat = $request->lat;
       $lng = $request->lng;
       $seachstring= $request->searchstring;
	   $gender=$request->gender;
	   /*
       $product = DB::table('staff_profile')
                   ->join('vendor','staff_profile.vendor_id','=','vendor.vendor_id')
                    ->select('vendor.vendor_id','vendor.vendor_name','vendor.vendor_name_ar','vendor.vendor_logo','vendor.delivery_range','vendor.owner','staff_profile.staff_id','staff_profile.staff_name','staff_profile.staff_name_ar','staff_profile.staff_image','staff_profile.designation',DB::raw("6371 * acos(cos(radians(".$lat . ")) 
                    * cos(radians(vendor.lat)) 
                    * cos(radians(vendor.lng) - radians(" . $lng . ")) 
                    + sin(radians(" .$lat. ")) 
                    * sin(radians(vendor.lat))) AS distance"))
                  ->orderBy('distance')
                 ->where('vendor.pstatus',1)
                  ->where('vendor.enabled',1)
				  ->where('staff_profile.enabled',1)
                  ->where('staff_profile.staff_name', 'like', $seachstring.'%')
                  ->paginate(5);
				  
				 
                  
                  
             */   
             $product = DB::table('staff_profile')
    ->join('vendor', 'vendor.vendor_id', '=','staff_profile.vendor_id' )
    ->select(
        'vendor.vendor_id',
        'vendor.vendor_name',
        'vendor.vendor_name_ar',
        DB::raw("CONCAT('public/',vendor_logo) AS vendor_logo"),
        'vendor.delivery_range',
        'vendor.owner',
        'staff_profile.staff_id',
        'staff_profile.staff_name',
        'staff_profile.staff_name_ar',
        DB::raw("CONCAT('public/',staff_image) AS staff_image"),
        'staff_profile.designation',
        'staff_profile.popular',
        'staff_profile.enabled',
        'vendor.enabled',
        'vendor.pstatus'
    );
	if($gender!='null')
	  {
		  $gend=explode(",",$gender);
		  $gen=[];
		  if(in_array('MEN',$gend))
		  {
			  $gen[]=1;
		  }
		    if(in_array('WOMEN',$gend))
		  {
			  $gen[]=2;
		  }
		   if(in_array('KIDS',$gend))
		  {
			  $gen[]=3;
		  }
		 
				    $product->WhereIn('vendor.shop_type',$gen);
   
                  
                   
	  
	  
	  }
    $product->where('vendor.pstatus', 1)
    ->where('vendor.enabled', 1)
    ->where('staff_profile.enabled', 1)
    ->where('staff_profile.popular', 1)
	->whereRaw('1=1')
	->orderBy('position','asc');
				   
				   
	 //$str=$product->toSql();
	 //dd($str);
                $products=  $product
                  ->paginate(5);
     
           $nearbystore = $products->unique('staff_id');        
          
       
          $pr = NULL;
        foreach($nearbystore as $store)
        {
            //if(15 >= $store->distance)  {  
           
                $pr[] = $store; 
            //}
            
        } 
        
        
          // Use LocalizationService to replace fields based on Accept-Language
    $fieldsToReplace = [
        'vendor_name' => 'vendor_name_ar',
        'staff_name' => 'staff_name_ar',
    ];

         $pr = $localizationService->replaceFieldsForLanguage(collect($pr), $request, $fieldsToReplace);
        
        
        
         if($pr != NULL){ 
 
            $message = array('status'=>'1', 'message'=>'Popular Artists Found at your location', 'data'=>$pr);
            return $message;
           }
       
    
           else{
			   $message1=__('messages.NoServicesRegistered');
                $message = array('status'=>'1', 'message'=>$message1);
            return $message;
           }
                  
    }
	
}
