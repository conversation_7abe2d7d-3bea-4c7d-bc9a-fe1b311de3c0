<?php

namespace App\Http\Controllers\Api;

use App\Services\LocalizationService;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Carbon\Carbon;
use App\Models\User;
use JWTAuth;
use Ty<PERSON>\JWTAuth\Exceptions\JWTException;
use Hash;
use App\Traits\SendMail;
use DateTime;
class VendorController extends Controller
{
	public function getTimeSlot($interval, $start_time, $end_time)
{
    $start = new DateTime($start_time);
    $end = new DateTime($end_time);
    $startTime = $start->format('h:i A');
    $endTime = $end->format('h:i A');
    $i=0;
    $time = [];
	
    while(strtotime($startTime) < strtotime($endTime)){
		
        //$start = $startTime;
        //$end = date('H:i',strtotime('+'.$interval.' minutes',strtotime($startTime)));
       
        
        if(strtotime($startTime) <= strtotime($endTime)){
			
            $time[$i] = $startTime;
            
			 $startTime = date('h:i A',strtotime('+'.$interval.' minutes',strtotime($startTime)));
			$i++;
            //$time[$i] = $end;
        }
		
    }
	 $time[$i]=$endTime;
	
    return $time;
}
	public function getnearbysalons(Request $request, LocalizationService $localizationService)
    {
        $lat = $request->lat;
       $lng = $request->lng;
       $seachstring= $request->searchstring;
	   $gender=$request->gender;
	   /*$nearbystores=NULL;
	   	$nearbystores = DB::table('vendor')
                    ->select('vendor_name','vendor.vendor_id','vendor_email','vendor_phone','vendor_logo', DB::raw("concat(area, ',', block,',',street,',',avenue,',',zipcode) as address"),'lat','lng','opening_time','closing_time','shop_type as type',DB::raw("6371 * acos(cos(radians(".$lat . ")) 
                    * cos(radians(lat)) 
                    * cos(radians(lng) - radians(" . $lng . ")) 
                    + sin(radians(" .$lat. ")) 
                    * sin(radians(lat))) AS distance"))
                  ->orderBy('distance')
				   ->where('vendor_name', '=', $seachstring)
				 
                  ->where('vendor.pstatus',1)
                  ->where('vendor.enabled',1)
				 
   
                  
                  ->paginate(5);
				  $nearbystores1=NULL;
				  
				  */
				  if($gender!='null')
	  {
		  $gend=explode(",",$gender);
		  $gen=[];
		  if(in_array('MEN',$gend))
		  {
			  $gen[]=1;
		  }
		    if(in_array('WOMEN',$gend))
		  {
			  $gen[]=2;
		  }
		   if(in_array('KIDS',$gend))
		  {
			  $gen[]=3;
		  }
		
   
                  
                   
	  
	  
	  }
				  if($seachstring!='')
				  {
				  	$nearbystores = DB::table('vendor')->join('vendor_services','vendor.vendor_id','=','vendor_services.vendor_id')->join('areas','vendor.area','=','areas.Area_id')
                    ->select('vendor_name','vendor_name_ar','vendor.vendor_id','vendor_email','vendor_phone',DB::raw("CONCAT('public/', vendor_logo) AS vendor_logo"), DB::raw("concat(area, ',', block,',',street,',',avenue,',',zipcode) as address"),DB::raw("concat(area, ',', block,',',street_ar,',',avenue_ar,',',zipcode) as addressar"),'lat','lng','opening_time','closing_time','shop_type as type',DB::raw("6371 * acos(cos(radians(".$lat . ")) 
                    * cos(radians(lat)) 
                    * cos(radians(lng) - radians(" . $lng . ")) 
                    + sin(radians(" .$lat. ")) 
                    * sin(radians(lat))) AS distance"))
                  ->orderBy('distance')
				  ->orwhere('vendor_name', '=', $seachstring)
				   ->orwhere('vendor_name_ar', '=', $seachstring)
				   ->orwhere('vendor_services.Name','=', $seachstring)
				     ->orwhere('vendor_services.Name_ar','=', $seachstring)
                  ->where('vendor.pstatus',1)
                  ->where('vendor.enabled',1)
				  ->
				   WhereIn('shop_type',$gen)
				 
   
                  
                  ->paginate(5);
				  }
				  else
				  {
					  
					$nearbystores = DB::table('vendor')->join('vendor_services','vendor.vendor_id','=','vendor_services.vendor_id')->join('areas','vendor.area','=','areas.Area_id')
                    ->select('vendor_name','vendor_name_ar','vendor.vendor_id','vendor_email','vendor_phone',DB::raw("CONCAT('public/', vendor_logo) AS vendor_logo"), DB::raw("concat(Area_Title, ',', block,',',street,',',avenue,',',zipcode) as address"),DB::raw("concat(Area_Title_ar, ',', block,',',street_ar,',',avenue_ar,',',zipcode) as addressar"),'lat','lng','opening_time','closing_time','shop_type as type',DB::raw("6371 * acos(cos(radians(".$lat . ")) 
                    * cos(radians(lat)) 
                    * cos(radians(lng) - radians(" . $lng . ")) 
                    + sin(radians(" .$lat. ")) 
                    * sin(radians(lat))) AS distance"))
                  ->orderBy('distance')
				 
                  ->where('vendor.pstatus',1)
                  ->where('vendor.enabled',1)
				  ->
				   WhereIn('shop_type',$gen)
				 
   
                  
                  ->paginate(5);  
					  
				  }
      
 //$nearbystores2=array_merge($nearbystores,$nearbystores1);
          $pr = NULL;
		  /*
		  if($nearbystores==NULL)
		  {
			$nearbystore = $nearbystores1->unique('vendor_name');  
		  }
		  else
		  {
			$nearbystore = $nearbystores->unique('vendor_name');   
		  }
		  */
		  $nearbystore = $nearbystores->unique('vendor_name');  
        foreach($nearbystore as $store)
        {
            if(15 >= $store->distance)  { 

$store->distance=ceil($store->distance);			
   $store->distance=strval($store->distance);        
                $pr[] = $store; 
            }
            
        }
        
        
        // Use LocalizationService to replace fields dynamically
    $fieldsToReplace = [
        'vendor_name' => 'vendor_name_ar',
        'address' => 'addressar',
    ];
    $pr = $localizationService->replaceFieldsForLanguage(collect($pr), $request, $fieldsToReplace);
        
        
        if($pr != NULL){ 
             $result =array();
            $i = 0;
            $j=0;

            foreach ($pr as $cats) {
                array_push($result, $cats);

                $app = json_decode($cats->vendor_id);
                $apps = array($app);
                $app = DB::table('review')
                        ->whereIn('vendor_id', $apps)
                        ->avg('rating');
                        
                
                if($app){        
                   $result[$i]->rating = number_format(round($app,1),1);
                   $i++; 
                   }
                   else{
                      $result[$i]->rating = (string)0;
                      $i++; 
                   }
                 }
if(count($pr)>=1)
{
	 $message = array('status'=>'1', 'message'=>'Services Found at your location', 'data'=>$pr);
}
else{
	 $message1=__('messages.NoServicesRegistered');
                $message = array('status'=>'1', 'message'=>$message1);
}	
           
            return $message;
           }
           else{
			    $message1=__('messages.NoServicesRegistered');
                $message = array('status'=>'1', 'message'=>$message1);
            return $message;
           }
                  
   

}
	
    public function SearchVendor(Request $request)
	{
	$lat = $request->lat;
    $lng = $request->lng;
	$service=$request->service;
	$gender=$request->gender;
	$times	=$request->times;
	$distance	=$request->distance;
	
	$nearbystore = DB::table('vendor')->join('vendor_services','vendor_services.vendor_id','=','vendor.vendor_id')->join('areas','vendor.area','=','areas.Area_id')
                    ->select('vendor_name','vendor_name_ar','owner','vendor_id','vendor_email','vendor_phone',DB::raw("CONCAT('public/', vendor_logo) AS vendor_logo"),DB::raw("concat(Area_Title, ',', block,',',street,',',avenue,',',zipcode) as address"),DB::raw("concat(Area_Title_ar, ',', block,',',street_ar,',',avenue_ar,',',zipcode) as addressar"),'lat','lng','opening_time','closing_time','shop_type as type',DB::raw("6371 * acos(cos(radians(".$lat . ")) 
                    * cos(radians(lat)) 
                    * cos(radians(lng) - radians(" . $lng . ")) 
                    + sin(radians(" .$lat. ")) 
                    * sin(radians(lat))) AS distance"))
                  ->orderBy('distance')
                  ->where('vendor.pstatus',1)
                  ->where('vendor.enabled',1)
				  ->where('vendor_services.ServicesID',$service)
                    //  ->where('vendor_id','!=', $vendor_id)
                  ->where('shop_type',$gender)
                  ->paginate(5);

          $pr = [];
        foreach($nearbystore as $store)
        {
            if(15 >= $store->distance)  {  
           $store->distance=ceil($store->distance);	
                $pr[] = $store; 
            }
            
        }
		
		if($pr != NULL){ 
            $message = array('status'=>'1', 'message'=>'Vendor Found at your location', 'data'=>$pr);
            return $message;
           }
           else{
			   $message1=__('messages.NoVendorregistered');
                $message = array('status'=>'1', 'message'=>'No Vendor registered at your location');
            return $message;
           }
	
	}
// public function vendorexplore(Request $request, LocalizationService $localizationService)

// 	{
// 		$vend=DB::table('vendor')
//                   ->get();
// 				  foreach($vend as $vendo)
// 				  {
// 				  $opent=strtotime($vendo->opening_time);
// 				  $closet=strtotime($vendo->closing_time);
// 		DB::table('vendor')->where('vendor_id', $vendo->vendor_id)
//                                 ->update
//     				(['opentimestamp'=>
// 				  $opent,'closetimestamp'=>$closet]);
// 				  }
// 		$seachstring= $request->searchstring;
// 		$lat = $request->lat;
//       $lng = $request->lng;
// 	$service=$request->service;
// 	$gender=$request->gender;
// 	$times	=$request->times;
// 	//$distance=$request->distance ?? 10000;
// 	$distance=$request->distance;
// 	$ratings=$request->ratings;
// 	$genders=DB::table('vendortypes')
//                   ->get();
// 		$perPage = $request->results;
		
//   if (!is_numeric($perPage)) {
//     $perPage = 10;
//   }
 
 
 	
		
		
// 	$nearbystores = DB::table('vendor')->join('areas','vendor.area','=','areas.Area_id');
// 	if($ratings!='')
// 	{
// 	$nearbystores->join('review','review.vendor_id','=','vendor.vendor_id');
// 	}
//                     	$nearbystores->select('vendor_name','vendor_name_ar','vendor.vendor_id','vendor_email','vendor_phone','vendor_logo','CatID', DB::raw("concat(vendor_loc,',',Area_Title, ',', block,',',street,',',avenue,',',zipcode) as address"),'lat','lng','opening_time','closing_time','shop_type as type',DB::raw("6371 * acos(cos(radians(".$lat . ")) 
//                     * cos(radians(lat)) 
//                     * cos(radians(lng) - radians(" . $lng . ")) 
//                     + sin(radians(" .$lat. ")) 
//                     * sin(radians(lat))) AS distance"));			
  
   
//  if($seachstring!='')
//   {
	
		
                 
// 				  $nearbystores->where('vendor_name', 'like', '%'.$seachstring.'%');
				   
                 
 
	  
//   }
 
// 	if($lng!='' and $lat!='')
// 	  {
	 
//                   $nearbystores->orderBy('distance');
                 

                 
// 	  }
// 	  if($times!='')
// 	  {
// 		$time=explode(",",$times);
// 		$times1=[];
		
// 		foreach($time as $tim)
// 		{
// 		/*	
// 		$times1=date('H:i',strtotime($tim));
		

//         // Add conditions to the query for each time range
//         $nearbystores->Where(function ($q) use ($times1) {
			
//             $q->where('opening_time',">=", $times1)
//               ->Where('closing_time',"<=", $times1);
              
//         });
// 		*/
		
// 		$times1 = strtotime($tim);

// // Add conditions to the query for each time range
// $nearbystores->where(function ($q) use ($times1) {
//     $q->where('opentimestamp', '<=', $times1)
//       ->orWhere('closetimestamp', '>=', $times1);
// });
    
// 		}
		
// 		/*$nearbystores
// 				   ->whereBetween('opening_time', $times1)
//         ->whereBetween('closing_time', $times1);*/
		
// 	  }
// 	  $servicequery='';
	  
// 	   if($service!='' and $service=='All' )
// 	  {
	
				  
   
                  
                
	  
// 	  }
// 	  else if($service!='')
// 	  {
		  
// 		  $servi=explode(",",$service);
// 		  //dd($servi);
// 		  if($gender!='')
// 		  {
// 			  $nearbystores->join('servicecategory','vendor.CatID','=','servicecategory.Cat_ID')->WhereIn('servicecategory.Cat_ID',$servi); 
// 			  //$nearbystores->join('vendor_servicecategory','vendor.CatID','=','vendor_servicecategory.Cat_ID')->WhereIn('vendor_servicecategory.Cat_ID',$servi); 
		  
// 		  }
// 		  else
// 		  {
// 			$nearbystores->join('servicecategory','vendor.CatID','=','servicecategory.Cat_ID')->WhereIn('servicecategory.Cat_ID',$servi); 
// 			 //$nearbystores->join('vendor_servicecategory','vendor.CatID','=','vendor_servicecategory.Cat_ID')->WhereIn('vendor_servicecategory.Cat_ID',$servi);
// 		  }
// 	  }
// 	  $genderquery='';
// 	  if($gender!='null')
// 	  {
// 		  $gend=explode(",",$gender);
// 		  $gen=[];
// 		  if(in_array('MEN',$gend))
// 		  {
// 			  $gen[]=1;
// 		  }
// 		    if(in_array('WOMEN',$gend))
// 		  {
// 			  $gen[]=2;
// 		  }
// 		   if(in_array('KIDS',$gend))
// 		  {
// 			  $gen[]=3;
// 		  }
// 		 $nearbystores->
// 				   WhereIn('shop_type',$gen);
   
                  
                   
	  
	  
// 	  }
	  
	  
// 		// $nearbystore=$nearbystores->where('vendor.pstatus',1)->where('vendor.enabled',1)->distinct('vendor.vendor_id')->toSql();
// 		  //dd($nearbystore);
                  
// 			 $nearbystore=$nearbystores->where('vendor.pstatus',1)->where('vendor.enabled',1)->distinct('vendor.vendor_id')->paginate($perPage);	
   
                  
                
				
                  
                

			
// 				  //$nearbystore = array_unique($nearbystore);
				  
// 				  //$nearbystore->unique('vendor_name');
//     $pr = [];
//     $i=0;

//     $ratingValues = [];
//     if ($ratings != '') {
//         $ratingValues = array_map('intval', explode(',', $ratings));
//     }

				
//         foreach($nearbystore as $store)
//         {
			
// 			 $app = DB::table('review')
//                         ->where('vendor_id', $store->vendor_id)
//                         ->avg('rating');
//                         if($ratings!='')
//                         {
// 			if(ceil($app)==$ratings)
// 			{		
			
// 			if($distance!='')
// 			{
				
			 
//              if($distance>=$store->distance) 
// 				 { 
			 
// 				if($app)
// 				{
// 				$store->rating= number_format(round($app,1),1);	
// 				}
// 				else
// 				{
// 				$store->rating= number_format(0,1);	
// 				}
				
// 				 $store->distance=number_format($store->distance,0);
// 				 $pr[] = $store; 
// 			 }
			 
// 			}
// 			else {
// 				if($app)
// 				{
// 				$store->rating= number_format(round($app,1),1);	
// 				}
// 				else
// 				{
// 				$store->rating= number_format(0,1);	
// 				} 
			
// 				$store->distance=number_format($store->distance,0);
// 			 $pr[] = $store; 	 
// 			 }}
// 			 else {
// 				if($app)
// 				{
// 				$store->rating= number_format(round($app,1),1);	
// 				}
// 				else
// 				{
// 				$store->rating= number_format(0,1);	
// 				} 
			
// 				$store->distance=number_format($store->distance,0);
// 			 $pr[] = $store; 	 
// 			 }
//                         }
// 			 else {
// 				if($app)
// 				{
// 				$store->rating= number_format(round($app,1),1);	
// 				}
// 				else
// 				{
// 				$store->rating= number_format(0,1);	
// 				} 
			
// 				$store->distance=number_format($store->distance,0);
// 			 $pr[] = $store; 	 
// 			 }
			
		   
        
			
		   
//         }
		
// 		  // Use LocalizationService to replace fields dynamically
//     $fieldsToReplace = [
//         'vendor_name' => 'vendor_name_ar'];
//     $pr = $localizationService->replaceFieldsForLanguage(collect($pr), $request, $fieldsToReplace);
		
//         if($pr != NULL){ 
//              $result =array();
//             $i = 0;
//             $j=0;

          
// 		$Services=['ServicesID'=>'All','Name'=>'All','Image'=>""];

// $ServicesCategory = DB::table('vendor_services')->select('ServicesID','Name','Image')
//                   ->orderBy('Name', 'ASC')->get();
// 				   $ServicesCategory->prepend($Services);
				  
// 				  if($pr!='')
// 				  {
// 					 $pr=collect($pr)->unique('vendor_id');
//             $message = array('status'=>'1', 'message'=>'Salons Found', 'data'=>$pr);
//             return $message;
// 				  }
// 				  else
// 				  {
// 					 $message = array('status'=>'1', 'message'=>'No Services Found');
//             return $message;  
// 				  }
//           }
//           else{
		  
				  
//                 $message = array('status'=>'1', 'message'=>'No Services Found');
//             return $message;
//           }
		
		
	
// 		/*
// 				$nearbystores1="DB::table('vendor')->join('vendor_services','vendor.vendor_id','=','vendor_services.vendor_id')->join('areas','vendor.area','=','areas.Area_id')
//                     ->select('vendor_name','vendor.vendor_id','vendor_email','vendor_phone','vendor_logo', DB::raw('concat(vendor_loc,',',Area_Title, ',', block,',',street,',',avenue,',',zipcode) as address'),'lat','lng','opening_time','closing_time','shop_type as type',DB::raw('6371 * acos(cos(radians(".$lat . ")) 
//                     * cos(radians(lat)) 
//                     * cos(radians(lng) - radians(" . $lng . ")) 
//                     + sin(radians(" .$lat. ")) 
//                     * sin(radians(lat))) AS distance'))";
// 					 $nearbystores1.="->where('vendor.pstatus',1)
//                   ->where('vendor.enabled',1)";
				  
				  
				  
//   if($seachstring=='All')
//   {
	
				 
               
				 
   
                  
                 
//   }
//  else if($seachstring!='')
//   {
	
// 		$nearbystores1.="
                 
// 				  ->orwhere('vendor_name', '=', $seachstring)
// 				   ->orwhere('vendor_services.Name','=', $seachstring)
//                  ";
				 
   
                  
                 
 
	  
//   }
 
	 
// 	  $servicequery='';
// 	  if($service!='')
// 	  {
// 		 $nearbystores1.="
				 
                  
// 				  ->where('vendor_services.Service_Category',$service)";
   
                  
                 
	  
// 	  }
// 	  $genderquery='';
// 	  if($gender!='')
// 	  {
// 		 $nearbystores1.="
				 
                 
// 				   ->where('shop_type',$gender)"
   
                  
//                  ; 
	  
	  
// 	  }
	  
// 		 $nearbystores1.="->distinct('vendor.vendor_id')";  
                
// $nearbystores=$nearbystores1."->paginate($perPage)";
// 			dd($nearbystores);
// 				  $nearbystore = array_unique(($nearbystores));
				  
// 				  //$nearbystores->unique('vendor_name');
// $pr = [];
// $i=0;
			  
//         foreach($nearbystores as $store)
//         {
			
// 			 $app = DB::table('review')
//                         ->where('vendor_id', $store[$i]->vendor_id)
//                         ->avg('rating');
					
// 			if($distance!='')
// 			{
				
//              if($distance >=$store[$i]->distance) 
// 				 { 			
// 				if($app)
// 				{
// 				$store[$i]->rating= number_format(round($app,1),1);	
// 				}
// 				else
// 				{
// 				$store[$i]->rating= number_format(0,1);	
// 				}
				
// 				 $store[$i]->distance=number_format($store[$i]->distance,2);
// 				 $pr[] = $store; 
// 			 }
			 
// 			}
// 			else {
// 				if($app)
// 				{
// 				$store[$i]->rating= number_format(round($app,1),1);	
// 				}
// 				else
// 				{
// 				$store[$i]->rating= number_format(0,1);	
// 				} 
			
// 				$store[$i]->distance=number_format($store[$i]->distance,2);
// 			 $pr[] = $store; 	 
// 			 }
//           $i++; 
//         }
//         if($pr != NULL){ 
//              $result =array();
//             $i = 0;
//             $j=0;

          
// 		$Services=['ServicesID'=>'All','Name'=>'All','Image'=>""];

// $ServicesCategory = DB::table('vendor_services')->select('ServicesID','Name','Image')
//                   ->orderBy('Name', 'ASC')->get();
// 				   $ServicesCategory->prepend($Services);
				  
// 				  if($pr!='')
// 				  {
// 					 $pr=collect($pr)->unique('vendor_id');
//             $message = array('status'=>'1', 'message'=>'Salons Found', 'data'=>$pr);
//             return $message;
// 				  }
// 				  else
// 				  {
// 					 $message = array('status'=>'1', 'message'=>'No Salons Found');
//             return $message;  
// 				  }
//           }
//           else{
		  
				  
//                 $message = array('status'=>'1', 'message'=>'No Salons Found');
//             return $message;
//           }
// 		   */
		
		
// 	}

public function vendorexplore(Request $request, LocalizationService $localizationService)
{
    // Update vendor open/close timestamps
    $vendors = DB::table('vendor')->get();
    foreach ($vendors as $vendor) {
        DB::table('vendor')->where('vendor_id', $vendor->vendor_id)->update([
            'opentimestamp' => strtotime($vendor->opening_time),
            'closetimestamp' => strtotime($vendor->closing_time)
        ]);
    }

    // Request parameters
    $lat = $request->lat;
    $lng = $request->lng;
    $searchString = $request->searchstring;
    $service = $request->service;
    $gender = $request->gender;
    $times = $request->times;
    $distance = $request->distance;
    $ratings = $request->ratings;
    $perPage = is_numeric($request->results) ? $request->results : 10;

    // Parse max rating from parameter
    $maxAllowedRating = null;
    if (!empty($ratings)) {
        $ratingValues = array_map('intval', explode(',', $ratings));
        $maxAllowedRating = max($ratingValues);
    }

    // Base query
    $query = DB::table('vendor')->join('areas', 'vendor.area', '=', 'areas.Area_id');

    // ✅ Use LEFT JOIN to include vendors with no reviews
    if (!empty($ratings)) {
        $query->leftJoin('review', 'review.vendor_id', '=', 'vendor.vendor_id');
    }

    $query->select(
        'vendor_name',
        'vendor_name_ar',
        'vendor.vendor_id',
        'vendor_email',
        'vendor_phone',
		'cod',
        DB::raw("CONCAT('public/', vendor_logo) AS vendor_logo"),
        'CatID',
        DB::raw("CONCAT(vendor_loc, ',', Area_Title, ',', block, ',', street, ',', avenue, ',', zipcode) AS address"),
        'lat',
        'lng',
        'opening_time',
        'closing_time',
        'shop_type AS type',
        DB::raw("6371 * ACOS(COS(RADIANS($lat)) 
            * COS(RADIANS(lat)) 
            * COS(RADIANS(lng) - RADIANS($lng)) 
            + SIN(RADIANS($lat)) 
            * SIN(RADIANS(lat))) AS distance")
    );

    // Apply filters
    if (!empty($searchString)) {
        $query->where('vendor_name', 'like', "%$searchString%");
    }

    if (!empty($lat) && !empty($lng)) {
        $query->orderBy('distance');
    }

    if (!empty($times)) {
        foreach (explode(',', $times) as $time) {
            $timestamp = strtotime($time);
            $query->where(function ($q) use ($timestamp) {
                $q->where('opentimestamp', '<=', $timestamp)
                    ->orWhere('closetimestamp', '>=', $timestamp);
            });
        }
    }

    if (!empty($service) && $service !== 'All') {
        $serviceIds = explode(',', $service);
        $query->join('servicecategory', 'vendor.CatID', '=', 'servicecategory.Cat_ID')
              ->whereIn('servicecategory.Cat_ID', $serviceIds);
    }

    if (!empty($gender) && $gender !== 'null') {
        $genderList = explode(',', $gender);
        $genderCodes = [];

        if (in_array('MEN', $genderList)) $genderCodes[] = 1;
        if (in_array('WOMEN', $genderList)) $genderCodes[] = 2;
        if (in_array('KIDS', $genderList)) $genderCodes[] = 3;

        if (!empty($genderCodes)) {
            $query->whereIn('shop_type', $genderCodes);
        }
    }

    $query->where('vendor.pstatus', 1)
          ->where('vendor.enabled', 1)
          ->distinct('vendor.vendor_id');

    $vendors = $query->paginate($perPage);

    // Filter by rating & distance
    $filteredVendors = [];

    foreach ($vendors as $vendor) {
        // Force rating: treat null as 0.0
        $maxRating = DB::table('review')->where('vendor_id', $vendor->vendor_id)->max('rating');
        $numericRating = is_null($maxRating) || !is_numeric($maxRating) ? 0.0 : floatval($maxRating);
        $vendor->rating = number_format($numericRating, 1);

        // Force distance: remove comma, convert to float
        $rawDistance = is_string($vendor->distance) ? str_replace(',', '', $vendor->distance) : $vendor->distance;
        $numericDistance = floatval($rawDistance);
        $vendor->distance = number_format($numericDistance, 0);

        // Filter by max rating
        if (!is_null($maxAllowedRating) && $numericRating > $maxAllowedRating) {
            continue;
        }

        // Filter by distance
        if (!empty($distance) && $numericDistance > floatval($distance)) {
            continue;
        }

        $filteredVendors[] = $vendor;
    }

    // Localize vendor name (if Arabic)
    $fieldsToReplace = ['vendor_name' => 'vendor_name_ar'];
    $filteredVendors = $localizationService->replaceFieldsForLanguage(collect($filteredVendors), $request, $fieldsToReplace);

    // Prepend "All" service (optional UI support)
    $servicesList = DB::table('vendor_services')
        ->select('ServicesID', 'Name', DB::raw("CONCAT('public/', Image) AS Image"))
        ->orderBy('Name', 'ASC')
        ->get()
        ->prepend(['ServicesID' => 'All', 'Name' => 'All', 'Image' => ""]);

    // Return response
    if (!empty($filteredVendors)) {
        return [
            'status' => '1',
            'message' => 'Salons Found',
            'data' => collect($filteredVendors)->unique('vendor_id')->values()
        ];
    } else {
        return [
            'status' => '1',
            'message' => 'No Services Found'
        ];
    }
}
	
	
	
	public function mapsalons(Request $request)
	{
		$lat = $request->lat;
       $lng = $request->long;
	    $dis = $request->distance;
	$vendors = DB::table('vendor')->join('areas','vendor.area','=','areas.Area_id')->select('vendor_name','vendor.vendor_id','vendor_email','vendor_phone',DB::raw("CONCAT('public/', vendor_logo) AS vendor_logo"),'lat','lng','opening_time','closing_time','shop_type as type', DB::raw("concat(vendor_loc,',',Area_Title, ',', block,',',street,',',avenue,',',zipcode) as address"),DB::raw("6371 * acos(cos(radians(".$lat . ")) 
                    * cos(radians(lat)) 
                    * cos(radians(lng) - radians(" . $lng . ")) 
                    + sin(radians(" .
		$lat. ")) 
                    * sin(radians(lat))) AS distance"))
                  ->where('vendor.pstatus',1)
                  ->where('vendor.enabled',1)->get();
				  
				  $pr = [];
$i=0;				  
        foreach($vendors as $store)
        {
			 if($dis >= $store->distance)  { 	
			 $app = DB::table('review')
                        ->where('vendor_id', $store->vendor_id)
                        ->avg('rating');
		
				$store->distance=number_format($store->distance,2);
            		
				if($app)
				{
				$store->rating= number_format(round($app,1),1);	
				}
				else
				{
				$store->rating= number_format(0,1);	
				}
                $pr[] = $store; 
			
			 
			
			 }
        }
				   $data=array('vendors'=>$pr);   
				   $message = array('status'=>'1', 'message'=>'Map Salons','data'=>$data );
					return $message;
	}
	public function filters(Request $request)
	{
	
	$vendors = DB::table('vendor')
                  ->where('vendor.pstatus',1)
                  ->where('vendor.enabled',1)->get();
				  $slotnew=[];
				 $slots='';
				  $date=date('Y-m-d');
				  $day = date('l', strtotime($date));
				  foreach($vendors as $vendor)
				  {
					
					 $time=DB::table('time_slot')->where('vendor_id',$vendor->vendor_id)->where('status',1)->where('days',$day)->first();
		if($time!=null)
		{
		
		$openhour=$time->open_hour;
		$closehour=$time->close_hour;
		
		if($openhour!='' and $closehour!='')
		{
			
		$slots=$this->getTimeSlot(30,$openhour,$closehour);
		
		}
		else
			
		{
		$slots="No Slots";
				
		}
		/*
		$bookings=DB::table('order_cart')->join('orders', 'orders.id', '=','order_cart.book_ID')
                ->where('order_cart.vendor_id',$vendor->vendor_id)
                ->get();
				
				foreach($bookings as $booking)
				{
					$times=$booking->service_time;
					$timesslots=explode("-",$times);
					$timesslot1=$timesslots[0];
					$timesslot2=$timesslots[1];
					$timesslot1=date("H:i", strtotime($timesslot1));
					$timesslot2=date("H:i", strtotime($timesslot2));
					$slotnew=$this->getTimeSlot(30,$timesslot1,$timesslot2);
				}
				
				foreach($slots as $slot)
				{
					if (false !== $key = array_search($slot, $slotnew)) {
					 unset($slots[$key]);	
					}
				
				}
				*/
		}
		
				  }
				  
				//if($slots!='')
				//{
				//$slots=array_unique($slots);
				//}
				
//$Services=['ServicesID'=>'All','Name'=>'All','Image'=>""];

$ServicesCategory = DB::table('servicecategory')->select('Cat_ID','Cat_Name','Cat_Name_ar','Icon')
                   ->orderBy('Ordering', 'ASC')->get();
				   //$ServicesCategory->prepend($Services);
$distanceminmax=array('min'=>1,'max'=>200);
$genders=DB::table('vendortypes')
                  ->get();
				  $ratings=array(1,2,3,4,5);
$data=array('categories'=>$ServicesCategory,'timeslots'=>$slots,'distance'=>$distanceminmax,'genders'=>$genders,'ratings'=>$ratings);
 $message = array('status'=>'1', 'message'=>'Filters','data'=>$data );
            return $message;				  
		
		
		
	}
	public function filterservicecategories(Request $request)
	{
		$category=$request->Cat_ID;
		$vendors=DB::table('vendor')->select('vendor_name','vendor_name_ar','vendor_id')
                   ->where('CatID',$category)
                ->get();
				$ServicesCategories=[];
				foreach($vendors as $vendor)
				{
	$ServicesCategory = DB::table('vendor_servicecategory')->select('Cat_Name','Cat_Name_ar','Cat_ID')->where('vendor_id',$vendor->vendor_id)->get();
                   
				$ServicesCategories=$ServicesCategory;   
				   
				}
				$data=array('servicecategories'=>$ServicesCategories);
			$message = array('status'=>'1', 'message'=>'Service Category','data'=>$data );	
		return $message;
		
	}
	

	
	
	 public function VendorDetail(Request $request)
	{
	$vendor_id = $request->vendor_id;
	$lat = $request->lat;
       $lng = $request->lng;
	$servicecategory=$request->Service_Category;
	$user_id=$request->user_id;
	$description=DB::table('vendor')->join('areas','vendor.area','=','areas.Area_id')->select('calendarschedule','vendor_name','vendor_name_ar','cod','vendor_email','vendor_phone',DB::raw("CONCAT('public/', vendor_logo) AS vendor_logo"),'lat','lng','description','opening_time','closing_time','paymentcancelpolicy',DB::raw("concat(vendor_loc,',',Area_Title_ar, ',', block,',',street_ar,',',avenue_ar,',',zipcode) as address_ar"),DB::raw("concat(vendor_loc,',',Area_Title, ',', block,',',street,',',avenue,',',zipcode) as address"),'instagram','facebook','YouTube','X','LinkedIn','Snapchat','Pininterest','Wechat','Tiktok',DB::raw("6371 * acos(cos(radians(".$lat . ")) 
                    * cos(radians(lat)) 
                    * cos(radians(lng) - radians(" . $lng . ")) 
                    + sin(radians(" .$lat. ")) 
                    * sin(radians(lat))) AS distance")
                 )->where('vendor_id', $vendor_id)
                     ->first();
     $rating = DB::table('review')
                 ->where('vendor_id', $vendor_id)
                 ->avg('rating');
				 if($user_id!='')
				 {
				 $wishlist = DB::table('wishlists')
                 ->where('vendor_id', $vendor_id)
				 ->where('user_id', $user_id)
                 ->count();
				 }
				 else 
				 {
				 $wishlist=0;	 
				 }
	 $reviewcount = DB::table('review')
                 ->where('vendor_id', $vendor_id)
                 ->count();	

$fivestarcount = DB::table('review')
                 ->where('vendor_id', $vendor_id)->where('rating', 5)
                 ->count();

$fourstarcount = DB::table('review')
                 ->where('vendor_id', $vendor_id)->where('rating', 4)
                 ->count();

$threestarcount = DB::table('review')
                 ->where('vendor_id', $vendor_id)->where('rating', 3)
                 ->count();

$twostarcount = DB::table('review')
                 ->where('vendor_id', $vendor_id)->where('rating', 2)
                 ->count();	

$onestarcount = DB::table('review')
                 ->where('vendor_id', $vendor_id)->where('rating', 1)
                 ->count();				 
					 
$review = DB::table('review')
                 ->join('users','review.user_id','=','users.id')
                 ->select('review.*', 'users.name',DB::raw("CONCAT('public/',users.image) AS image"))
                 ->where('vendor_id',$vendor_id)
                  ->get(); 
                  
if (!$review->isEmpty()) {
    //  review data with isLiked and isReported fields
    foreach ($review as $rev) {
        $isLiked = DB::table('likesreviews')
            ->where('user_id', $user_id)
            ->where('review_id', $rev->id)
            ->exists();

        $isReported = DB::table('reporreviews')
            ->where('user_id', $user_id)
            ->where('review_id', $rev->id)
            ->exists();

        // Add the fields to the review object
        $rev->isLiked = $isLiked ? true : false;
        $rev->isReported = $isReported ? true : false;
    }
}
                  
$staffs1 = DB::table('staff_profile')->select('staff_id', 'staff_name', 'staff_name_ar', DB::raw("CONCAT('public/',staff_image) AS staff_image"), 'email', 'phone', 'giveservices', 'staff_description', 'vendor_id', 'created_at', 'en_staff_description', 'staff_pass', 'enabled', 'designation', 'popular', 'position', 'breaktime')
                 ->where('vendor_id',$vendor_id)->where('enabled',1)
                  ->get();
				  $ameneties=DB::table('vendor_ameneties')->join('ameneties','ameneties.Am_ID','=','vendor_ameneties.ameneties_id')->select(DB::raw("CONCAT('public/',AmentiesImage) AS AmentiesImage"),'AmentiesName','AmentiesName_ar')
                 ->where('vendor_id',$vendor_id)
                  ->get();
				   if($servicecategory=='All' or $servicecategory=='')
				 {
$services = DB::table('vendor_services')
                  ->where('vendor_id',$vendor_id)
                  ->get();
			  
 }
 else {
$services = DB::table('vendor_services')
                  ->where('vendor_id',$vendor_id)->where('Service_Category',$servicecategory)
                  ->get();	 
	 
 }
 foreach ($services as $key => $service) {
    if ($service->event == 1) {
        // Get current date and time
        $now = Carbon::now();
        $today = $now->format('Y-m-d');
        $currentTime = $now->format('h:i A'); // matches '11:30 AM' format

        // Get schedule for today
        $schedule = DB::table('vendor_service_schedule')
    ->where('service_id', $service->ServicesID)
    ->whereRaw("
        STR_TO_DATE(CONCAT(service_date, ' ', service_time_from), '%Y-%m-%d %h:%i %p') >= ?
    ", [Carbon::now()])
    ->orderBy('service_date')
    ->orderBy('service_time_from')
    ->first();

        // Show only if currently within time range
        if ($schedule) {
            //$service->event_schedule = $this->buildEventScheduleInline($service->ServicesID);
        } else {
            unset($services[$key]); // Remove if not within time
        }
    }
}
 $portfolio = DB::table('vendor_photos')->select('Vendor_photoID','vendor_id',DB::raw("CONCAT('public/', photo) AS photo"),'likes')  
         ->where('vendor_id', $vendor_id)
                 ->get();
        $weektime= DB::table('time_slot')
                 ->where('vendor_id', $vendor_id)
                 ->get();				  
  
		$serv=[];
		 foreach ($services as $service) {
			$staffs = DB::table('staff_profile')->select('staff_id','staff_name',DB::raw("CONCAT('public/',staff_image) AS staff_image"),'staff_description','designation')
                 ->where('staff_id',$service->Staff_ID)->where('enabled',1)
                  ->get();
$service->Staff=$staffs;
$serv[]=$service;				  
		 }
		 $social=[];
		 if($description->instagram!='')
		 {
			$social[]=array('icon'=>'public/uploads/social/insta.png','link'=>$description->instagram); 
		 }
		 if($description->facebook!='')
		 {
			$social[]=array('icon'=>'public/uploads/social/facebook.png','link'=>$description->facebook); 
		 }
		 if($description->YouTube!='')
		 {
			$social[]=array('icon'=>'public/uploads/social/Youtube.png','link'=>$description->YouTube); 
		 }
		 if($description->X!='')
		 {
			$social[]=array('icon'=>'public/uploads/social/twitter-x.png','link'=>$description->X); 
		 }
		 if($description->LinkedIn!='')
		 {
			$social[]=array('icon'=>'public/uploads/social/linkedIn.png','link'=>$description->LinkedIn); 
		 }
		 if($description->Snapchat!='')
		 {
			$social[]=array('icon'=>'public/uploads/social/Snapchat.png','link'=>$description->Snapchat); 
		 }
		 if($description->Pininterest!='')
		 {
			$social[]=array('icon'=>'public/uploads/social/Pinterest.png','link'=>$description->Pininterest); 
		 }
		 if($description->Wechat!='')
		 {
			$social[]=array('icon'=>'public/uploads/social/Wechat.png','link'=>$description->Wechat); 
		 }
		 if($description->Tiktok!='')
		 {
			$social[]=array('icon'=>'public/uploads/social/Tiktok.png','link'=>$description->Tiktok); 
		 }
		 
		 
		 
     $data = array(
            'salon_name'=>$description->vendor_name,
			'salon_name_ar'=>$description->vendor_name_ar,
			'calendarschedule'=>$description->calendarschedule,
            'description'=>$description->description,
			'vendor_email'=>$description->vendor_email,
			'vendor_mobile'=>$description->vendor_phone,
            'vendor_logo'=>$description->vendor_logo,
			'cod'=>$description->cod,
			'address'=>$description->address,
			'addressar'=>$description->address_ar,
            'vendor_id'=>$vendor_id,
            'socialicons'=>$social,
			'latitude'=>$description->lat,
			'longitude'=>$description->lng,
			'distance'=>ceil($description->distance),
			'traveltime'=>'15 Min',
           
			'opening_days'=>'Mon - Sun',	
			'opening_time'=>$description->opening_time,
			'closing_time'=>$description->closing_time,
			'paymentcancelpolicy'=>$description->paymentcancelpolicy,
			'rating'=>round($rating, 1)."/5",
			'wishlist'=>$wishlist,
			'reviews'=>$review,
			'reviewcount'=> $reviewcount,
			'ratingonestarcount'=>$onestarcount,
			'ratingtwostarcount'=>$twostarcount,
			'ratingthreestarcount'=>$threestarcount,
			'ratingfourstarcount'=>$fourstarcount,
			'ratingfivestarcount'=>$fivestarcount,
			 'weekly_time'=>$weektime,
            'staffs'=>$staffs1,
            'services'=>$serv,
			
            'portfolio'=>$portfolio,
			'ameneties'=>$ameneties
            
            
            );       
               
        if($description){
            $message = array('status'=>'1', 'message'=>'salon details', "data"=>$data);
          return $message;
        }  
        else{
            $message = array('status'=>'1', 'message'=>'No Services found');
          return $message;
        }
      
	
	}
	
	public function addWishlist(Request $request)
	{
		$user_id=$request->user_id;
		$Vendor_ID=$request->Vendor_ID;
		$getWishlist = DB::table('wishlists')
    					->where('Vendor_ID',$Vendor_ID)->where('user_id',$user_id)
    					->first();
		 if($getWishlist){
			 $insertID=DB::table('wishlists')
                                ->where
    				('Vendor_ID',$Vendor_ID)->where('user_id',$user_id)->delete();
			 $message = array('status'=>'1', 'message'=>'Removed Wishlist Item');
	        	return $message;
		 }
		 else 
		 {
			 $insertID=DB::table('wishlists')
                                ->insertGetId
    				(['Vendor_ID'=>$Vendor_ID,'user_id'=>$user_id]);
			$message = array('status'=>'1', 'message'=>'Added to Wishlist');
	        	return $message; 
			 
		 }
						
	}
	
	public function viewWishlists(Request $request)
	{
		$user_id=$request->user_id;
		$lat=$request->lat;
		$lng=$request->lng;
		$servicecategory=$request->servicecategory;
		if($servicecategory=='All')
		{
		$servicecategory='';	
		}
			
		$vendors = DB::table('wishlists')->join('vendor', 'vendor.vendor_id', '=','wishlists.Vendor_ID')->join('vendor_services','vendor.vendor_id','=','vendor_services.vendor_id')->select('vendor_name','vendor.vendor_id','vendor_email','vendor_phone',DB::raw("CONCAT('public/', vendor_logo) AS vendor_logo"),'lat','lng','opening_time','closing_time','shop_type as type', DB::raw("concat(vendor_loc,',',area, ',', block,',',street,',',avenue,',',zipcode) as address"),DB::raw("6371 * acos(cos(radians(".$lat . ")) 
                    * cos(radians(lat)) 
                    * cos(radians(lng) - radians(" . $lng . ")) 
                    + sin(radians(" .
		$lat. ")) 
                    * sin(radians(lat))) AS distance"))
    					->where('user_id',$user_id)
    					 ->where('vendor.pstatus',1)
						 
                  ->where('vendor.enabled',1);
				  if($servicecategory!='')
				  {
				  				  
				  $vendors=$vendors->where('vendor_services.Service_Category','=', $servicecategory);
				  }
				  $vendors=$vendors->get()->unique('vendor_name');
				  	  $pr = [];
$i=0;				  
        foreach($vendors as $store)
        {
			  	
			 $app = DB::table('review')
                        ->where('vendor_id', $store->vendor_id)
                        ->avg('rating');
		
				$store->distance=ceil($store->distance);
            		
				if($app)
				{
				$store->rating= number_format(round($app,1),1);	
				}
				else
				{
				$store->rating= number_format(0,1);	
				}
                $pr[] = $store; 
			
			 
			
			
        }
				  
				  
		 if($pr!=NULL){
			 $message = array('status'=>'1', 'message'=>'Wishlisted Item(s) Found','data'=>$pr);
	        	return $message;
		 }
		 else 
		 {
			 
			$message = array('status'=>'1', 'message'=>'No Wishlist Found');
	        	return $message; 
			 
		 }
						
	}
	
	
	  public function add_salon_rating(Request $request)
    { 
        $user_id = $request->user_id;
        $staff_id = $request->vendor_id;
        $rating = $request->rating;
        $description=$request->description;
		$quickreview=$request->quickreview;
        $created_at = Carbon::now();
		$user=DB::table('orders')->select('vendor_id')->where('vendor_id',$staff_id)->where('user_id',$user_id)->get();
		if(count($user)>0)
		{
        $check = DB::table('review')
               ->where('user_id',$user_id)
               ->where('vendor_id',$staff_id)
               ->first();
               
        if($check){
            $review= DB::table('review') 
            ->where('user_id',$user_id)
               ->where('vendor_id',$staff_id)
               ->update([
               'rating'=>$rating,
               'description'=>$description,
               'active'=>1,
			   'quickreview'=>$quickreview,
               'created_at'=>$created_at,
               'updated_at'=>$created_at]);
        }       
          else{     
        $review= DB::table('review') 
               ->insert([ 'user_id'=>$user_id,
               'vendor_id'=>$staff_id,
               'rating'=>$rating,
               'description'=>$description,
			    'quickreview'=>$quickreview,
               'active'=>1,
               'created_at'=>$created_at,
               'updated_at'=>$created_at]);
          }     
        if($review){
            $message = array('status'=>'1', 'message'=>'reviewed successfully');
          return $message;
        }  
        else{
            $message = array('status'=>'1', 'message'=>'Please try again later');
          return $message;
        }
		}
		else
		{
			 $message1=__('messages.Youcannotleaveareview');
		$message = array('status'=>'1', 'message'=>$message1);
          return $message;	
			
		}
    }
	
// 	 public function add_likes_review(Request $request)
//     { 
//         $user_id = $request->user_id;
//         $review_id = $request->review_id;
//         $vendor_id=$request->vendor_id;
// 		$user=DB::table('likesreviews')->where('user_id',$user_id)->where('review_id',$review_id)->get();
// 		if(count($user)>0)
// 		{
// 			$message = array('status'=>'0', 'message'=>'You Already Liked this Review');
// 			return $message;
// 			exit();
// 		}
// 		else
// 		{
// 		$reviewlike= DB::table('likesreviews') 
//               ->insert(['user_id'=>$user_id,
//               'review_id'=>$review_id
//               ]);
// 			   $user=DB::table('likesreviews')->where('user_id',$user_id)->where('review_id',$review_id)->get();
// 		$count=count($user);
// 		$reviewlike= DB::table('review') 
//             ->where('user_id',$user_id)
//               ->where('vendor_id',$vendor_id)
//               ->update([
//               'likes'=>$count
//               ]);
// 		}
       
               
       
        
//           $message = array('status'=>'1', 'message'=>'Liked successfully');
//           return $message;
        
		
//     }

public function add_likes_review(Request $request)
{ 
    // Fetch input data from the request
    $user_id = $request->user_id;
    $review_id = $request->review_id;
    $vendor_id = $request->vendor_id;

    // Check if the user has already liked the review
    $existingLike = DB::table('likesreviews')
        ->where('user_id', $user_id)
        ->where('review_id', $review_id)
        ->first();

    if ($existingLike) {
        // If the like exists, remove it (unlike)
        DB::table('likesreviews')
            ->where('user_id', $user_id)
            ->where('review_id', $review_id)
            ->delete();

        // Recalculate the total like count for this review
        $likeCount = DB::table('likesreviews')
            ->where('review_id', $review_id)
            ->count();

        // Update the like count in the review table
        DB::table('review')
            ->where('id', $review_id)
            ->update(['likes' => $likeCount]);

        // Return a response indicating the like was removed
        $message = array(
            'status' => '1',
            'message' => 'Like removed successfully',
            'likes' => $likeCount
        );
        return $message;
    } else {
        // If the like does not exist, add it
        DB::table('likesreviews')->insert([
            'user_id' => $user_id,
            'review_id' => $review_id
        ]);

        // Recalculate the total like count for this review
        $likeCount = DB::table('likesreviews')
            ->where('review_id', $review_id)
            ->count();

        // Update the like count in the review table
        DB::table('review')
            ->where('id', $review_id)
            ->update(['likes' => $likeCount]);

        // Return a response indicating the review was liked
        $message = array(
            'status' => '1',
            'message' => 'Liked successfully',
            'likes' => $likeCount
        );
        return $message;
    }
}
	
public function add_report_reviews(Request $request)
    { 
        $user_id = $request->user_id;
        $review_id = $request->review_id;
        $vendor_id=$request->vendor_id;
		$notes=$request->notes;
		$user=DB::table('reporreviews')->where('user_id',$user_id)->where('review_id',$review_id)->get();
		if(count($user)>0)
		{
			$message = array('status'=>'0', 'message'=>'You Already Reported this Review');
			return $message;
			exit();
		}
		else
		{
		$reviewlike= DB::table('reporreviews') 
               ->insert(['user_id'=>$user_id,
               'review_id'=>$review_id,
			   'notes'=>$notes
               ]);
			   $user=DB::table('reporreviews')->where('user_id',$user_id)->where('review_id',$review_id)->get();
		$count=count($user);
		$reviewlike= DB::table('review') 
            ->where('user_id',$user_id)
               ->where('vendor_id',$vendor_id)
               ->update([
               'report'=>$count
              ]);
		}
       
               
       
        
            $message = array('status'=>'1', 'message'=>'Review Reported successfully');
          return $message;
       
		
    }
	
}
