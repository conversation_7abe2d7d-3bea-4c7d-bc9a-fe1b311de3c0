<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Carbon\Carbon;
use App\Models\User;
use JWTAuth;
use Tymon\JWTAuth\Exceptions\JWTException;
use Hash;
use App\Traits\SendMail;
use App\Services\LocalizationService;

class SplashScreenController extends Controller
{
     public function SplashScreens(Request $request,LocalizationService $localizationService)
    {
		
		$splashscreens=DB::table('splashscreens')->select('splash_titlear','splash_descriptionar','splash_id','splash_title','splash_description','enabled','date1','order1', DB::raw("CONCAT('public/',splash_image) AS splash_image"))->where('enabled',1)
               ->orderBy('order1', 'ASC')->get();
		
				 $fieldsToReplace = [
        'splash_title' => 'splash_titlear',
        'splash_description' => 'splash_descriptionar',
    ];

    $splashscreens = $localizationService->replaceFieldsForLanguage($splashscreens, $request, $fieldsToReplace);  
				   $message = array('status'=>'1', 'message'=>'Splash Screens', 'data'=>$splashscreens);
				   return $message;
                   
		
	}
}
