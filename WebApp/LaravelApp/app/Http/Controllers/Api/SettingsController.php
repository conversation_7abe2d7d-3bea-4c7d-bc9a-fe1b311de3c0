<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Carbon\Carbon;
use App\Models\User;
use JWTAuth;
use Tymon\JWTAuth\Exceptions\JWTException;
use Hash;
use App\Traits\SendMail;
use App\Services\LocalizationService;
use Illuminate\Support\Facades\App;
class SettingsController extends Controller
{
     public function privacypolicy(Request $request,LocalizationService $localizationService)
    
     {
		 
		 
		$privacy = DB::table('privacypolicy')
    					->where('Privacy_ID',1)
    					->first();
					
					if (App::getLocale() == 'ar') {
            // If the language is Arabic, replace English fields with Arabic ones
         
            $privacy->Description = $privacy->Description_ar;
        } else {
            // Otherwise, use English fields (default language)
            $privacy->Description = $privacy->Description;
        }					
						
				$message = array('status'=>'1', 'message'=>'Privacy Policy', 'data'=>$privacy);
	        	return $message;
		 
	 }
	 
	 public function termsconditions(Request $request)
    
     {
		 
		$terms = DB::table('terms')
    					->where('Terms_ID',1)
    					->first(); 
			if (App::getLocale() == 'ar') {
            // If the language is Arabic, replace English fields with Arabic ones
         
            $terms->Description = $terms->Description_ar;
        } else {
            // Otherwise, use English fields (default language)
            $terms->Description = $terms->Description;
        }								
						
				$message = array('status'=>'1', 'message'=>'Terms and Conditions', 'data'=>$terms);
	        	return $message;
		 
	 }
	 
	  public function faqs(Request $request,LocalizationService $localizationService)
    
     {
		$faqs = DB::table('faqs')->get();
    					
    					 
				if($faqs)
				{					
				$fieldsToReplace = [
        'Question' => 'Question_ar',
        'Answer' => 'Answer_ar',
    ];	
$faqs = $localizationService->replaceFieldsForLanguage($faqs, $request, $fieldsToReplace);	
				$message = array('status'=>'1', 'message'=>'Faqs', 'data'=>$faqs);
	        	return $message;
				}
				else
				{
						
				$message = array('status'=>'1', 'message'=>'Faqs Not Found', 'data'=>$faqs);
	        	return $message;	
				}
		 
	 }
	 
	 public function contactus(Request $request)
    
     {
		$contactus = DB::table('contactus')
    					->where('Contact_ID',1)
    					->first(); 
						
						
				$message = array('status'=>'1', 'message'=>'Contact Us', 'data'=>$contactus);
	        	return $message;
		 
	 }
}
