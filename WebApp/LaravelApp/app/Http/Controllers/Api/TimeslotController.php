<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Session;
use Hash;
use Carbon\Carbon;
use Illuminate\Support\Str;
use DateTime;

class TimeslotController extends Controller
{
		public function getTimeSlot($interval, $start_time, $end_time)
{
    $start = new DateTime($start_time);
    $end = new DateTime($end_time);
    $startTime = $start->format('H:i');
    $endTime = $end->format('H:i');
    $i=0;
    $time = [];
	
    while(strtotime($startTime) < strtotime($endTime)){
		
        //$start = $startTime;
        //$end = date('H:i',strtotime('+'.$interval.' minutes',strtotime($startTime)));
       
        
        if(strtotime($startTime) <= strtotime($endTime)){
			
            $time[$i] = $startTime;
			 $startTime = date('H:i',strtotime('+'.$interval.' minutes',strtotime($startTime)));
			$i++;
            //$time[$i] = $end;
        }
		
    }
	 $time[$i]=$endTime;
    return $time;
}
	
     public function timeslotfilter()
    {
		 $vendors = DB::table('vendor')
                  ->where('vendor.pstatus',1)
                  ->where('vendor.enabled',1)->get();
				  $slotnew=[];
				 $slots='';
				  $date=date('Y-m-d');
				  $day = date('l', strtotime($date));
				  foreach($vendors as $vendor)
				  {
					
					 $time=DB::table('time_slot')->where('vendor_id',$vendor->vendor_id)->where('status',1)->where('days',$day)->first();
		if($time!=null)
		{
		
		$openhour=$time->open_hour;
		$closehour=$time->close_hour;
		
		if($openhour!='' and $closehour!='')
		{
			
		$slots=$this->getTimeSlot(30,$openhour,$closehour);
		
		}
		else
			
		{
		$slots="No Slots";
				
		}
		
		$bookings=DB::table('order_cart')->join('orders', 'orders.id', '=','order_cart.book_ID')
                ->where('order_cart.vendor_id',$vendor->vendor_id)
                ->get();
				
				foreach($bookings as $booking)
				{
					$times=$booking->service_time;
					$timesslots=explode("-",$times);
					$timesslot1=$timesslots[0];
					$timesslot2=$timesslots[1];
					$timesslot1=date("H:i", strtotime($timesslot1));
					$timesslot2=date("H:i", strtotime($timesslot2));
					$slotnew=$this->getTimeSlot(30,$timesslot1,$timesslot2);
				}
				
				foreach($slots as $slot)
				{
					if (false !== $key = array_search($slot, $slotnew)) {
					 unset($slots[$key]);	
					}
				
				}
		}
		
				  }
				  
				if($slots!='')
				{
				$slots=array_unique($slots); 
if(count($slots)>0){
       
  
        
            $message = array('status'=>'1', 'message'=>'Present time Slot', 'data'=>$slots);
            return $message;
            }
            else
            {
                $message = array('status'=>'1', 'message'=>'Oops No time slot present');
            return $message;
            }
				}
				else
				{
				$message = array('status'=>'1', 'message'=>'Oops No time slot present');
            return $message;	
				}
			
			
				  
				  
				  
				  
				 
				
	

                  
		
	}
	
	
	



}
