<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Carbon\Carbon;
use App\Models\User;
use JWTAuth;
use Tymon\JWTAuth\Exceptions\JWTException;
use Hash;
use DateTime;
use PDF;
use Config;
use Mail;
use App\Mail\SendMail;
use Illuminate\Support\Facades\Log;
use App\Services\LocalizationService;
use Illuminate\Support\Facades\App;
class BookingController extends Controller
{	
	private $api_key="jtest123";
	private $paymenturl="https://sandboxapi.upayments.com/api/v1/";
public function VendorServicesCategory(Request $request)
{
    $vendor = $request->vendor_id;
    $services1 = [];

    $ServicesCategory = DB::table('vendor_servicecategory')
        ->select('Cat_ID', 'Cat_Name', 'Ordering')
        ->orderBy('Ordering', 'ASC')
        ->where('vendor_id', $vendor)
        ->where('enabled', 1)
        ->get();

    $i = 1;

    foreach ($ServicesCategory as $category) {
        if ($i == 1) {
            $services = DB::table('vendor_services')
                ->where('vendor_id', $vendor)
                ->where('enabled', 1)
                ->get();

           foreach ($services as $key => $service) {
    if ($service->event == 1) {
        // Get current date and time
        $now = Carbon::now();
        $today = $now->format('Y-m-d');
        $currentTime = $now->format('h:i A'); // matches '11:30 AM' format

        // Get schedule for today
        $schedule = DB::table('vendor_service_schedule')
    ->where('service_id', $service->ServicesID)
    ->whereRaw("
        STR_TO_DATE(CONCAT(service_date, ' ', service_time_from), '%Y-%m-%d %h:%i %p') >= ?
    ", [Carbon::now()])
    ->orderBy('service_date')
    ->orderBy('service_time_from')
    ->first();

        // Show only if currently within time range
        if ($schedule) {
            $service->event_schedule = $this->buildEventScheduleInline($service->ServicesID);
        } else {
            unset($services[$key]); // Remove if not within time
        }
    }
}

            $services1[] = [
                'Cat_ID'   => 'All',
                'Cat_Name' => 'All',
                'Services' => array_values($services->toArray()),
            ];

            $services = DB::table('vendor_services')->select('ServicesID', 'Name', 'Name_ar', DB::raw("CONCAT('public/',Image) AS Image"), 'servicesfor', 'Service_Price', 'Service_Time', 'Service_Category', 'Staff_ID', 'vendor_id', 'enabled', 'Afterdiscountprice', 'price', 'commision', 'event')
                ->where('vendor_id', $vendor)
                ->where('Service_Category', $category->Cat_ID)
                ->where('enabled', 1)
                ->get();

            foreach ($services as $service) {
                if ($service->event == 1) {
                    $schedule = $this->buildEventScheduleInline($service->ServicesID);
                    $service->event_schedule = $schedule ?? null;
                }
            }

            $services1[] = [
                'Cat_ID'   => $category->Cat_ID,
                'Cat_Name' => $category->Cat_Name,
                'Services' => $services,
            ];

            $i++;
        } else {
            $services = DB::table('vendor_services')->select('ServicesID', 'Name', 'Name_ar', DB::raw("CONCAT('public/',Image) AS Image"), 'servicesfor', 'Service_Price', 'Service_Time', 'Service_Category', 'Staff_ID', 'vendor_id', 'enabled', 'Afterdiscountprice', 'price', 'commision', 'event')
                ->where('vendor_id', $vendor)
                ->where('Service_Category', $category->Cat_ID)
                ->where('enabled', 1)
                ->get();

             foreach ($services as $key => $service) {
    if ($service->event == 1) {
        // Get current date and time
        $now = Carbon::now();
        $today = $now->format('Y-m-d');
        $currentTime = $now->format('h:i A'); // matches '11:30 AM' format

        // Get schedule for today
        $schedule = DB::table('vendor_service_schedule')
    ->where('service_id', $service->ServicesID)
    ->whereRaw("
        STR_TO_DATE(CONCAT(service_date, ' ', service_time_from), '%Y-%m-%d %h:%i %p') >= ?
    ", [Carbon::now()])
    ->orderBy('service_date')
    ->orderBy('service_time_from')
    ->first();

        // Show only if currently within time range
        if ($schedule) {
            $service->event_schedule = $this->buildEventScheduleInline($service->ServicesID);
        } else {
            unset($services[$key]); // Remove if not within time
        }
    }
}

            $services1[] = [
                'Cat_ID'   => $category->Cat_ID,
                'Cat_Name' => $category->Cat_Name,
                'Services' => $services,
            ];
        }
    }

    return [
        'status'  => '1',
        'message' => 'Category List',
        'data'    => $services1,
    ];
}

private function buildEventScheduleInline(int $serviceId)
{
    $schedules = DB::table('vendor_service_schedule')
    ->where('service_id', $serviceId)
    ->whereRaw("
        STR_TO_DATE(CONCAT(service_date, ' ', service_time_from), '%Y-%m-%d %h:%i %p') >= ?
    ", [Carbon::now()])
    ->orderBy('service_date')
    ->orderBy('service_time_from')
    ->get();

    if ($schedules->isEmpty()) {
        return null;
    }

    $grouped = $schedules->groupBy('service_date');

    foreach ($grouped as $date => $entries) {
        $maxPersons  = $entries->first()->max_persons;
        $bookedCount = 0;
        $times       = [];

        foreach ($entries as $entry) {
            $timeFrom = $entry->service_time_from;
            $dateTime = date('Y-m-d H:i:s', strtotime("$date $timeFrom"));
			Log::info($dateTime);
            $count = DB::table('orders')
                ->where('service_date', $dateTime)
                ->where('vendor_id', function ($q) use ($serviceId) {
                    $q->select('vendor_id')
                      ->from('vendor_services')
                      ->where('ServicesID', $serviceId)
                      ->limit(1);
                })
                ->whereIn('status', [1, 6])
                ->count();
 $count1 = DB::table('orders')
                ->where('service_date', $dateTime)
                ->where('vendor_id', function ($q) use ($serviceId) {
                    $q->select('vendor_id')
                      ->from('vendor_services')
                      ->where('ServicesID', $serviceId)
                      ->limit(1);
                })
                ->whereIn('status', [1, 6])->toSql();
          Log::info($count1);
            $bookedCount += $count;

            $times[] = [
                'time'     => $timeFrom,
                'disabled' => false,
            ];
        }

        return [
            'service_id'      => $serviceId,
            'date'            => $date,
            'times'           => $times,
            'max_persons'     => $maxPersons,
            'booked_count'    => $bookedCount,
            'available_count' => $maxPersons - $bookedCount,
            'available'       => $bookedCount < ($maxPersons * count($times)),
        ];
    }

    return null;
}
public function VendorServices(Request $request)
{
    $servicecategory = $request->Service_Category;
    $vendor_id = $request->vendor_id;

    if ($servicecategory == 'All' || $servicecategory == '') {
        $services = DB::table('vendor_services')
            ->where('vendor_id', $vendor_id)
            ->get();
    } else {
        $services = DB::table('vendor_services')
            ->where('vendor_id', $vendor_id)
            ->where('Service_Category', $servicecategory)
            ->get();
    }

    foreach ($services as $key => $service) {
    if ($service->event == 1) {
        // Get current date and time
        $now = Carbon::now();
        $today = $now->format('Y-m-d');
        $currentTime = $now->format('h:i A'); // matches '11:30 AM' format

        // Get schedule for today
         $schedule = DB::table('vendor_service_schedule')
    ->where('service_id', $service->ServicesID)
    ->whereRaw("
        STR_TO_DATE(CONCAT(service_date, ' ', service_time_from), '%Y-%m-%d %h:%i %p') >= ?
    ", [Carbon::now()])
    ->orderBy('service_date')
    ->orderBy('service_time_from')
    ->first();

        // Show only if currently within time range
        if ($schedule) {
            $service->event_schedule = $this->buildEventScheduleInline($service->ServicesID);
        } else {
            unset($services[$key]); // Remove if not within time
        }
    }
}

    $message = [
        'status' => '1',
        'message' => 'Service List',
        'data' => array_values($services->toArray()),
    ];

    return $message;
}
public function VendorCategories(Request $request)
		{
			
			
				 $ServicesCategory = DB::table('vendor_servicecategory')->select('Cat_ID','Cat_Name','Ordering')
                   ->orderBy('Cat_ID', 'ASC')->where('vendor_id',$vendor)->where('enabled',1)->get();
					$Services=['ServicesID'=>'All','Cat_Name'=>'All','Ordering'=>1];
					$ServicesCategory->prepend($Services);
					 $message = array('status'=>'1', 'message'=>'Service List', 'data'=>$ServicesCategory);
				   return $message;
	 
		}
			
		public function staffSelection(Request $request)
		{	
		$staff=array();
		$i=1;
		$service=$request->service;
		$vendor=$request->vendor;
		$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor)->where('Service_ID',$service)->get();
		$staff[]=array('staff_id'=>'NULL','staff_name'=>'No Preference','staff_image'=>'public/uploads/no-staff.png');
		foreach($vendorservices as $vendorservice)
		{
		$staff1=DB::table('staff_profile')->select('staff_id','staff_name',DB::raw("CONCAT('public/', staff_image) AS staff_image"))->where('staff_id',$vendorservice->staff_id)->first();	
			if($staff1!=null)
			{
				$staff[]=$staff1;
			}
			
		}
		$message = array('status'=>'1', 'message'=>'Staff List', 'data'=>$staff );
		return $message;
		
	}
	
	public function getAccessToken($jsonFilePath) {
    // Load the service account JSON file
    $serviceAccount = json_decode(file_get_contents($jsonFilePath), true);

    $clientEmail = $serviceAccount['client_email'];
    $privateKey = str_replace('\\n', "\n", $serviceAccount['private_key']);
    $scope = 'https://www.googleapis.com/auth/firebase.messaging';

    // Create the JWT header
    $header = json_encode(['alg' => 'RS256', 'typ' => 'JWT']);
    
    // Create the JWT claim set
    $now = time();
    $claimSet = json_encode([
        'iss' => $clientEmail,
        'scope' => $scope,
        'aud' => 'https://oauth2.googleapis.com/token',
        'exp' => $now + 3600, // Token expires in 1 hour
        'iat' => $now,
    ]);

    // Encode the header and claim set
    $base64UrlHeader = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
    $base64UrlClaimSet = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($claimSet));

    // Create the signature
    $signatureInput = $base64UrlHeader . '.' . $base64UrlClaimSet;
    openssl_sign($signatureInput, $signature, $privateKey, OPENSSL_ALGO_SHA256);
    $base64UrlSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

    // Create the JWT
    $jwt = $base64UrlHeader . '.' . $base64UrlClaimSet . '.' . $base64UrlSignature;

    // Prepare the cURL request to get the access token
    $url = 'https://oauth2.googleapis.com/token';
    $data = [
        'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        'assertion' => $jwt,
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
    ]);

    // Execute the request
    $response = curl_exec($ch);
    
    if ($response === false) {
        echo 'Curl error: ' . curl_error($ch);
        return null;
    }

    curl_close($ch);
    
    $responseData = json_decode($response, true);
    return $responseData['access_token'] ?? null; // Return the access token or null
}
public function getTimeSlotnew($interval, $start_time, $end_time)
{
   
    $i=0;
    $time = [];
	$add_mins  = $interval*60;
	 $array_of_time = array ();
	  $slots=array();
	 $start_time=strtotime($start_time);
	 $end_time=strtotime($end_time);
	
	while ($start_time < $end_time) // loop between time
    {
       $array_of_time[] = date ("h:i A", $start_time);
	   
       $start_time += $add_mins; // to check endtie=me
    }

    $new_array_of_time = array ();
    for($i = 0; $i < count($array_of_time) - 1; $i++)
    {
        $new_array_of_time[] = $array_of_time[$i];
		$new_array_of_time[] = $array_of_time[$i+1];
    }
	
	
    return  array_unique($new_array_of_time);
}


	public function getTimeSlot($date, $day, $vendor_id,$service, $staff_id = null)
{	 
 $slots=array();
			if($staff_id==null)
			{	
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$service)->first();
				$staff1=DB::table('staff_profile')->select('staff_id','staff_name','staff_image')->where('staff_id',@$vendorservice->staff_id)->first();
				@$staff_id=@$staff1->staff_id;
				$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			}
			else
			{
				$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			}
			if (isset($time_slot)) {
				
			$start_time = strtotime($time_slot->open_hour);
            $end_time = strtotime($time_slot->close_hour);
            $slot_duration = DB::table('vendor_services')->select('Service_Time')
               ->where('vendor_id',$vendor_id)->where('ServicesID',$service)
              
               ->first();

            //$slot_parts = explode(':', $slot_duration);
            //$slot_hours = intval($slot_parts[0]);
            //$slot_minutes = intval($slot_parts[1]);

            //$slot_duration_minutes =$slot_duration->Service_Time;
			$slot_duration_minutes =15;
            $current_time = $start_time;
            
            $now = strtotime(date('H:i'));
            
				while ($current_time < $end_time) {
                // Skip slots that overlap with break hours
                

                $slot_start = $current_time;
                $current_time += $slot_duration_minutes * 60;

                $startDateTime = date('Y-m-d', strtotime($date)).' '.date('H:i:s', $slot_start);
                $startTimestamp = strtotime($startDateTime);

                $endTimestamp = $startTimestamp + ($slot_duration_minutes * 60);
                
				if ($date === date('Y-m-d') &&  $slot_start < $now) {
                continue;
                 }

                // Check if the slot overlaps with any existing appointments
                $is_booked = false;
				
				$arraytimes=array();
					$arraytimes1=array();
				$user_ids=array();
                if ($staff_id) {
                    $existingAppointments = DB::table('order_cart')->where('staff_id', $staff_id)
                        ->whereDate('start_date',$date)->whereNotIn('status',[3,4])
                        ->get();

                    foreach ($existingAppointments as $appointment) {
                        $user_id=$appointment->user_id;
						$existingAppointments1 = DB::table('orders')->where('id', $appointment->book_ID)
                       
                        ->first();
						$timesnew1=explode("-",$existingAppointments1->service_time);
						if($user_id!=180)
						{
						$slotnew=$this->getTimeSlotnew(15,$timesnew1[0],$timesnew1[1]);
						
						foreach($slotnew as $slots1)
				{
				$arraytimes[]=$slots1;
				$user_ids[$slots1]=$user_id;
				}
						}
						else
						
						{
						$slotnew=$this->getTimeSlotnew(15,$timesnew1[0],$timesnew1[1]);
						
						foreach($slotnew as $slots1)
				{
				$arraytimes1[]=$slots1;
				$user_ids[$slots1]=$user_id;
				}    
						    
						}
                        @$appointment_start = strtotime($appointment->start_date);
						
                        $appointment_end = @$appointment_start + @$appointment->duration;

                        if ($startTimestamp >= $appointment_start && $startTimestamp < $appointment_end) {
                            $is_booked = true;
                            break;
                        }
                    }
                   
                }
              
                if (! $is_booked) {
					
					$slot =  date('h:i A', $slot_start);
					
					if(!in_array($slot,$arraytimes))
					{
					    if(@$user_ids[@$slot]==180)
					    {
					     $slot = [
                       
                        'time' => date('h:i A', $slot_start),
                        'disabled' => true,
                    ];    
					    }
					    else
					    {
                    $slot = [
                       
                        'time' => date('h:i A', $slot_start),
                        'disabled' => false,
                    ];
                    
					    }
                    $slots[] = $slot;
					}
                }
                
                 if ($is_booked) {
					
					$slot =  date('h:i A', $slot_start);
					
					if(!in_array($slot,$arraytimes1))
					{
					    
					     $slot = [
                       
                        'time' => date('h:i A', $slot_start),
                        'disabled' => true,
                    ];    
					   
                    $slots[] = $slot;
					}
                }
                
                
                
                
                
                
                
                
                
            }
			
             
        }
		
      $slots = array_values($slots);
        return $slots;
				
			}
			
			public function getTimeSlotbook($date, $day, $vendor_id,$service, $staff_id = '',$client='')
{	 
    
           $slots=array();
            if($client !=''){
                   $events=DB::table('user_cart')
                            ->where('user_id',$client)->orderBy('Cart_ID', 'asc')
                            ->first();
                    $eventsdata=DB::table('user_cart')
                            ->where('user_id',$client)->orderBy('Cart_ID', 'asc')
                            ->get();          
            }
           
		if($staff_id=='NULL' or $staff_id=='' )
			{	
    			
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$service)->first();
				$staff1=DB::table('staff_profile')->select('staff_id','staff_name','staff_image')->where('staff_id',@$vendorservices->staff_id)->first();
				
				$staff_id=$staff1->staff_id;
				$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			}
			else
			{
				$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			}
			if (isset($time_slot)) {
			    	if (!empty($eventsdata) && count($eventsdata)>=1) {
        		    $exists_time_slot = DB::table('employee_time_slot')
                   ->where('vendor_id',$vendor_id)->where('staff_id',$events->staff_id)
                   ->where('days',$day)->where('status',1)
                   ->first(); 
                   if($exists_time_slot->open_hour == $time_slot->open_hour){
                       
                   }
                   else{
                    $time1 = new DateTime($exists_time_slot->open_hour);
                    $time2 = new DateTime($time_slot->open_hour);
                
                    // Calculate the difference
                    $interval = $time1->diff($time2);
                
                        // Get the difference in minutes
                    $differenceInMinutes = ($interval->h * 60) + $interval->i;
                    // Add 15 minutes
                   $time2->modify("+{$differenceInMinutes} minutes");
                    // Format back to H:i
                    $time_slot->open_hour = $time2->format('H:i'); 
                   }
                }
                $start_time = strtotime($time_slot->open_hour);
                $end_time = strtotime($time_slot->close_hour);
			       $slot_duration = DB::table('vendor_services')->select('Service_Time')
               ->where('vendor_id',$vendor_id)->where('ServicesID',$service)
              
               ->first();

            //$slot_parts = explode(':', $slot_duration);
            //$slot_hours = intval($slot_parts[0]);
            //$slot_minutes = intval($slot_parts[1]);

            //$slot_duration_minutes =$slot_duration->Service_Time;
		$slot_duration_minutes =15;
            $current_time = $start_time;
            
            $now = strtotime(date('H:i'));
           
				while ($current_time < $end_time) {
                // Skip slots that overlap with break hours
                

                $slot_start = $current_time;
                $current_time += $slot_duration_minutes * 60;

                $startDateTime = date('Y-m-d', strtotime($date)).' '.date('H:i:s', $slot_start);
                $startTimestamp = strtotime($startDateTime);

                $endTimestamp = $startTimestamp + ($slot_duration_minutes * 60);

               if ($date === date('Y-m-d') &&  $slot_start < $now) {
                continue;
                 }
           
                // Check if the slot overlaps with any existing appointments
                 $is_booked = false;
				
				$arraytimes=array();
				
				
                if ($staff_id) {
                    $existingAppointments = DB::table('order_cart')->where('staff_id', $staff_id)
                        ->whereDate('start_date',$date)->whereNotIn('status',[3,4])
                        ->get();

                    foreach ($existingAppointments as $appointment) {
						$existingAppointments1 = DB::table('orders')->where('id', $appointment->book_ID)
                       
                        ->first();
						
						$timesnew1=explode("-",$existingAppointments1->service_time);
						$slotnew=$this->getTimeSlotnew(15,$timesnew1[0],$timesnew1[1]);
						
						foreach($slotnew as $slots1)
				{
				$arraytimes[]=$slots1;
				}
                        @$appointment_start = strtotime($appointment->start_date);
						
                        $appointment_end = @$appointment_start + @$appointment->duration;

                        if ($startTimestamp >= $appointment_start && $startTimestamp < $appointment_end) {
                            $is_booked = true;
                            break;
                        }
                    }
                }

                if (! $is_booked) {
					 $slot =  date('h:i A', $slot_start);
					if(!in_array($slot,$arraytimes))
					{
                    $slot =  date('h:i A', $slot_start);
                    
                    $slots[] = $slot;
					}
                }
            }
			/*
            foreach ($slots as  $key=> $slot) {
                $slot_timestamp = strtotime($slot); // Convert slot time to timestamp
                if ($slot_timestamp >= $appointment_start && $appointment_end >= $appointment_start) { 
                    unset($slots[$key]); // Remove this slot from the array
                    break;
                }
            }
			*/
        }
        $slots = array_values($slots);
        return $slots;
				
			}
			
		public function staff_getSlots(Request $request)
			{
				$slotsnew=[];
			$vendor_id=	$request->vendor_id;
			$selected_date=$request->date;	
			$services=$request->service;	
			@$staff=$request->staff;
			$delete_status=$request->after_delete;
			$client=$request->user_id;	
			//@$time1=$request->time;
			if($staff=='' or $staff=='NULL')
			{
				/*
			$day = date('l', strtotime($selected_date));
			$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->get();
		foreach($vendorservices as $staff1)
		{
			$staff=$staff1->staff_id;
		$slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services, $staff1->staff_id);
		if(count($slots)>0)
		{
			break;
		}
		}
		*/

		$day = date('l', strtotime($selected_date));
				$staffAvailability =[];
				$earliestTimeSlots = [];
				$timeSlots=[];
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->get();
				foreach($vendorservices as $vendorservice)
				{
			      if($delete_status ==1 && $client !=''){
                     $first_service=DB::table('user_cart')
                                ->where('user_id',$client)->orderBy('Cart_ID', 'asc')
                                ->first();
                     $staff1=DB::table('staff_profile')->select('staff_id','staff_name','staff_image')->where('staff_id',$first_service->staff_id)->first();           
                }
                else
				$staff1=DB::table('staff_profile')->select('staff_id','staff_name','staff_image')->where('staff_id',$vendorservice->staff_id)->first();	
					if($staff1!=null)
					{
						$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff1->staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			   
			   @$startTime = strtotime($time_slot->open_hour); // Start at 8:00 AM
@$endTime = strtotime($time_slot->close_hour); // End at 5:00 PM

while ($startTime < $endTime) {
    $timeSlots[$staff1->staff_id] = date('h:i A', $startTime);
    $startTime = strtotime('+30 minutes', $startTime);
}

					}
				}
				
				asort($timeSlots);
				$staffselected='';
				foreach ($timeSlots as $staff=> $time) {
					$staffselected=$staff;
					break;
				}
			$slots=$this->getTimeSlot($selected_date, $day, $vendor_id,$services, $staffselected);
			 $month=date('m',strtotime($selected_date));
		$year=date('Y',strtotime($selected_date));
		$days = date("t", mktime(0, 0, 0, $month, 1, $year));
		if($staff!='')
		{
				//$day = date('l', strtotime($selected_date));
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->where('staff_id',$staffselected)->get();
				if(count($vendorservices)>0)
				{
					$offdays=[];
					for($i=1;$i<=$days;$i++)
					{
					    $k=str_pad($i, 2, '0', STR_PAD_LEFT);
						$selected_date1=$year."-".$month."-".$k;
						$day = date('l', strtotime($selected_date1));
						$slots1= $this->getTimeSlotbook($selected_date1, $day, $vendor_id,$services, $staffselected);
							
							if(count($slots1)>0)
								{
								}
								else
								{
								$offdays[]=$selected_date1;	
								}
						
						
					}
				}
				
		}
			 $service=DB::table('vendor_services')->select('event')->where('ServicesID',$services)->first();
			 if($service->event==0)
			 {
			$data=array('selected_date'=>$selected_date,
            'time_slot'=>$slots,'offdays'=>@$offdays);	 
			 }
			 else
			 {
				 
				 $schedules = DB::table('vendor_service_schedule')
            ->where('service_id', $services)
            ->orderBy('service_date')
            ->orderBy('service_time_from')
            ->get();

        // Group schedules by date
        $grouped = $schedules->groupBy('service_date');

        $final = [];

        foreach ($grouped as $date => $entries) {
            $maxPersons = $entries->first()->max_persons;

            $bookedCount = 0;
            $times = [];

            foreach ($entries as $entry) {
                $timeFrom = $entry->service_time_from;
                //$timeTo = $entry->service_time_to;
				$newdate=date('Y-m-d H:i:s',strtotime($date." ".$timeFrom));
                // Count active bookings for this time slot
                $count = DB::table('orders')
                    ->whereDate('service_date', $newdate)
                  
                    ->where('vendor_id', function ($q) use ($services) {
                        $q->select('vendor_id')
                          ->from('vendor_services')
                          ->where('ServicesID', $services)
                          ->limit(1);
                    })
                    ->whereIn('status', [1, 6]) // confirmed or active
                    ->count();

                $bookedCount += $count;

                $times[] = [
                    'time' => $timeFrom,
					'disabled'=> false
                    
                ];
            }

            $final[] = [
                'service_id' => $services,
                'date' => $date,
                'times' => $times,
                'max_persons' => $maxPersons,
                'booked_count' => $bookedCount,
				'available_count' =>$maxPersons-$bookedCount, 
                'available' => $bookedCount < ($maxPersons * count($times)),
            ];
        }
			$data=array('selected_date'=>$selected_date,
            'time_slot'=>$times,'offdays'=>$offdays,'event'=>$final);	 
			 }
			 
			if(count($slots)>0)
			{
			
		 $message = array('status'=>'1', 'message'=>'Time Slot', 'data'=>$data);
				return $message;
			}
			else
			{
				$message1=__('messages.NoTimeSlot'); 
		 $message = array('status'=>'1', 'message'=>$message1);
			return $message;
			}
			
			
			}
			else
			{
			$day = date('l', strtotime($selected_date));
			$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->where('staff_id',$staff)->get();
		foreach($vendorservices as $staff1)
		{
			$staff=$staff1->staff_id;
		$slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services, $staff1->staff_id);
		if(count($slots)>0)
		{
			break;
		}
		}
			$slots=$this->getTimeSlot($selected_date, $day, $vendor_id,$services, $staff);
			 
			 $month=date('m',strtotime($selected_date));
		$year=date('Y',strtotime($selected_date));
		$days = date("t", mktime(0, 0, 0, $month, 1, $year));
		if($staff!='')
		{
				//$day = date('l', strtotime($selected_date));
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->where('staff_id',$staff)->get();
				if(count($vendorservices)>0)
				{
					$offdays=[];
					for($i=1;$i<=$days;$i++)
					{
						$k=str_pad($i, 2, '0', STR_PAD_LEFT);
						$selected_date1=$year."-".$month."-".$k;
						$day = date('l', strtotime($selected_date1));
						$slots1= $this->getTimeSlotbook($selected_date1, $day, $vendor_id,$services, $staff);
							if(count($slots1)>0)
								{
								}
								else
								{
								$offdays[]=$selected_date1;	
								}
						
						
					}
				}
				
		}
		
			 $service=DB::table('vendor_services')->select('event')->where('ServicesID',$services)->first();
			 if($service->event==0)
			 {
			$data=array('selected_date'=>$selected_date,
            'time_slot'=>$slots,'offdays'=>$offdays);	 
			 }
			 else
			 {
				 
				 $schedules = DB::table('vendor_service_schedule')
            ->where('service_id', $services)
            ->orderBy('service_date')
            ->orderBy('service_time_from')
            ->get();

        // Group schedules by date
        $grouped = $schedules->groupBy('service_date');

        $final = [];

        foreach ($grouped as $date => $entries) {
            $maxPersons = $entries->first()->max_persons;

            $bookedCount = 0;
            $times = [];

            foreach ($entries as $entry) {
                $timeFrom = $entry->service_time_from;
                //$timeTo = $entry->service_time_to;
				$newdate=date('Y-m-d H:i:s',strtotime($date." ".$timeFrom));
                
				// Count active bookings for this time slot
                $count = DB::table('orders')
                    ->whereDate('service_date', $newdate)
                  
                    ->where('vendor_id', function ($q) use ($services) {
                        $q->select('vendor_id')
                          ->from('vendor_services')
                          ->where('ServicesID', $services)
                          ->limit(1);
                    })
                    ->whereIn('status', [1, 6]) // confirmed or active
                    ->count();

                $bookedCount += $count;

                 $times[] = [
                    'time' => $timeFrom,
					'disabled'=> false
                    
                ];
            }

            $final[] = [
                'service_id' => $services,
                'date' => $date,
                'times' => $times,
                'max_persons' => $maxPersons,
                'booked_count' => $bookedCount,
				'available_count' =>$maxPersons-$bookedCount, 
                'available' => $bookedCount < ($maxPersons * count($times)),
            ];
        }
			$data=array('selected_date'=>$selected_date,
            'time_slot'=>$times,'offdays'=>$offdays,'event'=>$final);	 
			 }
			if(count($slots)>0)
			{
			
		 $message = array('status'=>'1', 'message'=>'Time Slot', 'data'=>$data);
				return $message;
			}
			else
			{
				$message1=__('messages.NoTimeSlot');
		 $message = array('status'=>'0', 'message'=>$message1);
			return $message;
			}	
				
				
				
			}
			
			
			
			
			}
			
			
		public function staff_AvailableSlots(Request $request)
			{
				$i=1;
				$slotsnew=[];
			$vendor_id=	$request->vendor_id;
			$selected_date=$request->date;	
			$services=$request->service;	
			@$staff=$request->staff;
			@$time1=$request->time;
			$delete_status=$request->after_delete;
				

			if($time1!='')
			{	$day = date('l', strtotime($selected_date));
	
				$staffAvailability =[];
				$earliestTimeSlots = [];
				$timeSlots=[];
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->get();
				foreach($vendorservices as $vendorservice)
				{
				$staff1=DB::table('staff_profile')->select('staff_id','staff_name','staff_image')->where('staff_id',$vendorservice->staff_id)->first();	
					if($staff1!=null)
					{
						$time_slot = DB::table('employee_time_slot')
                       ->where('vendor_id',$vendor_id)->where('staff_id',$staff1->staff_id)
                       ->where('days',$day)->where('status',1)
                       ->first();
			   
			    @$startTime = strtotime($time_slot->open_hour); // Start at 8:00 AM
                @$endTime = strtotime($time_slot->close_hour); // End at 5:00 PM
                
                while ($startTime < $endTime) {
                    $timeSlots[$staff1->staff_id] = date('h:i A', $startTime);
                    $startTime = strtotime('+30 minutes', $startTime);
                }

					}
				}
				
				asort($timeSlots);
				$staffselected='';
				foreach ($timeSlots as $staff=> $time) {
					$staffselected=$staff;
					break;
				}
             
				$staffarray=[];
				$day = date('l', strtotime($selected_date));
				
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->get();
			
			$month=date('m',strtotime($selected_date));
		$year=date('Y',strtotime($selected_date));
		$days = date("t", mktime(0, 0, 0, $month, 1, $year));
				if(count($vendorservices)>0)
				{
					$offdays=[];
					for($i=1;$i<=$days;$i++)
					{
						$k=str_pad($i, 2, '0', STR_PAD_LEFT);
						$selected_date1=$year."-".$month."-".$k;
						$day5 = date('l', strtotime($selected_date1));
						$slots1= $this->getTimeSlotbook($selected_date1, $day5, $vendor_id,$services, $staffselected);
							if(count($slots1)>0)
								{
								}
								else
								{
								$offdays[]=$selected_date1;	
								}
						
						
					}
				}
			$slot = [
					'staff_image'=>'public/uploads/no-staff.png',
						'staff_name'=>'No Preference',
                        'staff_id' => 'NULL',
                        'available' => true,
						'message'=> "Available",
						'isselectable'=>true,
						'offdays'=>$offdays
                    ];
					$slotsnew[]=$slot;
				foreach($vendorservices as $vendorservice)
				{
				$staff1=DB::table('staff_profile')->select('staff_id','staff_name','staff_image')->where('staff_id',$vendorservice->staff_id)->first();	
					if($staff1!=null)
					{
				$timeSlots = [];
				
				$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff1->staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			   
			   @$startTime = strtotime($time_slot->open_hour); // Start at 8:00 AM
@$endTime = strtotime($time_slot->close_hour); // End at 5:00 PM

while ($startTime < $endTime) {
    $timeSlots[] = date('h:i A', $startTime);
    $startTime = strtotime('+30 minutes', $startTime);
}
			   $slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services, $staff1->staff_id);
			  
if(count($slots)>0)
					{
					
    					if(in_array($time1,$slots))
    					{	
					$month=date('m',strtotime($selected_date));
		$year=date('Y',strtotime($selected_date));
		$days = date("t", mktime(0, 0, 0, $month, 1, $year));
		
				//$day = date('l', strtotime($selected_date));
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->where('staff_id', $staff1->staff_id)->get();
				if(count($vendorservices)>0)
				{
					$offdays=[];
					for($i=1;$i<=$days;$i++)
					{
						$k=str_pad($i, 2, '0', STR_PAD_LEFT);
						$selected_date1=$year."-".$month."-".$k;
						$day5 = date('l', strtotime($selected_date1));
						$slots1= $this->getTimeSlotbook($selected_date1, $day5, $vendor_id,$services, $staff1->staff_id);
							if(count($slots1)>0)
								{
								}
								else
								{
								$offdays[]=$selected_date1;	
								}
						
						
					}
				}
    									
            					$slot = [
            					'staff_image'=>'public/'.$staff1->staff_image,
            						'staff_name'=>$staff1->staff_name,
                                    'staff_id' => $staff1->staff_id,
                                    'available' => true,
            						'message'=> "Available",
            						'isselectable'=>true,
									'offdays'=>$offdays
                                ];
            					
            					$slotsnew[]=$slot;
    					}
					   else
					    {

                            // Get booked slots for the day
                            $booked_slots = DB::table('order_cart')
                                ->join('orders', 'orders.id', '=', 'order_cart.book_ID')
                                ->select('orders.service_time')
                                ->where('order_cart.vendor_id', $vendor_id)
                                ->where('order_cart.staff_id', $staff1->staff_id)
                                ->whereDate('orders.service_date', now()->toDateString())
                                ->orderBy('orders.service_time', 'asc')
                                ->get()
                                ->map(function ($slot) {
                                    // Split the time range and take the starting time part
                                    $startTime = explode('-', $slot->service_time)[0];
                                    return trim($startTime); // Trim any extra whitespace
                                })
                                ->toArray();
                            
                            // Get time slot details
                            $time_slot = DB::table('employee_time_slot')
                                ->where('vendor_id', $vendor_id)
                                ->where('staff_id', $staff1->staff_id)
                                ->where('days', $day)
                                ->where('status', 1)
                                ->first();
                            
                            $all_slots = []; // Array to store all generated slots
                            
                            if (isset($time_slot)) {
                                $start_time = strtotime($time_slot->open_hour);
                                $end_time = strtotime($time_slot->close_hour);
                                
                                // Get slot duration in minutes
                                $slot_duration = DB::table('vendor_services')
                                    ->select('Service_Time')
                                    ->where('vendor_id', $vendor_id)
                                    ->where('ServicesID', $services)
                                    ->first();
                                
                                $slot_duration_minutes = $slot_duration->Service_Time;
                                $current_time = $start_time;
                            
                                // Generate all available slots
                                while ($current_time < $end_time) {
                                    $slot_start = $current_time;
                                    $current_time += $slot_duration_minutes * 60;
                            
                                    
                                    $all_slots[] = date('h:i A', $slot_start);
                                }
                            }
                            
                            // Filter out slots that are in $booked_slots
                            $available_slots = array_filter($all_slots, function ($slot) use ($booked_slots) {
                                return !in_array($slot, $booked_slots);
                            });
                            
                            // Reindex the array to avoid any gaps in keys
                            $available_slots = array_values($available_slots);
                            
                         foreach ($available_slots as $time) {
                            if (strtotime($time) > strtotime($time1)) {
                                $firstTime = $time;
                                break; // Exit the loop after finding the first match
                            }
                        }
						
								$month=date('m',strtotime($selected_date));
		$year=date('Y',strtotime($selected_date));
		$days = date("t", mktime(0, 0, 0, $month, 1, $year));
		
				//$day = date('l', strtotime($selected_date));
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->where('staff_id', $staff1->staff_id)->get();
				if(count($vendorservices)>0)
				{
					$offdays=[];
					for($i=1;$i<=$days;$i++)
					{
						$k=str_pad($i, 2, '0', STR_PAD_LEFT);
						$selected_date1=$year."-".$month."-".$k;
						$day5 = date('l', strtotime($selected_date1));
						$slots1= $this->getTimeSlotbook($selected_date1, $day5, $vendor_id,$services, $staff1->staff_id);
							if(count($slots1)>0)
								{
								}
								else
								{
								$offdays[]=$selected_date1;	
								}
						
						
					}
				}
									$slot = [
					                     'staff_image'=>'public/'.$staff1->staff_image,
						                 'staff_name'=>$staff1->staff_name,
                                         'staff_id' => $staff1->staff_id,
                                          'available' => false,
						                  'message'=> "Available at ".$firstTime."",
						                   'isselectable'=>true,
										   'offdays'=>$offdays
                                     ];
					               $slotsnew[]=$slot;
				
					    }
					
					}	
					else
					{
							$month=date('m',strtotime($selected_date));
		$year=date('Y',strtotime($selected_date));
		$days = date("t", mktime(0, 0, 0, $month, 1, $year));
		
				//$day = date('l', strtotime($selected_date));
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->where('staff_id', $staff1->staff_id)->get();
				if(count($vendorservices)>0)
				{
					$offdays=[];
					for($i=1;$i<=$days;$i++)
					{
					$k=str_pad($i, 2, '0', STR_PAD_LEFT);
						$selected_date1=$year."-".$month."-".$k;
						$day5 = date('l', strtotime($selected_date1));
						$slots1= $this->getTimeSlotbook($selected_date1, $day5, $vendor_id,$services, $staff1->staff_id);
							if(count($slots1)>0)
								{
								}
								else
								{
								$offdays[]=$selected_date1;	
								}
						
						
					}
				}
						
					$slot = [
					'staff_image'=>'public/'.$staff1->staff_image,
						'staff_name'=>$staff1->staff_name,
                        'staff_id' => $staff1->staff_id,
                        'available' => false,
						'message'=> "Not Working On Selected Date",
						'isselectable'=>false,
						'offdays'=>$offdays
                    ];	
						
					$slotsnew[]=$slot;	
					}
				}
			   
			}
			$data=array('selected_date'=>$selected_date,
            'time_slot'=>$slotsnew);
			
			
		 $message = array('status'=>'1', 'message'=>'Time Slot', 'data'=>$data);
				return $message;
			
			exit();
			}
			else
			{
				$staffarray=[];
				$day = date('l', strtotime($selected_date));
				$day = date('l', strtotime($selected_date));
				$staffAvailability =[];
				$earliestTimeSlots = [];
				$timeSlots=[];$maxSlotsCount = 0;
                $selectedStaffId = null;
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->get();
			 //  foreach($vendorservices as $vendorservice1)
				// {
				//      $slots1= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services, $vendorservice1->staff_id); 
				//      $slotsCount = count($slots1); // Assuming $slots1 is an array or collection of slots
    
    //                     // Check if this staff has more slots than the current maximum
    //                     if ($slotsCount > $maxSlotsCount) {
    //                         $maxSlotsCount = $slotsCount;
    //                         $selectedStaffId = $vendorservice1->staff_id;
    //                     }
				// }
				foreach($vendorservices as $vendorservice)
				{
				   
				$staff1=DB::table('staff_profile')->select('staff_id','staff_name','staff_image')->where('staff_id',$vendorservice->staff_id)->first();	
					if($staff1!=null)
					{
						$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff1->staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			  
			   @$startTime = strtotime($time_slot->open_hour); // Start at 8:00 AM
                    @$endTime = strtotime($time_slot->close_hour); // End at 5:00 PM
                    
                    while ($startTime < $endTime) {
                        $timeSlots[$staff1->staff_id] = date('h:i A', $startTime);
                        $startTime = strtotime('+30 minutes', $startTime);
                    }

					}
				}
				
				asort($timeSlots);
				$staffselected='';
				foreach ($timeSlots as $staff=> $time1) {
					$staffselected=$staff;
					break;
				} 
				$month=date('m',strtotime($selected_date));
		$year=date('Y',strtotime($selected_date));
		$days = date("t", mktime(0, 0, 0, $month, 1, $year));
				if(count($vendorservices)>0)
				{
					$offdays=[];
					for($i=1;$i<=$days;$i++)
					{
					$k=str_pad($i, 2, '0', STR_PAD_LEFT);
						$selected_date1=$year."-".$month."-".$k;
						$day = date('l', strtotime($selected_date1));
						$slots1= $this->getTimeSlotbook($selected_date1, $day, $vendor_id,$services, $staffselected);
							if(count($slots1)>0)
								{
								}
								else
								{
								$offdays[]=$selected_date1;	
								}
						
						
					}
				}
				$slot = [
					'staff_image'=>'public/uploads/no-staff.png',
						'staff_name'=>'No Preference',
                        'staff_id' => 'NULL',
                        'available' => true,
						'message'=> "Available",
						'isselectable'=>true,
						'offdays'=>@$offdays
						
                    ];
					$slotsnew[]=$slot;
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->get();
			
				foreach($vendorservices as $vendorservice)
				{
				$staff1=DB::table('staff_profile')->select('staff_id','staff_name','staff_image')->where('staff_id',$vendorservice->staff_id)->first();	
					if($staff1!=null)
					{
				$timeSlots = [];
				
				$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff1->staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			   
			   @$startTime = strtotime($time_slot->open_hour); // Start at 8:00 AM
@$endTime = strtotime($time_slot->close_hour); // End at 5:00 PM

while ($startTime < $endTime) {
    $timeSlots[] = date('h:i A', $startTime);
    $startTime = strtotime('+30 minutes', $startTime);
}
			   $slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services, $staff1->staff_id);
			   if(count($slots)>0)
					{
					
					$month=date('m',strtotime($selected_date));
		$year=date('Y',strtotime($selected_date));
		$days = date("t", mktime(0, 0, 0, $month, 1, $year));
		
				//$day = date('l', strtotime($selected_date));
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->where('staff_id', $staff1->staff_id)->get();
				if(count($vendorservices)>0)
				{
					$offdays=[];
					for($i=1;$i<=$days;$i++)
					{
						$k=str_pad($i, 2, '0', STR_PAD_LEFT);
						$selected_date1=$year."-".$month."-".$k;
						$day = date('l', strtotime($selected_date1));
						$slots1= $this->getTimeSlotbook($selected_date1, $day, $vendor_id,$services, $staff1->staff_id);
							if(count($slots1)>0)
								{
								}
								else
								{
								$offdays[]=$selected_date1;	
								}
						
						
					}
				}
				
		
									
					$slot = [
					'staff_image'=>'public/'.$staff1->staff_image,
						'staff_name'=>$staff1->staff_name,
                        'staff_id' => $staff1->staff_id,
                        'available' => true,
						'message'=> "Available",
						'isselectable'=>true,
						'offdays'=>$offdays
                    ];
					
					$slotsnew[]=$slot;
					}
					else
					{
						$month=date('m',strtotime($selected_date));
		$year=date('Y',strtotime($selected_date));
		$days = date("t", mktime(0, 0, 0, $month, 1, $year));
		
				//$day = date('l', strtotime($selected_date));
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->where('staff_id', $staff1->staff_id)->get();
				if(count($vendorservices)>0)
				{
					$offdays=[];
					for($i=1;$i<=$days;$i++)
					{
						$k=str_pad($i, 2, '0', STR_PAD_LEFT);
						$selected_date1=$year."-".$month."-".$k;
						$day = date('l', strtotime($selected_date1));
						$slots1= $this->getTimeSlotbook($selected_date1, $day, $vendor_id,$services, $staff1->staff_id);
							if(count($slots1)>0)
								{
								}
								else
								{
								$offdays[]=$selected_date1;	
								}
						
						
					}
				}	
								
						$slot = [
							'staff_image'=>'public/'.$staff1->staff_image,
								'staff_name'=>$staff1->staff_name,
								'staff_id' => $staff1->staff_id,
								'available' => true,
								'message'=> "Not Working On Selected Date",
								'isselectable'=>true,
								'offdays'=>$offdays
							];	
								
							$slotsnew[]=$slot;	
							
				
					}
					
					
					
				}
			   
			}
			$data=array('selected_date'=>$selected_date,
            'time_slot'=>$slotsnew);
			
			
		 $message = array('status'=>'1', 'message'=>'Time Slot', 'data'=>$data);
				return $message;
			
			exit();



			}
			}
			
	public function DeleteItemCart(Request $request)
	{
		$client=$request->user_id;
		$Cart_ID=$request->Cart_ID;
		@$time=$request->time;
		$now = strtotime(date('Y-m-d H:i:s'));
		 $current_time = strtotime($time);
		
        $delete=DB::table('user_cart')->where('user_id',$client)->where('Cart_ID',$Cart_ID)->delete();
         $firstRow = DB::table('user_cart')
        ->where('user_id', $client)
        ->orderBy('Cart_ID', 'asc') 
        ->first();  
		$data=DB::table('user_cart')->where('user_id',$client)->where('Cart_ID',$Cart_ID)->first();
		$dataEdit=DB::table('user_cart')->where('user_id',$client)->get();
		 $exists_cart=DB::table('user_cart')
                            ->where('user_id',$client)->orderBy('Cart_ID', 'asc')
                            ->get();
			$day = date('l', strtotime($firstRow->start_date));
		$slots= $this->getTimeSlotbook(date('Y-m-d',strtotime($firstRow->start_date)), $day,  $firstRow->vendor_id, $firstRow->service_id,   $firstRow->staff_id);
		// Get booked slots for the day
                            $booked_slots = DB::table('order_cart')
                                ->join('orders', 'orders.id', '=', 'order_cart.book_ID')
                                ->select('orders.service_time')
                                ->where('order_cart.vendor_id', $firstRow->vendor_id)
                                ->where('order_cart.staff_id', $firstRow->staff_id)
                                ->whereDate('orders.service_date', now()->toDateString())
                                 ->where('orders.status', 2)
                                ->orderBy('orders.service_time', 'asc')
                                ->get()
                                ->map(function ($slot) {
                                    // Split the time range and take the starting time part
                                    $startTime = explode('-', $slot->service_time)[0];
                                    return trim($startTime); // Trim any extra whitespace
                                })
                                ->toArray();
                            
                            // Get time slot details
                            $time_slot = DB::table('employee_time_slot')
                                ->where('vendor_id', $firstRow->vendor_id)
                                ->where('staff_id', $firstRow->staff_id)
                                ->where('days', $day)
                                ->where('status', 1)
                                ->first();
                          
                            $all_slots = []; // Array to store all generated slots
                            
                            if (isset($time_slot)) {
                                $start_time = strtotime($time_slot->open_hour);
                                $end_time = strtotime($time_slot->close_hour);
                                
                                // Get slot duration in minutes
                                $slot_duration = DB::table('vendor_services')
                                    ->select('Service_Time')
                                    ->where('vendor_id', $firstRow->vendor_id)
                                    ->where('ServicesID', $firstRow->service_id)
                                    ->first();
                                
                                $slot_duration_minutes = $slot_duration->Service_Time;
                                $current_time = $start_time;
                            
                                // Generate all available slots
                                while ($current_time < $end_time) {
                                    $slot_start = $current_time;
                                    $current_time += $slot_duration_minutes * 60;
                            
                                    
                                    $all_slots[] = date('h:i A', $slot_start);
                                }
                            }
                            
                           $current_time = strtotime(now()->format('h:i A'));

                        $available_slots = array_filter($all_slots, function ($slot) use ($booked_slots, $current_time) {
                            return !in_array($slot, $booked_slots);
                        });
                          
                            // Reindex the array to avoid any gaps in keys
                            $available_slots = array_values($available_slots);
                            
                         foreach ($available_slots as $times) {
                            if (strtotime($times) > strtotime($time)) {
                                $firstTime = $times;
                                break; // Exit the loop after finding the first match
                            }
                        }
		
                            if(in_array($time,$slots)){ 
                                 foreach($dataEdit as $keyda=>$dataed){
                                    	$newqueryservice=DB::table('vendor_services')
                                        ->where('vendor_id',$firstRow->vendor_id)->where('ServicesID',$dataed->service_id)
                                        ->first();
                        				$new_datetime=date('Y-m-d',strtotime($dataed->start_date))." ".$time;
                                		$newtime=date('Y-m-d H:i:s',strtotime($new_datetime));
                                		$appointment_start = strtotime($newtime);
                                        $appointment_end = $appointment_start +$newqueryservice->Service_Time*60;
                        				$endtime=date('h:i A',$appointment_end);
                                              DB::table('user_cart')->where('Cart_ID',$dataed->Cart_ID)
                            				->update(['start_time'=>$time,'end_time'=> $endtime,'start_date'=>$newtime]);
                                           $time=$endtime; 	
                                }
                         
            				$count=count($exists_cart);      
                        if (count($exists_cart) >= 2) {
                            for ($i = 0; $i < count($exists_cart) - 1; $i++) {
                        		$start=strtotime($exists_cart[$i + 1]->start_time);
                        		$end=strtotime($exists_cart[$i]->end_time);
                                $Diff = ($start- $end) /60; // Start time difference in minutes
                               $id=$exists_cart[$i + 1]->Cart_ID;
                        	   $insert1 = DB::table('user_cart')->where('Cart_ID',$id)
                            				->update(['delay1'=>$Diff]);
                            }
                        }
                    }
                    else{ 
                        if(in_array($times,$slots))
                			{  
                			  
                			       foreach($dataEdit as $keyda=>$dataed){
                                    	$newqueryservice=DB::table('vendor_services')
                                        ->where('vendor_id',$firstRow->vendor_id)->where('ServicesID',$dataed->service_id)
                                        ->first();
                        				$new_datetime=date('Y-m-d',strtotime($dataed->start_date))." ".$times;
                                		$newtime=date('Y-m-d H:i:s',strtotime($new_datetime));
                                		$appointment_start = strtotime($newtime);
                                        $appointment_end = $appointment_start +$newqueryservice->Service_Time*60;	
                        				$endtime=date('h:i A',$appointment_end);
                                              DB::table('user_cart')->where('Cart_ID',$dataed->Cart_ID)
                            				->update(['start_time'=>$times,'end_time'=> $endtime,'start_date'=>$newtime]);
                                           $times=$endtime; 	
                                    } 
                			    	$count=count($exists_cart);      
                                if (count($exists_cart) >= 2) {
                                    for ($i = 0; $i < count($exists_cart) - 1; $i++) {
                                		$start=strtotime($exists_cart[$i + 1]->start_time);
                                		$end=strtotime($exists_cart[$i]->end_time);
                                        $Diff = ($start- $end) /60; // Start time difference in minutes
                                       $id=$exists_cart[$i + 1]->Cart_ID;
                                	   $insert1 = DB::table('user_cart')->where('Cart_ID',$id)
                                    				->update(['delay1'=>$Diff]);
                                    }
                                }
                			}
                    }
 $message1=__('messages.DeletedSuccessfully');
		$message = array('status'=>'1', 'message'=>$message1);
		return $message;
	}
	public function ViewCart(Request $request)
	{
		$client=$request->user_id;
		
		$vendor_id=	$request->vendor_id;
		$events=DB::table('user_cart')->select('Cart_ID','service_name','service_id','user_id','user_cart.vendor_id','start_date','duration','price','delay1','start_time','end_time','date1','user_cart.staff_id','staff_name',DB::raw("CONCAT('public/',staff_image) AS staff_image"),'designation','nopreference')->join('staff_profile','user_cart.staff_id','=','staff_profile.staff_id')
                ->where('user_id',$client)->where('user_cart.vendor_id',$vendor_id)->orderBy('Cart_ID', 'asc')
                ->get();
				foreach($events as $event)
				{
						if($event->nopreference=='Yes')
						{
						$event->staff_id='NULL';
						$event->staff_name='No Preference';
						$event->staff_image='public/uploads/no-staff.png';						
						}
						else
						{
							$event->staff_id=(string)$event->staff_id;
						}
				}
				if(count($events)>0)
				{
				$message = array('status'=>'1', 'message'=>'View Cart','data'=>$events);
				return $message;
				}
				else
				{
				$message = array('status'=>'1', 'message'=>'There is no Item in Cart');
				return $message;	
					
					
				}
	}
public function UpdateCart(Request $request)
	{
		$date=date('Y-m-d');
		$vendor_id=	$request->vendor_id;
		$selected_date=$request->selected_date;
		$time=$request->time;
		$staff=$request->staff;
		$client=$request->user_id;
		$Cart_ID=$request->Cart_ID;
		$updatedEvents = DB::table('user_cart')
        ->where('user_id', $client)
        ->orderBy('Cart_ID', 'asc')
        ->get();
        $first_updatedEvents=$updatedEvents->first();
		$day = date('l', strtotime($selected_date));
		if($Cart_ID!='')
		{
		
                		$services1=DB::table('user_cart')->where('Cart_ID',$Cart_ID)->get();
                		if($selected_date=='' or !isset($selected_date))
                		{
                			$datenew=$services1[0]->start_date;
                		$day = date('l', strtotime($datenew));
                		$selected_date=date('Y-m-d', strtotime($datenew));;
                		}
                		if($time=='' or !isset($time))
                		{
                		$time=$services1[0]->start_time;	
                		}
                		
                		if($staff=='NULL' or $staff=='')
                		{
                				$nopreference="Yes";
                		}
                		else
                		{
                			$nopreference="No";
                		}
                		
                		if($staff=='NULL' or $staff=='')
                			{
                			$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services1[0]->service_id)->get();
                		foreach($vendorservices as $staff1)
                		{
                			$staff=$staff1->staff_id;
                		$slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services1[0]->service_id, $staff1->staff_id,$client);
                		if(count($slots)>0)
                		{
                			break;
                		}
                		}	
                			}
                			else
                			{
                			$slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services1[0]->service_id, $staff,$client);	
                			}
                		   	//Next day time issue
                		   	
                            $selected_timestamp = strtotime($selected_date . " " . $time);
                    
                            // Get the last available slot and convert to timestamp
                            $last_slot = end($slots); 
                            $last_slot_timestamp = strtotime($selected_date . " " . $last_slot);
                           
                            // Calculate event end time
                            $event_end_time = $selected_timestamp + (DB::table('vendor_services')
                                            ->where('vendor_id',$vendor_id)->where('ServicesID',$services1[0]->service_id)->value('Service_Time') * 60);
                            //   dd($event_end_time,$last_slot_timestamp);               
                            //  if($event_end_time >= $last_slot_timestamp) dd($slots); else dd( $event_end_time,$last_slot_timestamp);
                            // ✅ Correct check: Ensure event end time does not go beyond last slot
                            if ($event_end_time > $last_slot_timestamp) {
                                 $message1=__('closing hour is greater than chosen time');
                            				$message = array('status'=>'0', 'message'=>$message1);
                            				return $message;
                            }
                		   
                		   //End
                		   
                		if(in_array($time,$slots))
                			{
                				
                				
                				$newqueryservice=DB::table('vendor_services')
                                ->where('vendor_id',$vendor_id)->where('ServicesID',$services1[0]->service_id)
                                ->first();
                				$new_datetime=$selected_date." ".$time;
                        		$newtime=date('Y-m-d H:i:s',strtotime($new_datetime));
                        		$appointment_start = strtotime($newtime);
                                $appointment_end = $appointment_start +$newqueryservice->Service_Time*60;
                						
                				$endtime=date('h:i A',$appointment_end);
                		
                				
                				
                			    $insert1 = DB::table('user_cart')->where('Cart_ID',$Cart_ID)
                    				->update(['vendor_id'=>$vendor_id,'staff_id'=>$staff,'start_date'=>$newtime,'duration'=>$newqueryservice->Service_Time,'start_time'=>$time,'end_time'=> $endtime,'date1'=>$date,'nopreference'=>$nopreference]);
                					$servicesdata=array();
                					$events=DB::table('user_cart')
                                ->where('user_id',$services1[0]->user_id)->orderBy('Cart_ID', 'asc')
                                ->get();
                				$count=count($events);
                				if (count($events) >= 2) {
                                    for ($i = 0; $i < count($events) - 1; $i++) {
                                		$start=strtotime($events[$i + 1]->start_time);
                                		$end=strtotime($events[$i]->end_time);
                                        $Diff = ($start- $end) /60; // Start time difference in minutes
                                       $id=$events[$i + 1]->Cart_ID;
                                	   $insert1 = DB::table('user_cart')->where('Cart_ID',$id)
                                    				->update(['delay1'=>$Diff]);
                                    }
                                }
                
                			    $message = array('status'=>'1', 'message'=>'Updated Successfully');
                				//return $message;
                			}
                			else
                			{
                			    
                			    $booked_slots = DB::table('order_cart')
                                                ->join('orders', 'orders.id', '=', 'order_cart.book_ID')
                                                ->select('orders.service_time')
                                                ->where('order_cart.vendor_id', $vendor_id)
                                                ->where('order_cart.staff_id', $staff)
                                                ->whereDate('orders.service_date', now()->toDateString())
                                                ->orderBy('orders.service_time', 'asc')
                                                ->get()
                                                ->map(function ($slot) {
                                                    // Split the time range and take the starting time part
                                                    $startTime = explode('-', $slot->service_time)[0];
                                                    return trim($startTime); // Trim any extra whitespace
                                                })
                                                ->toArray();
                                            
                                            // Get time slot details
                                            $time_slot = DB::table('employee_time_slot')
                                                ->where('vendor_id', $vendor_id)
                                                ->where('staff_id', $staff)
                                                ->where('days', $day)
                                                ->where('status', 1)
                                                ->first();
                                             $cart_first_one_slot=  DB::table('employee_time_slot')
                                                    ->where('vendor_id', $first_updatedEvents->vendor_id)
                                                    ->where('staff_id', $first_updatedEvents->staff_id)
                                                    ->where('days', $day)
                                                    ->where('status', 1)
                                                    ->first(); 
                                            $all_slots = []; // Array to store all generated slots
                                            
                                            if (isset($time_slot)) {
                                                $start_time = strtotime($time_slot->open_hour);
                                                $end_time = strtotime($time_slot->close_hour);
                                                
                                                // Get slot duration in minutes
                                                $slot_duration = DB::table('vendor_services')
                                                    ->select('Service_Time')
                                                    ->where('vendor_id', $vendor_id)
                                                    ->where('ServicesID', $services1[0]->service_id)
                                                    ->first();
                                                
                                                $slot_duration_minutes = $slot_duration->Service_Time;
                                                $current_time = $start_time;
                                            
                                                // Generate all available slots
                                                while ($current_time < $end_time) {
                                                    $slot_start = $current_time;
                                                    $current_time += $slot_duration_minutes * 60;
                                            
                                                    
                                                    $all_slots[] = date('h:i A', $slot_start);
                                                }
                                            }
                                            
                                            // Filter out slots that are in $booked_slots
                                            $available_slots = array_filter($all_slots, function ($slot) use ($booked_slots) {
                                                return !in_array($slot, $booked_slots);
                                            });
                                            
                                            // Reindex the array to avoid any gaps in keys
                                            $available_slots = array_values($available_slots);
                                            
                                         foreach ($available_slots as $time1) {
                                            if (strtotime($time1) > strtotime($time)) {
                                                $firstTime = $time1;
                                                break; // Exit the loop after finding the first match
                                            }
                                        } 
                                if($cart_first_one_slot->open_hour == $time_slot->open_hour){
                       
                               }
                               else{
                                $times1 = new DateTime($cart_first_one_slot->open_hour);
                                $times2 = new DateTime($time_slot->open_hour);
                                
                                    // Calculate the difference
                                $interval = $times1->diff($times2);
                                
                                        // Get the difference in minutes
                                $differenceInMinutes = ($interval->h * 60) + $interval->i;
                                $dateTime = new DateTime($time1); // Create a DateTime object
                                $dateTime->modify("+{$differenceInMinutes} minutes");; // Add 15 minutes
                                $time1 = $dateTime->format('H:i A');
                               }
                            if(empty($time1)){
                                 $message = array('status'=>'1', 'message'=>'Staff is on holiday');
                				//return $message;
                            }
                            if(in_array($time1,$slots))
                			{
                				
                				
                				$newqueryservice=DB::table('vendor_services')
                                ->where('vendor_id',$vendor_id)->where('ServicesID',$services1[0]->service_id)
                                ->first();
                				$new_datetime=$selected_date." ".$time1;
                        		$newtime=date('Y-m-d H:i:s',strtotime($new_datetime));
                        		$appointment_start = strtotime($newtime);
                                $appointment_end = $appointment_start +$newqueryservice->Service_Time*60;
                						
                				$endtime=date('h:i A',$appointment_end);
                		
                				
                				
                			    $insert1 = DB::table('user_cart')->where('Cart_ID',$Cart_ID)
                    				->update(['vendor_id'=>$vendor_id,'staff_id'=>$staff,'start_date'=>$newtime,'duration'=>$newqueryservice->Service_Time,'start_time'=>$time1,'end_time'=> $endtime,'date1'=>$date,'nopreference'=>$nopreference]);
                					$servicesdata=array();
                					$events=DB::table('user_cart')
                                ->where('user_id',$services1[0]->user_id)->orderBy('Cart_ID', 'asc')
                                ->get();
                				$count=count($events);
                				if (count($events) >= 2) {
                                    for ($i = 0; $i < count($events) - 1; $i++) {
                                		$start=strtotime($events[$i + 1]->start_time);
                                		$end=strtotime($events[$i]->end_time);
                                        $Diff = ($start- $end) /60; // Start time difference in minutes
                                       $id=$events[$i + 1]->Cart_ID;
                                	   $insert1 = DB::table('user_cart')->where('Cart_ID',$id)
                                    				->update(['delay1'=>$Diff]);
                                    }
                                }
                
                			    $message = array('status'=>'1', 'message'=>'Updated Successfully');
                			//	return $message;
                			}
                			else{
								 $message1=__('messages.ThereisNoAvailibilityleftonthisDate');
                			    $message = array('status'=>'0', 'message'=>$message1);
                				return $message;
                		    }
                			}
                	return 		$message;
		}
		else
		{
           
                    		foreach($updatedEvents as $key3=>$event)
                    		{
                    			$staff=$event->staff_id;
                    			$service_id=$event->service_id;
                    			$nopreference=$event->nopreference;
                    						
                    		    $slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$service_id, $staff,$client);	
                    			
                            	//Next day time issue
                		   	
                            $selected_timestamp = strtotime($selected_date . " " . $time);
                    
                            // Get the last available slot and convert to timestamp
                            $last_slot = end($slots); 
                            $last_slot_timestamp = strtotime($selected_date . " " . $last_slot);
                           
                            // Calculate event end time
                            $event_end_time = $selected_timestamp + (DB::table('vendor_services')
                                            ->where('vendor_id',$vendor_id)->where('ServicesID',$service_id)->value('Service_Time') * 60);
                            //   dd($event_end_time,$last_slot_timestamp);               
                            //  if($event_end_time >= $last_slot_timestamp) dd($slots); else dd( $event_end_time,$last_slot_timestamp);
                            // ✅ Correct check: Ensure event end time does not go beyond last slot
                            if ($event_end_time > $last_slot_timestamp) {
                                 $message1=__('closing hour is greater than chosen time');
                            				$message = array('status'=>'0', 'message'=>$message1);
                            				return $message;
                            }
                		   
                		   //End
                		   
                    if(in_array($time,$slots))
                    {
                    			
                        					$newqueryservice=DB::table('vendor_services')
                                        ->where('vendor_id',$vendor_id)->where('ServicesID',$service_id)
                                        ->first();
                        				
                        				        $new_datetime=$selected_date." ".$time;
                        		                $newtime=date('Y-m-d H:i:s',strtotime($new_datetime));
                                                $appointment_start = strtotime($newtime);
                                                $appointment_end = $appointment_start +$newqueryservice->Service_Time*60;
                        						
                        						$endtime=date('h:i A',$appointment_end);
                        						$insert1 = DB::table('user_cart')->where('Cart_ID',$event->Cart_ID)
                            				->update(['vendor_id'=>$vendor_id,'start_date'=>$newtime,'duration'=>$newqueryservice->Service_Time,'start_time'=>$time,
                            				'end_time'=> $endtime,'date1'=>$date]); 
                            				$last_recod=DB::table('user_cart')->where('Cart_ID',$event->Cart_ID)->first();
                            				$time=$last_recod->end_time;
                                            $events=DB::table('user_cart')
                                                ->where('user_id',$client)->orderBy('Cart_ID', 'asc')
                                                ->get();
                                            if (count($events) >= 2) {
                                                for ($i = 0; $i < count($events) - 1; $i++) {
                                            		$start=strtotime($events[$i + 1]->start_time);
                                            		$end=strtotime($events[$i]->end_time);
                                                    $Diff = ($start- $end) /60; // Start time difference in minutes
                                                   $id=$events[$i + 1]->Cart_ID;
                                            	   $insert1 = DB::table('user_cart')->where('Cart_ID',$id)
                                                				->update(['delay1'=>$Diff]);
                                                }					
                        					
                        					}
                        					
                    	$message = array('status'=>'1', 'message'=>'Updated Successfully');				
                    }
                    else
                    {
                    			   
                    	 
                    	    $booked_slots = DB::table('order_cart')
                                            ->join('orders', 'orders.id', '=', 'order_cart.book_ID')
                                            ->select('orders.service_time')
                                            ->where('order_cart.vendor_id', $vendor_id)
                                            ->where('order_cart.staff_id', $staff)
                                            ->whereDate('orders.service_date', now()->toDateString())
                                            ->orderBy('orders.service_time', 'asc')
                                            ->get()->map(function ($slot) {
                                                        // Split the time range and take the starting time part
                                                        $startTime = explode('-', $slot->service_time)[0];
                                                        return trim($startTime); // Trim any extra whitespace
                                                    })->toArray();
                                                
                                                // Get time slot details
                            $time_slot = DB::table('employee_time_slot')
                                                    ->where('vendor_id', $vendor_id)
                                                    ->where('staff_id', $staff)
                                                    ->where('days', $day)
                                                    ->where('status', 1)
                                                    ->first();
                            $cart_first_one_slot=  DB::table('employee_time_slot')
                                                    ->where('vendor_id', $first_updatedEvents->vendor_id)
                                                    ->where('staff_id', $first_updatedEvents->staff_id)
                                                    ->where('days', $day)
                                                    ->where('status', 1)
                                                    ->first();               
                            $all_slots = []; // Array to store all generated slots
                                                
                        if (isset($time_slot)) {
                                $start_time = strtotime($time_slot->open_hour);
                                $end_time = strtotime($time_slot->close_hour);
                                                    
                                // Get slot duration in minutes
                                $slot_duration = DB::table('vendor_services')
                                                        ->select('Service_Time')
                                                        ->where('vendor_id', $vendor_id)
                                                        ->where('ServicesID', $service_id)
                                                        ->first();
                                                    
                                $slot_duration_minutes = $slot_duration->Service_Time;
                                $current_time = $start_time;
                                                
                                // Generate all available slots
                                while ($current_time < $end_time) {
                                        $slot_start = $current_time;
                                        $current_time += $slot_duration_minutes * 60;
                                                
                                                        
                                        $all_slots[] = date('h:i A', $slot_start);
                                }
                            }
                                                
                            // Filter out slots that are in $booked_slots
                            $available_slots = array_filter($all_slots, function ($slot) use ($booked_slots) {
                                                    return !in_array($slot, $booked_slots);
                            });
                                                
                            // Reindex the array to avoid any gaps in keys
                            $available_slots = array_values($available_slots);
                            foreach ($available_slots as $time1) {
                                if (strtotime($time1) > strtotime($time)) {
                                    $firstTime = $time1;
                                    break; // Exit the loop after finding the first match
                                }
                            } 
                             if($cart_first_one_slot->open_hour == $time_slot->open_hour){
                       
                               }
                               else{
                                $times1 = new DateTime($cart_first_one_slot->open_hour);
                                $times2 = new DateTime($time_slot->open_hour);
                                
                                    // Calculate the difference
                                $interval = $times1->diff($times2);
                                
                                        // Get the difference in minutes
                                $differenceInMinutes = ($interval->h * 60) + $interval->i;
                                $dateTime = new DateTime($time1); // Create a DateTime object
                                $dateTime->modify("+{$differenceInMinutes} minutes");; // Add 15 minutes
                                $time1 = $dateTime->format('h:i A');
                               }
                            if(empty($time1)){
                                 $message = array('status'=>'1', 'message'=>'Staff is on holiday');
                				//return $message;
                            }
                                if(in_array($time1,$slots))
                    			{
                    			
                    				$newqueryservice=DB::table('vendor_services')
                                    ->where('vendor_id',$vendor_id)->where('ServicesID',$service_id)
                                    ->first();
                    				$new_datetime=$selected_date." ".$time1;
                            		$newtime=date('Y-m-d H:i:s',strtotime($new_datetime));
                            		$appointment_start = strtotime($newtime);
                                    $appointment_end = $appointment_start +$newqueryservice->Service_Time*60;
                    						
                    				$endtime=date('h:i A',$appointment_end);
                    		
                    				
                    				
                    			    $insert1 = DB::table('user_cart')->where('Cart_ID',$event->Cart_ID)
                        				->update(['vendor_id'=>$vendor_id,'start_date'=>$newtime,'duration'=>$newqueryservice->Service_Time,'start_time'=>$time1,'end_time'=> $endtime,'date1'=>$date]);
                    				$servicesdata=array();
                    				$last_recod=DB::table('user_cart')->where('Cart_ID',$event->Cart_ID)->first();
                            		$time=$last_recod->end_time;	
                    					$events=DB::table('user_cart')
                                    ->where('user_id',$client)->orderBy('Cart_ID', 'asc')
                                    ->get();
                    				$count=count($events);
                    				if (count($events) >= 2) {
                                        for ($i = 0; $i < count($events) - 1; $i++) {
                                    		$start=strtotime($events[$i + 1]->start_time);
                                    		$end=strtotime($events[$i]->end_time);
                                            $Diff = ($start- $end) /60; // Start time difference in minutes
                                           $id=$events[$i + 1]->Cart_ID;
                                    	   $insert1 = DB::table('user_cart')->where('Cart_ID',$id)
                                        				->update(['delay1'=>$Diff]);
                                        }
                                    }
                  
                    			    $message = array('status'=>'1', 'message'=>'Updated Successfully');
                    		//	return $message;
                    			}
                    			else{
									 $message1=__('messages.ThereisNoAvailibilityleftonthisDate');
                    			 $message = array('status'=>'0', 'message'=>$message1);
                    			  return $message;
                    			}
                    			
                    			
                }
                    				
                    			
                }
                    	$message = array('status'=>'1', 'message'=>'Updated Successfully');
                    				return $message;
		}
		
	}
	
		public function UpdateRecordCart(Request $request)
	{
	}
	public function DeleteAllCart(Request $request)
	{
		$client=$request->user_id;
	$delete=DB::table('user_cart')->where('user_id',$client)->delete();
	$message = array('status'=>'1', 'message'=>'Cart Item Deleted Successfully');
	return $message;	
	}
	public function addToCart(Request $request)
		
	{
		$date=date('Y-m-d');
		$vendor_id=	$request->vendor_id;
		$selected_date=$request->date;
		$client=$request->user_id;
		
		$time=$request->time;
		$staff=$request->staff;
		$service=$request->services;
		$new=$request->newcart;
		$book_id=$request->book_id;
		$serve=explode(",",$service);
      Log::info('Service' . $service);
      if($new==1)
      {
       $delete=DB::table('user_cart')->where('user_id',$client)->delete(); 
      }
		$newqueryservice=DB::table('vendor_services')->select('*')->where('ServicesID',$service)->first();
			 if($newqueryservice->event==1)
			 {
               $delete=DB::table('user_cart')->where('user_id',$client)->delete();	
               Log::info('Servicenew' . $service);
               $service=$serve;
				$schedules = DB::table('vendor_service_schedule')
        ->where('service_id', $serve)
        ->orderBy('service_date')
        ->orderBy('service_time_from')
        ->first(); 
				 $event_time = $schedules->service_time_from;      // Event start time
$time1=$time;
               $date2=date('Y-m-d H:i:s');
// Combine selected date and user-selected time
$new_datetime = $date2;
$current_time = date('Y-m-d H:i:s', strtotime($new_datetime));

// Combine selected date and event time
$event_datetime = $schedules->service_date." ".$event_time;
$event_time_formatted = date('Y-m-d H:i:s', strtotime($event_datetime));

                Log::info('time1' . $current_time);
                Log::info('time2' . $event_time_formatted);
// Compare
if (strtotime($current_time) >= strtotime($event_time_formatted)) {
$message1=__('messages.Eventstarted');
				 
				$message = array('status'=>'0', 'message'=>$message1);
				return $message;
                 exit();	

}	
			$new_datetime=$selected_date." ".$time1;
               
              Log::info('time' . $new_datetime);  
                
		$newtime=date('Y-m-d H:i:s',strtotime($new_datetime));
		$appointment_start = strtotime($newtime);
		
		
                        $appointment_end = $appointment_start + $newqueryservice->Service_Time*60;
						
						$endtime=date('h:i A',$appointment_end);
               Log::info('time' . $newtime);
			$insert1 = DB::table('user_cart')
    				->insert(['service_name'=>$newqueryservice->Name,'service_id'=>$newqueryservice->ServicesID,'price'=>$newqueryservice->Service_Price,'user_id'=>$client,'vendor_id'=>$vendor_id,'staff_id'=>$staff,'start_date'=>$newtime,'duration'=>$newqueryservice->Service_Time,'start_time'=>$time1,'end_time'=> $endtime,'date1'=>$date,'nopreference'=>'No']);
					$servicesdata=array();
					$events=DB::table('user_cart')
                ->where('user_id',$client)->orderBy('Cart_ID', 'asc')
                ->get();
				
				 $message1=__('messages.AddedSuccessfully');
			$message = array('status'=>'1', 'message'=> $message1);
				return $message;
				
    }	 
				 
				 
			 
			 else
			 {
		
		if(count($serve)==1)
		{
			$services=$service;
		if($new==1)
		{
		$delete=DB::table('user_cart')->where('user_id',$client)->delete();	
		}
		$day = date('l',strtotime($selected_date));
		if($staff=='NULL' or $staff=='')
		{
				$nopreference="Yes";
		}
		else
		{
			$nopreference="No";
		}
		if(@$book_id!='')
		{
		$staffnew1 = DB::table('order_cart')->select('staff_id')->where('book_ID',$book_id)->first();
		$staff=$staffnew1->staff_id;
		}
		if($staff=='NULL' or $staff=='')
			{
				/*
		$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->get();
		foreach($vendorservices as $staff1)
		{
			$staff=$staff1->staff_id;
		$slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services, $staff1->staff_id);
		if(count($slots)>0)
		{
		
			break;
		}
		}*/
          
		$day = date('l', strtotime($selected_date));
				$staffAvailability =[];
				$earliestTimeSlots = [];
				$timeSlots=[];
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->get();
				
				foreach($vendorservices as $vendorservice)
				{
				$staff1=DB::table('staff_profile')->select('staff_id','staff_name','staff_image')->where('staff_id',$vendorservice->staff_id)->first();	
				$booked_slots = DB::table('order_cart')
                                ->join('orders', 'orders.id', '=', 'order_cart.book_ID')
                                ->select('orders.service_time')
                                ->where('order_cart.vendor_id', $vendor_id)->where('order_cart.user_id',$client)
                                ->where('order_cart.staff_id', $vendorservice->staff_id)
                                ->whereDate('orders.service_date', now()->toDateString())
                                ->orderBy('orders.service_time', 'asc')
                                ->get();
					if($staff1!=null)
					{
						$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff1->staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			   
			   @$startTime = strtotime($time_slot->open_hour); // Start at 8:00 AM
                @$endTime = strtotime($time_slot->close_hour); // End at 5:00 PM
                
                while ($startTime < $endTime) {
                    $timeSlots[$staff1->staff_id] = date('h:i A', $startTime);
                    $startTime = strtotime('+30 minutes', $startTime);
                }

					}
				}
				
				asort($timeSlots);
				$staffselected='';
				foreach ($timeSlots as $staff=> $time1) {
					$staffselected=$staff;
					break;
				}
				$slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services, $staffselected,$client);
			
			}
			else
			{
			$slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services, $staff,$client);
			}
		//Next day time issue	
			
        $selected_timestamp = strtotime($selected_date . " " . $time);

        // Get the last available slot and convert to timestamp
        $last_slot = end($slots);
        $last_slot_timestamp = strtotime($selected_date . " " . $last_slot);
        
        // Calculate event end time
        $event_end_time = $selected_timestamp + (DB::table('vendor_services')
                        ->where('vendor_id',$vendor_id)->where('ServicesID',$services)->value('Service_Time') * 60);
        
        // ✅ Correct check: Ensure event end time does not go beyond last slot
        if ($event_end_time > $last_slot_timestamp) {
             $message1=__('closing hour is greater than chosen time');
        				$message = array('status'=>'0', 'message'=>$message1);
        				return $message;
        }
        //End
        
		if(in_array($time,$slots))
			{
				$events=DB::table('user_cart')
                ->where('user_id',$client)->where('service_id',$services)
                ->get();
				if(count($events)>0)
				{
				$message1=__('messages.ServicesAlreadyAddedtoCart');
				 
				$message = array('status'=>'1', 'message'=>$message1);
				return $message;
                 exit();				
				}
				$newqueryservice=DB::table('vendor_services')
                ->where('vendor_id',$vendor_id)->where('ServicesID',$services)
                ->first();
				$new_datetime=$selected_date." ".$time;
        		$newtime=date('Y-m-d H:i:s',strtotime($new_datetime));
        		$appointment_start = strtotime($newtime);
		
		
                $appointment_end = $appointment_start + $newqueryservice->Service_Time*60;
						
				$endtime=date('h:i A',$appointment_end);
	
			     $insert1 = DB::table('user_cart')
    				->insert(['service_name'=>$newqueryservice->Name,'service_id'=>$newqueryservice->ServicesID,'price'=>$newqueryservice->Service_Price,
    				'user_id'=>$client,'vendor_id'=>$vendor_id,'staff_id'=>$staff,'start_date'=>$newtime,'duration'=>$newqueryservice->Service_Time,
    				'start_time'=>$time,'end_time'=> $endtime,'date1'=>$date,'nopreference'=>$nopreference]);
					$servicesdata=array();
					$events=DB::table('user_cart')
                ->where('user_id',$client)->orderBy('Cart_ID', 'asc')
                ->get();
				$count=count($events);
				if (count($events) >= 2) {
                for ($i = 0; $i < count($events) - 1; $i++) {
            		$start=strtotime($events[$i + 1]->start_time);
            		$end=strtotime($events[$i]->end_time);
                    $Diff = ($start- $end) /60; // Start time difference in minutes
                   $id=$events[$i + 1]->Cart_ID;
            	   $insert1 = DB::table('user_cart')->where('Cart_ID',$id)
                				->update(['delay1'=>$Diff]);
                }
                }
			 $message1=__('messages.AddedSuccessfully');
			$message = array('status'=>'1', 'message'=>$message1);
				return $message;
			}
			else
			{
				if(count($slots)==0)
				{
					 $message1=__('messages.ThereisNoAvailibilityleftonthisDate');
				$message = array('status'=>'0', 'message'=>$message1);
				return $message;
				exit();				
				}
				
				$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff)
               ->where('days',$day)->where('status',1)
               ->first();
               
               $first_events=DB::table('user_cart')
                ->where('user_id',$client)->orderBy('Cart_ID', 'asc')
                ->first();
               $eventsdata=DB::table('user_cart')
                ->where('user_id',$client)->orderBy('Cart_ID', 'asc')
                ->get();
                
                if (!empty($eventsdata) && count($eventsdata)>=1) {
        		    $exists_time_slot = DB::table('employee_time_slot')
                   ->where('vendor_id',$vendor_id)->where('staff_id',$first_events->staff_id)
                   ->where('days',$day)->where('status',1)
                   ->first();
                   if($exists_time_slot->open_hour == $time_slot->open_hour){
                       
                   }
                   else{
                    $time1 = new DateTime($exists_time_slot->open_hour);
                    $time2 = new DateTime($time_slot->open_hour);
                
                    // Calculate the difference
                    $interval = $time1->diff($time2);
                
                        // Get the difference in minutes
                    $differenceInMinutes = ($interval->h * 60) + $interval->i;
                    // Add 15 minutes
                   $time2->modify("+{$differenceInMinutes} minutes");
                    // Format back to H:i
                    $time_slot->open_hour = $time2->format('H:i'); 
                   }
                }
               
				$timeSlots = [];
                $startTime = strtotime($time_slot->open_hour); // Start at 8:00 AM
                $endTime = strtotime($time_slot->close_hour); // End at 5:00 PM
                while ($startTime < $endTime) {
                    $timeSlots[] = date('h:i A', $startTime);
                    $startTime = strtotime('+30 minutes', $startTime);
                }
                $time1=$time;
                $index=array_search($time,$timeSlots);
                $index=$index+1;
                label:
                for($i=$index;$i<=count($timeSlots);$i++)
                {
                	$time=$timeSlots[$i];
                	if (date('H.i',strtotime($timeSlots[$i])) < date('H.i',strtotime($time1))) {
                					
                       $time=$timeSlots[$i+1];
                    }
                		if(in_array($time,$slots))
                			{
                				
                			
                				
                				$events=DB::table('user_cart')
                                ->where('user_id',$client)->where('service_id',$services)->where('start_date',$services)
                                ->get();
                				if(count($events)>0)
                				{
									$message1=__('messages.ServicesAlreadyAddedtoCart');
                				$message = array('status'=>'1', 'message'=>$message1);
                				return $message;
                exit();				
                				}
                				$newqueryservice=DB::table('vendor_services')
                                ->where('vendor_id',$vendor_id)->where('ServicesID',$services)
                                ->first();
                                $booked_slots = DB::table('user_cart')
                                                ->select('end_time')->where('user_id',$client)
                                                ->where('vendor_id', $vendor_id)
                                                ->whereDate('start_date', now()->toDateString())
                                                 ->orderBy('end_time', 'asc')->pluck('end_time')
                                                ->toArray();
                                $latest_end_time = collect($booked_slots)->map(fn($time) => \DateTime::createFromFormat('h:i A', $time))->filter()->sortDesc()->first();
                              
                                if ($latest_end_time) {
                                    $latest_end_time_formatted = $latest_end_time->format('h:i A'); // Format as AM/PM
                                } else {
                                    $latest_end_time_formatted = $time;
                                } 
                                 foreach ($slots as $time1) {
                                    if (strtotime($time1) >= strtotime($latest_end_time_formatted)) {
                                        $firstTime = $time1;
                                        break; // Exit the loop after finding the first match
                                    }
                                 }
                                
                				$new_datetime=$selected_date." ".$time1;
                		$newtime=date('Y-m-d H:i:s',strtotime($new_datetime));
                		$appointment_start = strtotime($newtime);
                		
                		
                                        $appointment_end = $appointment_start + $newqueryservice->Service_Time*60;
                						$endtime=date('h:i A',$appointment_end);
                			$insert1 = DB::table('user_cart')
                    				->insert(['service_name'=>$newqueryservice->Name,'service_id'=>$newqueryservice->ServicesID,'price'=>$newqueryservice->Service_Price,'user_id'=>$client,'vendor_id'=>$vendor_id,'staff_id'=>$staff,'start_date'=>$newtime,'duration'=>$newqueryservice->Service_Time,'start_time'=>$time1,'end_time'=> $endtime,'date1'=>$date,'nopreference'=>$nopreference]);
                					$servicesdata=array();
                					$events=DB::table('user_cart')
                                ->where('user_id',$client)->orderBy('Cart_ID', 'asc')
                                ->get();
                				$count=count($events);
                				if (count($events) >= 2) {
                    for ($i = 0; $i < count($events) - 1; $i++) {
                		$start=strtotime($events[$i + 1]->start_time);
                		$end=strtotime($events[$i]->end_time);
                        $Diff = ($start- $end) /60; // Start time difference in minutes
                       $id=$events[$i + 1]->Cart_ID;
                	   $insert1 = DB::table('user_cart')->where('Cart_ID',$id)
                    				->update(['delay1'=>$Diff]);
                    }
                }
                 $message1=__('messages.AddedSuccessfully');
                			$message = array('status'=>'1', 'message'=>$message1);
                				return $message;
                				break;
                			}
                }

				
			}
			
		}
		
		else
		{  
			if($new==1)
		{
		$delete=DB::table('user_cart')->where('user_id',$client)->delete();	
		}
		$times=array();
		foreach($serve as $key=>$services)
			{
		if($staff=='NULL' or $staff=='')
			{
				
          
		$day = date('l', strtotime($selected_date));
				$staffAvailability =[];
				$earliestTimeSlots = [];
				$timeSlots=[];
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->get();
				
				foreach($vendorservices as $vendorservice)
				{
				$staff1=DB::table('staff_profile')->select('staff_id','staff_name','staff_image')->where('staff_id',$vendorservice->staff_id)->first();	
				$booked_slots = DB::table('order_cart')
                                ->join('orders', 'orders.id', '=', 'order_cart.book_ID')
                                ->select('orders.service_time')->where('order_cart.user_id',$client)
                                ->where('order_cart.vendor_id', $vendor_id)
                                ->where('order_cart.staff_id', $vendorservice->staff_id)
                                ->whereDate('orders.service_date', now()->toDateString())
                                ->orderBy('orders.service_time', 'asc')
                                ->get();
					if($staff1!=null)
					{
						$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff1->staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			   
			   @$startTime = strtotime($time_slot->open_hour); // Start at 8:00 AM
@$endTime = strtotime($time_slot->close_hour); // End at 5:00 PM

while ($startTime < $endTime) {
    $timeSlots[$staff1->staff_id] = date('h:i A', $startTime);
    $startTime = strtotime('+30 minutes', $startTime);
}

					}
				}
				
				asort($timeSlots);
				$staffselected='';
				foreach ($timeSlots as $staff=> $time1) {
					$staffselected=$staff;
					break;
				}
				$slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services, $staffselected,$client);
			}
			else
			{
				$day = date('l', strtotime($selected_date));
			$slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services, $staff,$client);
			}
			
	
			$timenew=array_search($time,$slots);
			$times[]=$slots[$key+$timenew];
			}
			$staffnew1 = DB::table('order_cart')->select('staff_id')->where('book_ID',$book_id)->get();
			
			foreach($serve as $key=>$services)
			{
					
		$day = date('l',strtotime($selected_date));
		if($staff=='NULL' or $staff=='')
		{
				$nopreference="Yes";
		}
		else
		{
			$nopreference="No";
		}
		if($staff=='NULL' or $staff=='')
			{
				
          
		$day = date('l', strtotime($selected_date));
				$staffAvailability =[];
				$earliestTimeSlots = [];
				$timeSlots=[];
				$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->get();
				
				foreach($vendorservices as $vendorservice)
				{
				$staff1=DB::table('staff_profile')->select('staff_id','staff_name','staff_image')->where('staff_id',$vendorservice->staff_id)->first();	
				$booked_slots = DB::table('order_cart')
                                ->join('orders', 'orders.id', '=', 'order_cart.book_ID')
                                ->select('orders.service_time')->where('order_cart.user_id',$client)
                                ->where('order_cart.vendor_id', $vendor_id)
                                ->where('order_cart.staff_id', $vendorservice->staff_id)
                                ->whereDate('orders.service_date', now()->toDateString())
                                ->orderBy('orders.service_time', 'asc')
                                ->get();
					if($staff1!=null)
					{
						$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff1->staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			   
			   @$startTime = strtotime($time_slot->open_hour); // Start at 8:00 AM
@$endTime = strtotime($time_slot->close_hour); // End at 5:00 PM

while ($startTime < $endTime) {
    $timeSlots[$staff1->staff_id] = date('h:i A', $startTime);
    $startTime = strtotime('+30 minutes', $startTime);
}

					}
				}
				
				asort($timeSlots);
				$staffselected='';
				foreach ($timeSlots as $staff=> $time1) {
					$staffselected=$staff;
					break;
				}
				$staff=$staffnew1[$key]->staff_id;
				$slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services, $staffselected,$client);
			}
			else
			{
				$staff=$staffnew1[$key]->staff_id;
			$slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services, $staff,$client);
			}
		
			
		if(in_array($times[$key],$slots))
			{
				$events=DB::table('user_cart')
                ->where('user_id',$client)->where('service_id',$services)
                ->get();
				if(count($events)>0)
				{
				$message = array('status'=>'1', 'message'=>'Services Already Added to Cart');
				return $message;
exit();				
				}
				$newqueryservice=DB::table('vendor_services')
                ->where('vendor_id',$vendor_id)->where('ServicesID',$services)
                ->first();
				$new_datetime=$selected_date." ".$times[$key];
		$newtime=date('Y-m-d H:i:s',strtotime($new_datetime));
		$appointment_start = strtotime($newtime);
		
		
                        $appointment_end = $appointment_start + $newqueryservice->Service_Time*60;
						
						$endtime=date('h:i A',$appointment_end);
	$staff1=$staffnew1[$key]->staff_id;
			$insert1 = DB::table('user_cart')
    				->insert(['service_name'=>$newqueryservice->Name,'service_id'=>$newqueryservice->ServicesID,'price'=>$newqueryservice->Service_Price,
    				'user_id'=>$client,'vendor_id'=>$vendor_id,'staff_id'=>$staff1,'start_date'=>$newtime,'duration'=>$newqueryservice->Service_Time,
    				'start_time'=>$times[$key],'end_time'=> $endtime,'date1'=>$date,'nopreference'=>$nopreference]);
					$servicesdata=array();
					$events=DB::table('user_cart')
                ->where('user_id',$client)->orderBy('Cart_ID', 'asc')
                ->get();
				$count=count($events);
				if (count($events) >= 2) {
    for ($i = 0; $i < count($events) - 1; $i++) {
		$start=strtotime($events[$i + 1]->start_time);
		$end=strtotime($events[$i]->end_time);
        $Diff = ($start- $end) /60; // Start time difference in minutes
       $id=$events[$i + 1]->Cart_ID;
	   $insert1 = DB::table('user_cart')->where('Cart_ID',$id)
    				->update(['delay1'=>$Diff]);
    }
}

			//$message = array('status'=>'1', 'message'=>'Added Successfully');
				//return $message;
			}
			else
			{
				if(count($slots)==0)
				{
				$message = array('status'=>'0', 'message'=>'There is no Slot left on the day');
				return $message;
				exit();				
				}
				
				$time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff)
               ->where('days',$day)->where('status',1)
               ->first();
				$timeSlots = [];
$startTime = strtotime($time_slot->open_hour); // Start at 8:00 AM
$endTime = strtotime($time_slot->close_hour); // End at 5:00 PM

while ($startTime < $endTime) {
    $timeSlots[] = date('h:i A', $startTime);
    $startTime = strtotime('+30 minutes', $startTime);
}
$time1=$time;
$index=array_search($time,$timeSlots);
$index=$index+1;
label1:
for($i=$index;$i<=count($timeSlots);$i++)
{
	$time=$timeSlots[$i];
	if (date('H.i',strtotime($timeSlots[$i])) < date('H.i',strtotime($time1))) {
					
       $time=$timeSlots[$i+1];
    }
		if(in_array($times[$key],$slots))
			{
				
			
				
				$events=DB::table('user_cart')
                ->where('user_id',$client)->where('service_id',$services)->where('start_date',$services)
                ->get();
				if(count($events)>0)
				{
				$message = array('status'=>'1', 'message'=>'Services Already Added to Cart');
				return $message;
exit();				
				}
				$newqueryservice=DB::table('vendor_services')
                ->where('vendor_id',$vendor_id)->where('ServicesID',$services)
                ->first();
                $booked_slots = DB::table('user_cart')
                                ->select('end_time')
                                ->where('vendor_id', $vendor_id)->where('user_id',$client)
                                ->whereDate('start_date', now()->toDateString())
                                 ->orderBy('end_time', 'asc')->pluck('end_time')
                                ->toArray();
                $latest_end_time = collect($booked_slots)->map(fn($time) => \DateTime::createFromFormat('h:i A', $times[$key]))->filter()->sortDesc()->first();
                
                if ($latest_end_time) {
                    $latest_end_time_formatted = $latest_end_time->format('h:i A'); // Format as AM/PM
                } else {
                    $latest_end_time_formatted = $times[$key];
                }  
                 foreach ($slots as $time1) {
                    if (strtotime($time1) > strtotime($latest_end_time_formatted)) {
                        $firstTime = $time1;
                        break; // Exit the loop after finding the first match
                    }
                 }
				$new_datetime=$selected_date." ".$time1;
		$newtime=date('Y-m-d H:i:s',strtotime($new_datetime));
		$appointment_start = strtotime($newtime);
		
		
                        $appointment_end = $appointment_start + $newqueryservice->Service_Time*60;
						
						$endtime=date('h:i A',$appointment_end);
						$staff1=$staffnew1[$key]->staff_id;
			$insert1 = DB::table('user_cart')
    				->insert(['service_name'=>$newqueryservice->Name,'service_id'=>$newqueryservice->ServicesID,'price'=>$newqueryservice->Service_Price,'user_id'=>$client,'vendor_id'=>$vendor_id,'staff_id'=>$staff1,'start_date'=>$newtime,'duration'=>$newqueryservice->Service_Time,'start_time'=>$time1,'end_time'=> $endtime,'date1'=>$date,'nopreference'=>$nopreference]);
					$servicesdata=array();
					$events=DB::table('user_cart')
                ->where('user_id',$client)->orderBy('Cart_ID', 'asc')
                ->get();
				$count=count($events);
				if (count($events) >= 2) {
    for ($i = 0; $i < count($events) - 1; $i++) {
		$start=strtotime($events[$i + 1]->start_time);
		$end=strtotime($events[$i]->end_time);
        $Diff = ($start- $end) /60; // Start time difference in minutes
       $id=$events[$i + 1]->Cart_ID;
	   $insert1 = DB::table('user_cart')->where('Cart_ID',$id)
    				->update(['delay1'=>$Diff]);
    }
}

			
			}
}

				
			}
			}
			 $message1=__('messages.AddedSuccessfully');
			$message = array('status'=>'1', 'message'=> $message1);
				return $message;
				
		}
             }
		
	}
	public function bookings(Request $request)
	{
		
	$client=$request->user_id;
	@$coupon_code=$request->coupon_code;
	$paymentstatus="Not Paid";
	$paymentmethod="";
	$serviceat='Salon';
	$date = date('Y-m-d');
	$notes=$request->notes;	
		
	
$totaltime=0;
				$pricetotal=0;
 $orders = 1;
   
   $events=DB::table('user_cart')
                ->where('user_id',$client)->orderBy('Cart_ID', 'asc')
                ->get();
$totcount=count($events);
foreach($events as $key=> $event)
			{
				
				$day = date('l', strtotime($event->start_date));
				$staff=$event->staff_id;
				$time=$event->start_time;
				$vendor_id=$event->vendor_id;
				$services=$event->service_id;
				$selected_date=$event->start_date;
				$pricetotal=$pricetotal+$event->price;
				$users=DB::table('users')->select('user_phone')->where('id',$client)->first();
				@$mobile=@$users->user_phone;
	
	/*
	$slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services, $staff);
	
	if(in_array($time,$slots))
	{
		
		
					
				
	}
	else
	{
	$message = array('status'=>'0', 'message'=>'No Time Slot');
	return $message;	
	}
	*/
	
	
			}
			$coupondiscount=0;
			if($coupon_code!='')
			{
			@$coupondiscount=$this->couponCodeStore($client,$vendor_id,$coupon_code);
			
			}
			$bookdate1=$date;
			$bookID="BOOK".rand().time();
			
			$insert = DB::table('orders')
    				->insertGetId(['user_id'=>$client,'vendor_id'=> $vendor_id,'Bookid'=>$bookID,'total_price'=>$pricetotal-$coupondiscount,'service_date'=>$events[0]->start_date,'payment_method'=>$paymentmethod,'payment_status'=>$paymentstatus,'status'=>3,'payment_gateway'=>$paymentmethod,'bookdate'=>$selected_date,'created_at'=>$bookdate1,'updated_at'=>$bookdate1,'Bookingat'=>$serviceat,'notes'=>$notes,'service_time'=> $events[0]->start_time."-".$events[$totcount-1]->end_time,'mobile'=>$mobile,'coupon_id'=>$coupon_code,'coupon_discount'=>$coupondiscount]);
					
					if($insert!='')
					{
						foreach($events as $key=> $event)
						{
						$insert1 = DB::table('order_cart')
    				->insert(['status'=>3,'service_name'=>$event->service_name,'service_id'=>$event->service_id,'price'=>$event->price,'user_id'=>$event->user_id,'vendor_id'=>$event->vendor_id,'staff_id'=>$event->staff_id,'book_ID'=>$insert,'start_date'=>$event->start_date,'duration'=>$event->duration,'sequence'=>$key]);	
						}
					}
	
	/*
	$ordersuccessed = DB::table('orders')->select('id as bookid','total_price','vendor_id','user_id','service_date','mobile','service_time','status','coupon_id','coupon_discount')
                           ->where('id',$insert)
                           ->first();*/
	$total=$pricetotal-$coupondiscount;
	$link="";
	$link=$this->upayments_method($total,$insert,$client,$vendor_id);
	$ordersuccessed = DB::table('orders')->select('id as bookid','total_price','vendor_id','user_id','service_date','mobile','service_time','status','coupon_id','coupon_discount','link')
                           ->where('id',$insert)
                           ->first();
			$ordersuccessed->link=$link;			   
$message = array('status'=>'1', 'message'=>'Proceed to Payment Gateway', 'data'=>$ordersuccessed );
				return $message;	
					


					
					
}
public  function array_flatten($array) {
    $return = array();
    foreach ($array as $key => $value) {
        if (is_array($value)){
            $return = array_merge($return, $this->array_flatten($value));
        } else {
            $return[$key] = $value;
        }
    }

    return $return;
}

public function rebookings(Request $request)
	{
	
	$vendor=$request->vendor_id;
	$vendor_id=	$request->vendor_id;
	$selected_date=$request->date;
	$serviceat='Salon';
	$client=$request->user_id;
	$services1=$request->services;
	$time1=$request->time;
	$paymentstatus="Paid";
	$paymentmethod="Knet";
	$staff1=$request->staff;
	$date = date('Y-m-d');
	$notes=$request->notes;	
		
	
$totaltime=0;
				$pricetotal=0;
 $orders = 1;
    $duration  = 30;
//check vendor service related Staff
foreach($services1 as $key=>$services)
					
					{
						$staff=@$staff1[$key];
$vendorservices=DB::table('staff_services')->select('staff_id')->where('vendor_id',$vendor_id)->where('Service_ID',$services)->first();
if($staff!='')
{

$staff_id = $staff;
}
else
{
$staff_id = $vendorservices->staff_id;
}	
//get all booked time slots in specified date 
$bookings=DB::table('order_cart')->join('orders', 'orders.id', '=','order_cart.book_ID')
                ->where('staff_id',$staff_id)->where('order_cart.vendor_id',$vendor_id)->where('orders.service_date',$selected_date)
                ->get();
				$slotnew1=[];
				
				foreach($bookings as $booking)
				{
					$times=$booking->service_time;
					
					$timesslots=explode("-",$times);
					$timesslot1=$timesslots[0];
					$timesslot2=$timesslots[1];
					$timesslot1=date("h:i A", strtotime($timesslot1));
					$timesslot2=date("h:i A", strtotime($timesslot2));
					$slotnew1[]=$this->getTimeSlot(30,$timesslot1,$timesslot2);
				}




			
			//checking time slot in specified time slot end		
	$date = date('Y-m-d');//today date
	$current_time = Carbon::Now(); //currenttime
	
	//get employee timeslot based on service
	//$vendorservices=DB::table('vendor_services')->select('Staff_ID','Name')->where('vendor_id',$vendor_id)->where('ServicesID',$services)->first();
	//$staff_id = $vendorservices->Staff_ID;
     $day = date('l', strtotime($selected_date));
       $time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
			if($time_slot=='')
			{
				$message = array('status'=>'1', 'message'=>'No Time Slot Found');
				return $message;
				exit();
			}
			$staffslots=[];
			$starttime  = $time_slot->open_hour;
			$endtime  = $time_slot->close_hour;

				$array_of_time = array ();
				$array_of_time1 = array ();
				$min = 30;
				$currenttime = strtotime ("+".$min." minutes", strtotime($current_time));
				$start_time    = strtotime ($starttime); //change to strtotime
				$end_time      = strtotime ($endtime); //change to strtotime

				$add_mins  = $duration*60;
				//get todays time slot
				if(strtotime($date)==strtotime($selected_date)){
					
					while ($start_time <= $currenttime) // loop between time
								{
							$array_of_time[] = date ("h:i A", $start_time);
							$start_time += $add_mins; // to check endtime
								}
							$new_array_of_time = array ();
							for($i = 0; $i < count($array_of_time) - 1; $i++)
								{
								$new_array_of_time[] = $array_of_time[$i];
								}

						$items=last($new_array_of_time);
						$numbers = explode('-', $items);
						$last_Number = end($numbers);
						 $lastNumber    = strtotime ($last_Number);
						 if($last_Number!= NULL){
						while ($lastNumber <= $end_time) // loop between time
							{
							   $array_of_time1[] = date ("h:i A", $lastNumber);
							   $lastNumber += $add_mins; // to check endtie=me
							}

							$new_array_of_time1 = array ();
							for($i = 1; $i < count($array_of_time1) - 1; $i++)
							{
								  $totorders = DB::table('orders')->select('service_time')
									   ->where('service_date',$selected_date)->get();
									 
									   //->where('service_time',$array_of_time1[$i] . ' - ' . $array_of_time1[$i + 1])
									   //->count();
									   $arraytimes=array();
									 foreach($totorders as $order)
									 {
										$times1=explode("-",$order->service_time);
						$slotnew=$this->getTimeSlot(30,rtrim($times1[0]," "),$times1[1]);
										foreach($slotnew as $slot)
										{
										$arraytimes[]=$slot;
										}
									 }
									
									   if(in_array($array_of_time1[$i],$arraytimes))
									   {
										 
									
									   


								

								   

								}
								else{

									$new_array_of_time1[] = $array_of_time1[$i];
								}
    }			
				
				}
				
				}  //end todays time slot
				
				//else other dates
				 else{
     while ($start_time <= $end_time) // loop between time
    {
       $array_of_time1[] = date ("h:i A", $start_time);
       $start_time += $add_mins; // to check endtie=me
    }

    $new_array_of_time1 = array ();
   for($i = 1; $i < count($array_of_time1) - 1; $i++)
    {
          $totorders = DB::table('orders')->select('service_time')
               ->where('service_date',$selected_date)->get();
			 
               //->where('service_time',$array_of_time1[$i] . ' - ' . $array_of_time1[$i + 1])
               //->count();
			   $arraytimes=array();
			 foreach($totorders as $order)
			 {
				$times1=explode("-",$order->service_time);
$slotnew=$this->getTimeSlot(30,rtrim($times1[0]," "),$times1[1]);
				foreach($slotnew as $slot)
				{
				$arraytimes[]=$slot;
				}
			 }
			
			   if(in_array($array_of_time1[$i],$arraytimes))
			   {
				 
			
			   


        

           

        }
        else{

             $new_array_of_time1[] = $array_of_time1[$i];
        }
    }
 }//end other dates timeslots
	

			
	$newbookedslot=$this->array_flatten($slotnew1);			
		$time3=$time1;		
	
	if(strtotime($date)==strtotime($selected_date))
	{
	if(in_array($time1,$new_array_of_time1))
	{
		if(!in_array($time1,$newbookedslot))
		{
			//insert to database
			foreach($services1 as $key=>$service)
			{
					$price=DB::table('vendor_services')
                ->where('vendor_id',$vendor_id)->where('ServicesID',$service)
                ->first();
				$totaltime=$totaltime+$price->Service_Time;
				if($totaltime==15)
				{
					$totaltime=30;
				}
				if($totaltime==45)
				{
					$totaltime=60;
				}
				$pricetotal=$pricetotal+$price->Service_Price;
					}
					$endtime = date('h:i A',strtotime('+'.$totaltime.' minutes',strtotime($time3)));
					$slotnew1=$this->getTimeSlot(30,$time3,$endtime);
					
					$count=count($services1);
					
					$totime=end($slotnew1);
					
					//$totime=date("h:i a",strtotime($totime));
					
					$totime=explode("-",$totime);
					$fromtime=$time1;
					
					//$fromtime=date("h:i a",strtotime($fromtime));
					$newtime=$fromtime . ' - ' . $totime[0];
					
					$bookID="BOOK".rand().time();
					//$pricetotal=0;
					
					$mobile=DB::table('users')
                ->where('id',$client)
                ->first();
				
					$bookdate=date('Y-m-d');
					$bookdate1=date('Y-m-d h:i:s');
					$insert = DB::table('orders')
    				->insertGetId(['user_id'=>$client,'vendor_id'=> $vendor_id,'Bookid'=>$bookID,'total_price'=>$pricetotal,'service_date'=>$selected_date,'service_time'=>$newtime,'payment_method'=>$paymentmethod,'payment_status'=>$paymentstatus,'status'=>1,'mobile'=>$mobile->user_phone,'payment_gateway'=>$paymentmethod,'bookdate'=>$bookdate,'created_at'=>$bookdate1,'updated_at'=>$bookdate1,'Bookingat'=>$serviceat,'notes'=>$notes]);
					if($insert!='')
					{
						foreach($services1 as $key=>$service)
					{
						$staff=@$staff1[$key];
						$newqueryservice=DB::table('vendor_services')
                ->where('vendor_id',$vendor_id)->where('ServicesID',$service)
                ->first();
				
						$insert1 = DB::table('order_cart')
    				->insert(['service_name'=>$newqueryservice->Name,'service_id'=>$newqueryservice->ServicesID,'price'=>$newqueryservice->Service_Price,'user_id'=>$client,'vendor_id'=>$vendor_id,'staff_id'=>$staff_id,'book_ID'=>$insert]);
					
					}
					
					}
					

				if(@$insert1)   { 
					 $ordersuccessed = DB::table('orders')->select('id as bookid','total_price','vendor_id','user_id','service_date','mobile','service_time','status')
                           ->where('id',$insert)
                           ->first();
			
			$check = DB::table('vendor')->select('vendor.vendor_logo')
               ->where('vendor_id',$vendor_id)
               ->first();
			$notify_image= url('/').'/'.$check->vendor_logo;      
        
                  $notification_title = "Hey ".$mobile->name.", Your Booking has been placed";
                $notification_text = "Booking Successfully Placed: Your Booking id #".$bookID." price KD ".$pricetotal. " is placed Successfully for ".$selected_date."(".$newtime.").";
                
                $date = date('d-m-Y');
        
        
                $getDevice = DB::table('users')
                         ->where('id', $client)
                        ->select('device_id')
                        ->first();
                $created_at = Carbon::now();
        
                if($getDevice){
                
                
                $getFcm = DB::table('fcm_key')
                            ->first();
                            
                $getFcmKey = $getFcm->user_app_key;
                $fcmUrl = 'https://fcm.googleapis.com/v1/projects/bookme-f44a4/messages:send';
                $token = $getDevice->device_id;
                    
                    $notification = [
                        'title' => $notification_title,
                        'body' => $notification_text,
                         'image'=>$notify_image,
                        'sound' => true,
                    ];
                    
                    $extraNotificationData = ["message" => $notification];
        
                    $fcmNotification = [
                        'to'        => $token,
                        'notification' => $notification,
                        'data' => $extraNotificationData,
                    ];
        
                    $headers = [
                        'Authorization: Bearer='.$getFcmKey,
                        'Content-Type: application/json'
                    ];
        
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL,$fcmUrl);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fcmNotification));
                    $result = curl_exec($ch);
                    curl_close($ch);
                    
             
                $dd = DB::table('user_notification')
                    ->insert(['user_id'=>$client,
                     'noti_title'=>$notification_title,
                     'noti_message'=>$notification_text,
                     'image'=>$notify_image,
                     'created_at'=>Carbon::now()]);
					 }
			
		 $message = array('status'=>'1', 'message'=>'Proceed to Checkout', 'data'=>$ordersuccessed );
				return $message;	}	
		}
		else
		{
			 
		$message = array('status'=>'1', 'message'=>'Slot Already Booked');
        return $message;	
		}
	}
	else
		{
		$message = array('status'=>'1', 'message'=>'Slot Already Booked');
        return $message;	
		}
	}
	else
	{
	if(in_array($time1,$new_array_of_time1))
	{
		
		if(!in_array($time1,$newbookedslot))
		{
			
		
		 foreach($services1 as $key=>$service)
			{
					$price=DB::table('vendor_services')
                ->where('vendor_id',$vendor_id)->where('ServicesID',$service)
                ->first();
				$totaltime=$totaltime+$price->Service_Time;
				if($totaltime==15)
				{
					$totaltime=30;
				}
				if($totaltime==45)
				{
					$totaltime=60;
				}
				$pricetotal=$pricetotal+$price->Service_Price;
					}
					$endtime = date('h:i A',strtotime('+'.$totaltime.' minutes',strtotime($time3)));
					$slotnew1=$this->getTimeSlot(30,$time3,$endtime);
					
					$count=count($services1);
					
					$totime=end($slotnew1);
					
					//$totime=date("h:i a",strtotime($totime));
					
					$totime=explode("-",$totime);
					$fromtime=$time1;
					
					//$fromtime=date("h:i a",strtotime($fromtime));
					$newtime=$fromtime . ' - ' . $totime[0];
					
					$bookID="BOOK".rand().time();
					//$pricetotal=0;
					
					$mobile=DB::table('users')
                ->where('id',$client)
                ->first();
				
					$bookdate=date('Y-m-d');
					$bookdate1=date('Y-m-d h:i:s');
					$insert = DB::table('orders')
    				->insertGetId(['user_id'=>$client,'vendor_id'=> $vendor_id,'Bookid'=>$bookID,'total_price'=>$pricetotal,'service_date'=>$selected_date,'service_time'=>$newtime,'payment_method'=>$paymentmethod,'payment_status'=>$paymentstatus,'status'=>1,'mobile'=>$mobile->user_phone,'payment_gateway'=>$paymentmethod,'bookdate'=>$bookdate,'created_at'=>$bookdate1,'updated_at'=>$bookdate1,'Bookingat'=>$serviceat,'notes'=>$notes]);
					if($insert!='')
					{
						foreach($services1 as $key=>$service)
					{
						$staff=@$staff1[$key];
						$newqueryservice=DB::table('vendor_services')
                ->where('vendor_id',$vendor_id)->where('ServicesID',$service)
                ->first();
				
						$insert1 = DB::table('order_cart')
    				->insert(['service_name'=>$newqueryservice->Name,'service_id'=>$newqueryservice->ServicesID,'price'=>$newqueryservice->Service_Price,'user_id'=>$client,'vendor_id'=>$vendor_id,'staff_id'=>$staff_id,'book_ID'=>$insert]);
					
					}
					
					}
					

				if(@$insert1)   { 
					 $ordersuccessed = DB::table('orders')->select('id as bookid','total_price','vendor_id','user_id','service_date','mobile','service_time','status')
                           ->where('id',$insert)
                           ->first();
			
			$check = DB::table('vendor')->select('vendor.vendor_logo')
               ->where('vendor_id',$vendor_id)
               ->first();
			$notify_image= url('/').'/'.$check->vendor_logo;      
        
                  $notification_title = "Hey ".$mobile->name.", Your Booking has been placed";
                $notification_text = "Booking Successfully Placed: Your Booking id #".$bookID." price KD ".$pricetotal. " is placed Successfully for ".$selected_date."(".$newtime.").";
                
                $date = date('d-m-Y');
        
        
                $getDevice = DB::table('users')
                         ->where('id', $client)
                        ->select('device_id')
                        ->first();
                $created_at = Carbon::now();
        
                if($getDevice){
                
                
                $getFcm = DB::table('fcm_key')
                            ->first();
                            
                $getFcmKey = $getFcm->user_app_key;
                $fcmUrl = 'https://fcm.googleapis.com/v1/projects/bookme-f44a4/messages:send';
                $token = $getDevice->device_id;
                    
                    $notification = [
                        'title' => $notification_title,
                        'body' => $notification_text,
                         'image'=>$notify_image,
                        'sound' => true,
                    ];
                    
                    $extraNotificationData = ["message" => $notification];
        
                    $fcmNotification = [
                        'to'        => $token,
                        'notification' => $notification,
                        'data' => $extraNotificationData,
                    ];
        
                    $headers = [
                        'Authorization: Bearer='.$getFcmKey,
                        'Content-Type: application/json'
                    ];
        
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL,$fcmUrl);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fcmNotification));
                    $result = curl_exec($ch);
                    curl_close($ch);
                    
             
                $dd = DB::table('user_notification')
                    ->insert(['user_id'=>$client,
                     'noti_title'=>$notification_title,
                     'noti_message'=>$notification_text,
                     'image'=>$notify_image,
                     'created_at'=>Carbon::now()]);
					 }
			
		 $message = array('status'=>'1', 'message'=>'Proceed to Checkout', 'data'=>$ordersuccessed );
				return $message;	}		
		}
		else
		{
			
		$message = array('status'=>'1', 'message'=>'Slot Already Booked');
        return $message;	
		}
	}
	else
		{
		$message = array('status'=>'1', 'message'=>'Slot Already Booked');
        return $message;	
		}	
	}
	
	
	
	
	
	
	
	
	
	}
					


					
					
}

public function downloadPDF(Request $request)
    {
       $user_id=$request->user_id;
	   $bookid=$request->bookid;
$bookings = DB::table('orders')->select('orders.*','vendor.*','areas.*','users.id as userid','users.user_phone','users.name','users.email','users.address','users.aboutme','orders.Bookid')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')->join('users', 'orders.user_id', '=','users.id')->join('areas', 'vendor.area', '=','areas.Area_id')->where('orders.id',$bookid)->first();
        $staffs=DB::table('staff_profile')->where('vendor_id',$bookings->vendor_id)->get();
			$bookingdetails = DB::table('order_cart')->where('book_ID', $bookid)->where('vendor_id',$bookings->vendor_id)->get();
			$bookings = DB::table('orders')->select('orders.*','vendor.*','areas.*','users.id as userid','users.user_phone','users.name','users.email','users.address','users.aboutme','orders.Bookid')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')->join('users', 'orders.user_id', '=','users.id')->join('areas', 'vendor.area', '=','areas.Area_id')->where('orders.id',$bookid)->first();
			   $data = [
            'staffs' => $staffs,
            'bookingdetails' => $bookingdetails,
            'bookings' => $bookings
        ]; 
			  $pdf = PDF::loadView('myPDF', $data);
			
            
       
     
        return $pdf->download('receipt-bookme.pdf');
    }

public function upcoming_orders(Request $request)
{
	$user_id=$request->user_id;
	$lat=$request->lat;
	$lng=$request->lng;
	$ongoing=DB::table('orders')
            ->join('vendor','orders.vendor_id','=','vendor.vendor_id')
            ->join('users','orders.user_id','=','users.id')
           ->join('areas','vendor.area','=','areas.Area_id')
		   ->select('users.name','cancel','cancelduration','total_price','status','remindme','orders.id','service_date','service_time','payment_method','payment_status','vendor_name','owner','orders.vendor_id','vendor_email','vendor_phone','vendor_logo',DB::raw("concat(Area_Title, ',', vendor.block,',',vendor.street,',',vendor.avenue,',',vendor.zipcode) as address"),'vendor.lat','vendor.lng','opening_time','closing_time',DB::raw("6371 * acos(cos(radians(".$lat . ")) 
                    * cos(radians(vendor.lat)) 
                    * cos(radians(vendor.lng) - radians(" . $lng . ")) 
                    + sin(radians(" .$lat. ")) 
                    * sin(radians(vendor.lat))) AS distance"))
               
              ->where('orders.user_id',$user_id)
             
              ->whereIn('status',[1,6])->whereDate('service_date','>=',date('Y-m-d'))
              ->orderBy('orders.id', 'DESC')
               ->get();
			   
			    if(count($ongoing)>0){
      foreach($ongoing as $ongoings){
		  $cancel=$ongoings->cancel;
		  $duration=$ongoings->cancelduration;
		  $date2=date('Y-m-d H:i:s');
		  $servicetime=explode('-',$ongoings->service_time);
		  $time1=$servicetime[0];
		  $time2=date("Y-m-d H:i:s", strtotime($time1));
		  $date1=$ongoings->service_date;
		  $vendor_id=$ongoings->vendor_id;
		  	$date12 = strtotime($ongoings->service_date);
            $date22 = strtotime(date('Y-m-d H:i'));
            
            $diffInSeconds = abs($date22 - $date12); // Difference in seconds
            
            // To get the difference in hours
            $diffInHours = $diffInSeconds / 3600;
// 		 	$dStart = new DateTime($ongoings->service_date);
// $dEnd = new DateTime($time2);
// $dDiff = $dStart->diff($dEnd);
// $dDiff=$dDiff->format('%h.%i');
		  //$time = strtotime($date2) - strtotime($date1);
		  
		  //$from = new DateTime($date1);
//$to = new DateTime($date2);

//$time= $from->diff($to)->format('%h.%i');
			//if it 0 no cancel button will show
			$time = round((strtotime($date1) - strtotime($date2))/3600, 1);
			
		  if($diffInHours <= $duration) {
			$cancel=0;  
		  }
		  $order = DB::table('order_cart')->join('staff_profile','staff_profile.staff_id','=','order_cart.staff_id')
            ->select('order_cart_id','service_name','service_id','book_ID','user_id','order_cart.vendor_id','status','price','staff_name','staff_profile.staff_id')
            ->where('book_ID',$ongoings->id)
            ->get();
			$services=DB::table('order_cart')->join('vendor_services','vendor_services.ServicesID','=','order_cart.service_id')
            ->select('vendor_services.ServicesID','order_cart.staff_id','vendor_services.Name','vendor_services.Service_Price','vendor_services.Service_Time','vendor_services.vendor_id','vendor_services.enabled','vendor_services.event')
            ->where('book_ID',$ongoings->id)->where('vendor_services.vendor_id',$vendor_id)
            ->get();
			$date=date('Y-m-d',strtotime($ongoings->service_date));
			   $data[]=array('user_name'=>$ongoings->name,'vendor_name'=>$ongoings->vendor_name, 'vendor_phone'=>$ongoings->vendor_phone, 'vendor_email'=>$ongoings->vendor_email, 'vendor_logo'=>$ongoings->vendor_logo, 'service_date'=>$date, 'service_time'=>$ongoings->service_time,'payment_method'=>$ongoings->payment_method,'payment_status'=>$ongoings->payment_status, 'price'=>$ongoings->total_price,'status'=>$ongoings->status,'vendor_id'=>$ongoings->vendor_id,'address'=>$ongoings->address,'distance'=>ceil($ongoings->distance),'remindme'=>$ongoings->remindme,'book_id'=>$ongoings->id,'cancel'=>$cancel,'order_details'=>$order,'services'=>$services);
			   
	  }
				}
			    if(count($ongoing)>0){
					$message = array('status'=>'1', 'message'=>'All Upcoming Bookings', "data"=>$data);
          return $message;
				}
				
       
        else{
			 
            $message = array('status'=>'1', 'message'=>'No Scheduled Bookings Found');
          return $message;
        }
	
}


public function completed_orders(Request $request)
{
	$user_id=$request->user_id;
	$lat=$request->lat;
	$lng=$request->lng;
	$ongoing=DB::table('orders')
            ->join('vendor','orders.vendor_id','=','vendor.vendor_id')
            ->join('users','orders.user_id','=','users.id')
           ->join('areas','vendor.area','=','areas.Area_id')
		   ->select('users.name','total_price','status','remindme','orders.id','service_date','service_time','payment_method','payment_status','vendor_name','owner','orders.vendor_id','vendor_email','vendor_phone','vendor_logo',DB::raw("concat(Area_Title, ',', vendor.block,',',vendor.street,',',vendor.avenue,',',vendor.zipcode) as address"),'vendor.lat','vendor.lng','opening_time','closing_time',DB::raw("6371 * acos(cos(radians(".$lat . ")) 
                    * cos(radians(vendor.lat)) 
                    * cos(radians(vendor.lng) - radians(" . $lng . ")) 
                    + sin(radians(" .$lat. ")) 
                    * sin(radians(vendor.lat))) AS distance"))
               
              ->where('orders.user_id',$user_id)
             
                ->whereIn('status',[2])
              ->orderBy('orders.id', 'DESC')
               ->get();
			    if(count($ongoing)>0){
      foreach($ongoing as $ongoings){
		   $vendor_id=$ongoings->vendor_id;
		  $order = DB::table('order_cart')->join('staff_profile','staff_profile.staff_id','=','order_cart.staff_id')
            ->select('order_cart_id','service_name','service_id','book_ID','user_id','order_cart.vendor_id','status','price','staff_name','staff_profile.staff_id')
            ->where('book_ID',$ongoings->id)
            ->get();
			$services=DB::table('order_cart')->join('vendor_services','vendor_services.ServicesID','=','order_cart.service_id')
            ->select('vendor_services.ServicesID','vendor_services.Name','vendor_services.Service_Price','vendor_services.Service_Time','vendor_services.vendor_id','vendor_services.enabled','vendor_services.event')
            ->where('book_ID',$ongoings->id)->where('vendor_services.vendor_id',$vendor_id)
            ->get();
			$date=date('Y-m-d',strtotime($ongoings->service_date));
			   $data[]=array('user_name'=>$ongoings->name,'vendor_name'=>$ongoings->vendor_name, 'vendor_phone'=>$ongoings->vendor_phone, 'vendor_email'=>$ongoings->vendor_email, 'vendor_logo'=>$ongoings->vendor_logo, 'service_date'=>$date, 'service_time'=>$ongoings->service_time,'payment_method'=>$ongoings->payment_method,'payment_status'=>$ongoings->payment_status, 'price'=>$ongoings->total_price,'status'=>$ongoings->status,'vendor_id'=>$ongoings->vendor_id,'address'=>$ongoings->address,'distance'=>ceil($ongoings->distance),'remindme'=>$ongoings->remindme,'book_id'=>$ongoings->id,'order_details'=>$order,'services'=>$services);
			   
	  }
				}
			    if(count($ongoing)>0){
					$message = array('status'=>'1', 'message'=>'All Completed Bookings', "data"=>$data);
          return $message;
				}
				
       
        else{
            $message = array('status'=>'1', 'message'=>'No Completed Bookings Found');
          return $message;
        }
	
}


public function cancelled_orders(Request $request)
{
	$user_id=$request->user_id;
	$lat=$request->lat;
	$lng=$request->lng;
	$ongoing=DB::table('orders')
            ->join('vendor','orders.vendor_id','=','vendor.vendor_id')
            ->join('users','orders.user_id','=','users.id')
           ->join('areas','vendor.area','=','areas.Area_id')
		   ->select('users.name','total_price','status','remindme','orders.id','service_date','service_time','payment_method','payment_status','vendor_name','owner','orders.vendor_id','vendor_email','vendor_phone','vendor_logo',DB::raw("concat(Area_Title, ',', vendor.block,',',vendor.street,',',vendor.avenue,',',vendor.zipcode) as address"),'vendor.lat','vendor.lng','opening_time','closing_time',DB::raw("6371 * acos(cos(radians(".$lat . ")) 
                    * cos(radians(vendor.lat)) 
                    * cos(radians(vendor.lng) - radians(" . $lng . ")) 
                    + sin(radians(" .$lat. ")) 
                    * sin(radians(vendor.lat))) AS distance"))
               
              ->where('orders.user_id',$user_id)
             
                ->whereIn('status',[4])
              ->orderBy('orders.id', 'DESC')
               ->get();
			    if(count($ongoing)>0){
      foreach($ongoing as $ongoings){
		   $vendor_id=$ongoings->vendor_id;
		  $order = DB::table('order_cart')->join('staff_profile','staff_profile.staff_id','=','order_cart.staff_id')
            ->select('order_cart_id','service_name','service_id','book_ID','user_id','order_cart.vendor_id','status','price','staff_name','staff_profile.staff_id')
            ->where('book_ID',$ongoings->id)
            ->get();
			$services=DB::table('order_cart')->join('vendor_services','vendor_services.ServicesID','=','order_cart.service_id')
            ->select('vendor_services.ServicesID','vendor_services.Name','vendor_services.Service_Price','vendor_services.Service_Time','vendor_services.vendor_id','vendor_services.enabled')
            ->where('book_ID',$ongoings->id)->where('vendor_services.vendor_id',$vendor_id)
            ->get();
			$date=date('Y-m-d',strtotime($ongoings->service_date));
			   $data[]=array('user_name'=>$ongoings->name,'vendor_name'=>$ongoings->vendor_name, 'vendor_phone'=>$ongoings->vendor_phone, 'vendor_email'=>$ongoings->vendor_email, 'vendor_logo'=>$ongoings->vendor_logo, 'service_date'=>$date, 'service_time'=>$ongoings->service_time,'payment_method'=>$ongoings->payment_method,'payment_status'=>$ongoings->payment_status, 'price'=>$ongoings->total_price,'status'=>$ongoings->status,'vendor_id'=>$ongoings->vendor_id,'address'=>$ongoings->address,'distance'=>ceil($ongoings->distance),'remindme'=>$ongoings->remindme,'book_id'=>$ongoings->id,'order_details'=>$order,'services'=>$services);
			   
	  }
				}
	
			    if(count($ongoing)>0){
					$message = array('status'=>'1', 'message'=>'All Cancelled Bookings', "data"=>$data);
          return $message;
				}
				
       
        else{
            $message = array('status'=>'1', 'message'=>'No Cancelled Bookings Found');
          return $message;
        }
	
}

 public function orders_cancelled(Request $request){
    
	$order_id=$request->order_id;
	$user_id=$request->user_id;
	$status=4;
	$order=DB::table('orders')->where('id',$order_id)->where('user_id',$user_id)->first();
	$vendor_id=$order->vendor_id;
	$vendordetails=DB::table('vendor')->where('vendor_id',$vendor_id)->first();
	$time=explode("-",$order->service_time);
	$timenew=rtrim($time[0]," ");
	$time1=date("H:i",strtotime($timenew));
	$today=date('Y-m-d H:i');
	$date12 = strtotime($order->service_date);
$date22 = strtotime(date('Y-m-d H:i'));

$diffInSeconds = abs($date22 - $date12); // Difference in seconds


$diffInHours = $diffInSeconds / 3600; 

$cancelduration=$vendordetails->cancelduration;
$commision=0;
	if($vendordetails->cancel==1)
	{
		if($diffInHours >= $cancelduration)
		{
       $update=DB::table('orders')->where('id',$order_id)->where('user_id',$user_id)
                                ->update
    				(['status'=>$status]);
					if($update)
					{
					    $commision=($order->total_price*$vendordetails->cancelfee)/100;
						DB::table('refunds')
    				->insert(['order_id'=>$order_id,'refund_amount'=>$commision]);    
					$message = array('status'=>'1', 'message'=>'Order is Cancelled Successfully');
          return $message;	
					}
					else
					{
					$message = array('status'=>'1', 'message'=>'Order Cancellation Failed');
					return $message;
					}
		}
		else
		{
		$message = array('status'=>'1', 'message'=>'Order not Allowed to Cancel');
					return $message;	
			
		}
	}
	else
	{
	$message = array('status'=>'1', 'message'=>'Order not Allowed to Cancel');
					return $message;	
		
	}
 }
 
 public function orders_modify(Request $request){
    
	$order_id=$request->order_id;
	$user_id=$request->user_id;
	$date=$request->date;
	$time1=$request->time;
	$status=4;
	$order=DB::table('orders')->where('id',$order_id)->where('user_id',$user_id)->first();
	$vendor_id=$order->vendor_id;
	$vendordetails=DB::table('vendor')->where('vendor_id',$vendor_id)->first();
	
	$today=date('Y-m-d H:i:s');
	$dStart = new DateTime($order->service_date);
	$dEnd = new DateTime($today);
	$dDiff = $dStart->diff($dEnd);
	$dDiff=$dDiff->format('%h.%i');
	$cancelduration=$vendordetails->cancelduration;
	if($vendordetails->cancel==1 and $vendordetails->modifydate==1 and $dDiff >= $cancelduration)
	{
							$events=DB::table('order_cart')
                ->where('user_id',$user_id)->where('book_ID',$order_id)->orderBy('book_ID', 'asc')
                ->get();
$totcount=count($events);
$times=array();

foreach($events as $key=> $event)
			{
				
				$day = date('l', strtotime($date));
				$staff=$event->staff_id;
				
				$vendor_id=$event->vendor_id;
				$services=$event->service_id;
				$selected_date=$event->start_date;
				//$pricetotal=$pricetotal+$event->price;
				$users=DB::table('users')->select('user_phone')->where('id',$user_id)->first();
				@$mobile=@$users->user_phone;
	$slots= $this->getTimeSlotbook($date, $day, $vendor_id,$services, $staff);
			}
			foreach($events as $key=> $event)
			{
				$timenew=array_search($time1,$slots);
				
				$times[]=$slots[$key+$timenew];
			}
			foreach($events as $key=> $event)
			{
	if(in_array($times[$key],$slots))
	{
		$starttime=date('H:i:s',strtotime($times[$key]));
		$startdate=date('Y-m-d H:i:s',strtotime($date." ".$starttime));
	$eventsnew=DB::table('order_cart')
                ->where('user_id',$user_id)->where('order_cart_id',$event->order_cart_id)->update(['start_date'=>$startdate]);
                	
		
					
				
	}
	else
	{
	$message = array('status'=>'1', 'message'=>'No Time Slot');
	return $message;	
	}
	
	
	
			}
			
			$events=DB::table('order_cart')
                ->where('user_id',$user_id)->where('book_ID',$order_id)->orderBy('order_cart_id', 'asc')
                ->first();
			$appointment_start=$events->start_date;
			$eventsnew1=DB::table('orders')
                ->where('id',$order_id)->update(['service_date'=>$appointment_start]);
				$events2=DB::table('order_cart')
                ->where('user_id',$user_id)->where('book_ID',$order_id)->orderBy('order_cart_id', 'desc')
                ->first();
				 @$appointment_start = strtotime($events->start_date);
			$starttime=date('h:i A',strtotime($events->start_date));
			$appointment_end = $appointment_start + $events2->duration*60;
						
						$endtime=date('h:i A',$appointment_end);
				
					$service_time=$starttime."-".$endtime;

$eventsnew1=DB::table('orders')
                ->where('id',$order_id)->update(['service_time'=>$service_time]);
				$message = array('status'=>'1', 'message'=>'Order Modified Successfully');
          return $message;	
	
	}
	else
	{
	$message = array('status'=>'1', 'message'=>'Order Cannot Modify');
	return $message;	
	}
	
	
      
 }
		
  public function transactionhistory(Request $request)
  {
	  $user_id=$request->user_id;
	  $date=date('Y-m-d');
	  $datename="Today";
	 $bookings1=DB::table('order_cart')->join('orders', 'orders.id', '=','order_cart.book_ID')->select('order_cart.service_name','order_cart.price','orders.created_at','orders.bookdate')
                ->where('order_cart.user_id',$user_id)->where('orders.bookdate',$date)
                ->get();

$date=date('Y-m-d',strtotime("-1 days"));
	 $bookings2=DB::table('order_cart')->join('orders', 'orders.id', '=','order_cart.book_ID')->select('order_cart.service_name','order_cart.price','orders.created_at','orders.bookdate')
                ->where('order_cart.user_id',$user_id)->where('orders.bookdate',$date)
                ->get();

$date=date('Y-m-d',strtotime("-2 days"));
 $bookings3=DB::table('order_cart')->join('orders', 'orders.id', '=','order_cart.book_ID')->select('order_cart.service_name','order_cart.price','orders.created_at','orders.bookdate')
                ->where('order_cart.user_id',$user_id)->where('orders.bookdate','<=',$date)
                ->get();
				
				//$bookings=array_merge($bookings1,$bookings2,$bookings3);
				$bookingsnew=$bookings1->merge($bookings2);
				$bookings=$bookingsnew->merge($bookings3);
				 $pr = [];
$i=0;				  
        foreach($bookings as $booking)
        {
			 $date=date('Y-m-d');
			 $date1=date('Y-m-d',strtotime("-1 days"));
			if($booking->bookdate==$date)
			{
				$booking->created=$booking->created_at;	
				$booking->bookdate="Today";
				$booking->created_at=date('d F|h:i a',strtotime($booking->created_at));
				
			}
			
			else if($booking->bookdate==$date1)
			{
				$booking->created=$booking->created_at;	
				$booking->bookdate="Yesterday";
				$booking->created_at=date('d F|h:i a',strtotime($booking->created_at));
				
			}
			else
			{
				$booking->created=$booking->created_at;	
			$booking->bookdate=date('d F Y',strtotime($booking->created_at));
				$booking->created_at=date('d F|h:i a',strtotime($booking->created_at));
							
			}
			
			 $pr[] = $booking; 
		}
				
	   if($pr!=NULL){
			 $message = array('status'=>'1', 'message'=>'Transaction History','data'=>$pr);
	        	return $message;
		 }
		 else 
		 {
			 
			$message = array('status'=>'1', 'message'=>'No Transaction History');
	        	return $message; 
			 
		 }
	  
  }
  
  public function vendortimeslot(Request $request)
  {
	  
	 $orders = 1;
    $duration  = 30;
	$selected_date  = $request->selected_date;
	if($selected_date=='')
	{
    $selected_date  = date('Y-m-d');
	}
	else
	{
	 $selected_date  = $request->selected_date;	
	}
	$date = date('Y-m-d');
	 $current_time = Carbon::Now();
$vendor_id=$request->vendor_id;
$service=$request->service_id;
 $vendorservices=DB::table('vendor_services')->select('Staff_ID')->where('vendor_id',$vendor_id)->where('ServicesID',$service)->first();

$staff_id = $vendorservices->Staff_ID;
     $day = date('l', strtotime($selected_date));
       $time_slot = DB::table('employee_time_slot')
               ->where('vendor_id',$vendor_id)->where('staff_id',$staff_id)
               ->where('days',$day)->where('status',1)
               ->first();
              
if($time_slot=='')
{
 $message = array('status'=>'1', 'message'=>'No Time Slot Found');
          return $message;	
}
    $starttime  = $time_slot->open_hour;
     $endtime  = $time_slot->close_hour;

    $array_of_time = array ();
    $array_of_time1 = array ();
    $min = 30;
    $currenttime = strtotime ("+".$min." minutes", strtotime($current_time));
    $start_time    = strtotime ($starttime); //change to strtotime
    $end_time      = strtotime ($endtime); //change to strtotime

    $add_mins  = $duration*60;
	
	
if(strtotime($date)==strtotime($selected_date)){
    while ($start_time <= $currenttime) // loop between time
    {
       $array_of_time[] = date ("h:i A", $start_time);
       $start_time += $add_mins; // to check endtie=me
    }

    $new_array_of_time = array ();
    for($i = 0; $i < count($array_of_time) - 1; $i++)
    {
        $new_array_of_time[] = '' . $array_of_time[$i] . ' - ' . $array_of_time[$i + 1];
    }

$items=last($new_array_of_time);
$numbers = explode('-', $items);
$last_Number = end($numbers);
 $lastNumber    = strtotime ($last_Number);
 if($last_Number!= NULL){
while ($lastNumber <= $end_time) // loop between time
    {
       $array_of_time1[] = date ("h:i A", $lastNumber);
       $lastNumber += $add_mins; // to check endtie=me
    }

    $new_array_of_time1 = array ();
        for($i = 1; $i < count($array_of_time1) - 1; $i++)
    {
          $totorders = DB::table('orders')->select('service_time')
               ->where('service_date',$selected_date)->get();
			 
               //->where('service_time',$array_of_time1[$i] . ' - ' . $array_of_time1[$i + 1])
               //->count();
			   $arraytimes=array();
			 foreach($totorders as $order)
			 {
				$times1=explode("-",$order->service_time);
$slotnew=$this->getTimeSlot(30,rtrim($times1[0]," "),$times1[1]);
				foreach($slotnew as $slot)
				{
				$arraytimes[]=$slot;
				}
			 }
			
			   if(in_array($array_of_time1[$i],$arraytimes))
			   {
				 
			
			   


        

            $new_array_of_time1[] =array('timeslot'=>'' . $array_of_time1[$i], 'availibility'=>'false');

        }
        else{

             $new_array_of_time1[] =array('timeslot'=>'' . $array_of_time1[$i], 'availibility'=>'true');
        }
    }
 }
 else{
     while ($start_time <= $end_time) // loop between time
    {
       $array_of_time1[] = date ("h:i A", $start_time);
       $start_time += $add_mins; // to check endtie=me
    }

    $new_array_of_time1 = array ();
       for($i = 1; $i < count($array_of_time1) - 1; $i++)
    {
          $totorders = DB::table('orders')->select('service_time')
               ->where('service_date',$selected_date)->get();
			 
               //->where('service_time',$array_of_time1[$i] . ' - ' . $array_of_time1[$i + 1])
               //->count();
			   $arraytimes=array();
			 foreach($totorders as $order)
			 {
				$times1=explode("-",$order->service_time);
$slotnew=$this->getTimeSlot(30,rtrim($times1[0]," "),$times1[1]);
				foreach($slotnew as $slot)
				{
				$arraytimes[]=$slot;
				}
			 }
			
			   if(in_array($array_of_time1[$i],$arraytimes))
			   {
				 
			
			   


        

            $new_array_of_time1[] =array('timeslot'=>'' . $array_of_time1[$i], 'availibility'=>'false');

        }
        else{

             $new_array_of_time1[] =array('timeslot'=>'' . $array_of_time1[$i], 'availibility'=>'true');
        }
    }
 }
 
  if($new_array_of_time1){
	  $data=array('selected_date'=>$selected_date,
            'time_slot'=>$new_array_of_time1);
            $message = array('status'=>'1', 'message'=>'Time Slot','data'=>$data );
          return $message;
        }
        else{
            $message = array('status'=>'1', 'message'=>'No Time Slot Found');
          return $message;
        }

}

else {

    while ($start_time >= $end_time) // loop between time
    {
       $array_of_time[] = date ("h:i A", $start_time);
       $start_time += $add_mins; // to check endtie=me
    }

    $new_array_of_time = array ();
    for($i = 0; $i < count($array_of_time) - 1; $i++)
    {
        $new_array_of_time[] = '' . $array_of_time[$i] . ' - ' . $array_of_time[$i + 1];
    }

$items=last($new_array_of_time);
$numbers = explode('-', $items);
$last_Number = end($numbers);
 $lastNumber    = strtotime ($last_Number);
 if($last_Number!= NULL){
while ($lastNumber <= $end_time) // loop between time
    {
       $array_of_time1[] = date ("h:i A", $lastNumber);
       $lastNumber += $add_mins; // to check endtie=me
    }

    $new_array_of_time1 = array ();
       for($i = 1; $i < count($array_of_time1) - 1; $i++)
    {
          $totorders = DB::table('orders')->select('service_time')
               ->where('service_date',$selected_date)->get();
			 
               //->where('service_time',$array_of_time1[$i] . ' - ' . $array_of_time1[$i + 1])
               //->count();
			   $arraytimes=array();
			 foreach($totorders as $order)
			 {
				$times1=explode("-",$order->service_time);
$slotnew=$this->getTimeSlot(30,rtrim($times1[0]," "),$times1[1]);
				foreach($slotnew as $slot)
				{
				$arraytimes[]=$slot;
				}
			 }
			
			   if(in_array($array_of_time1[$i],$arraytimes))
			   {
				 
			
			   


        

            $new_array_of_time1[] =array('timeslot'=>'' . $array_of_time1[$i], 'availibility'=>'false');

        }
        else{

             $new_array_of_time1[] =array('timeslot'=>'' . $array_of_time1[$i], 'availibility'=>'true');
        }
    }
 }
 else{
     while ($start_time <= $end_time) // loop between time
    {
       $array_of_time1[] = date ("h:i A", $start_time);
       $start_time += $add_mins; // to check endtie=me
    }

    $new_array_of_time1 = array ();
    for($i = 1; $i < count($array_of_time1) - 1; $i++)
    {
          $totorders = DB::table('orders')->select('service_time')
               ->where('service_date',$selected_date)->get();
			 
               //->where('service_time',$array_of_time1[$i] . ' - ' . $array_of_time1[$i + 1])
               //->count();
			   $arraytimes=array();
			 foreach($totorders as $order)
			 {
				$times1=explode("-",$order->service_time);
$slotnew=$this->getTimeSlot(30,rtrim($times1[0]," "),$times1[1]);
				foreach($slotnew as $slot)
				{
				$arraytimes[]=$slot;
				}
			 }
			
			   if(in_array($array_of_time1[$i],$arraytimes))
			   {
				 
			
			   


        

            $new_array_of_time1[] =array('timeslot'=>'' . $array_of_time1[$i], 'availibility'=>'false');

        }
        else{

             $new_array_of_time1[] =array('timeslot'=>'' . $array_of_time1[$i], 'availibility'=>'true');
        }
    }
	
 }
 
  if($new_array_of_time1){
	  $data=array('selected_date'=>$selected_date,
            'time_slot'=>$new_array_of_time1);
            $message = array('status'=>'1', 'message'=>'Time Slot', 'data'=>$data);
          return $message;
        }
        else{
            $message = array('status'=>'1', 'message'=>'No Time Slot Found');
          return $message;
        }



}	
	  
	  
	  
  }
  
  
  public function bookingdetail(Request $request)
{
	$user_id=$request->user_id;
	$bookid=$request->bookid;
	$ongoing=DB::table('orders')->select('*','vendor.block','vendor.street','vendor.avenue','vendor.zipcode','orders.id as idbook')
            ->join('vendor','orders.vendor_id','=','vendor.vendor_id')
            ->join('users','orders.user_id','=','users.id')
           ->join('areas','vendor.area','=','areas.Area_id')
              ->where('orders.user_id',$user_id)
              ->where('orders.id',$bookid)
              
              ->orderBy('orders.id', 'DESC')
               ->get();
			    if(count($ongoing)>0){
      foreach($ongoing as $ongoings){
		  $order = DB::table('order_cart')->join('staff_profile','staff_profile.staff_id','=','order_cart.staff_id')
            ->select('order_cart_id','service_name','service_id','book_ID','user_id','order_cart.vendor_id','status','price','staff_name')
            ->where('book_ID',$ongoings->idbook)
            ->get();
            $text=DB::table('bookingmodifytext')->first();
			$date=date('Y-m-d',strtotime($ongoings->service_date));
			$address=$ongoings->Area_Title.",".$ongoings->block.",".$ongoings->street.",".$ongoings->avenue.",".$ongoings->zipcode;
			   $data[]=array('user_name'=>$ongoings->name,'vendor_name'=>$ongoings->vendor_name, 'vendor_phone'=>$ongoings->vendor_phone, 'vendor_email'=>$ongoings->vendor_email, 'vendor_logo'=>$ongoings->vendor_logo, 'service_date'=>$date, 'service_time'=>$ongoings->service_time,'payment_method'=>$ongoings->payment_method,'payment_status'=>$ongoings->payment_status, 'price'=>$ongoings->total_price,'status'=>$ongoings->status,'vendor_id'=>$ongoings->vendor_id,'address'=>$address,'order_details'=>$order,'booking_text'=>$text->ModifyText);
			   
	  }
				}
			    if(count($ongoing)>0){
					$message = array('status'=>'1', 'message'=>'Booking Detail', "data"=>$data);
          return $message;
				}
				
       
        else{
            $message = array('status'=>'1', 'message'=>'No Bookings Found');
          return $message;
        }
	
}
  
  
   public function RemindMeEnableDisable(Request $request)
    {
        $user_id=$request->user_id;
		$bookid=$request->bookid;
		$enabledisable=$request->enabledisable;
	
		$update=DB::table('orders')->where('id',$bookid)->where('user_id',$user_id)
                                ->update
    				(['remindme'=>$enabledisable]);
					if($update){
					$message = array('status'=>'1', 'message'=>'Updated Successfully');
          return $message;
				}
				
       
        else{
            $message = array('status'=>'1', 'message'=>'Nothing to Update');
          return $message;
        }
					
	}

	public function autocompletion_cron(Request $request)
	{
		$ongoing=DB::table('orders')
            ->join('vendor','orders.vendor_id','=','vendor.vendor_id')
            
           ->join('areas','vendor.area','=','areas.Area_id')
		   ->select('cancel','cancelduration','total_price','status','remindme','orders.id','service_date','service_time','payment_method','payment_status','vendor_name','owner','orders.vendor_id','vendor_email','vendor_phone','vendor_logo',DB::raw("concat(Area_Title, ',', vendor.block,',',vendor.street,',',vendor.avenue,',',vendor.zipcode) as address"),'vendor.lat','vendor.lng','opening_time','closing_time')
               
              
             
              ->whereIn('status',[1,6])->whereDate('service_date','>=',date('Y-m-d'))
              ->orderBy('orders.id', 'DESC')
               ->get();
             
               
			    if(count($ongoing)>0){
      foreach($ongoing as $ongoings){
		  $cancel=$ongoings->cancel;
		  $duration=$ongoings->cancelduration;
		  $date1 = date('Y-m-d H:i', strtotime($ongoings->service_date));
          $date2 = date('Y-m-d H:i');
          
		  if ($date1 == $date2) {
			DB::table('orders')->where('id',$ongoings->id)
			->update(['status'=>2]);
		  }
		}
	}
		
		
		
		
	}
	public function reminder_cron(Request $request)
	{
		$users=DB::table('users')->select('reminderenable','hours','id','name','device_id')->where('reminderenable',1)->get();
          
			foreach($users as $user)
			{
					$ongoing=DB::table('orders')
            ->join('vendor','orders.vendor_id','=','vendor.vendor_id')
            
           ->join('areas','vendor.area','=','areas.Area_id')
		   ->select('cancel','cancelduration','total_price','status','remindme','orders.id','service_date','service_time','payment_method','payment_status','vendor_name','owner','orders.vendor_id','vendor_email','vendor_phone','vendor_logo',DB::raw("concat(Area_Title, ',', vendor.block,',',vendor.street,',',vendor.avenue,',',vendor.zipcode) as address"),'vendor.lat','vendor.lng','opening_time','closing_time')
               
              
             ->where('user_id',$user->id)
              ->whereIn('status',[1,6])->whereDate('service_date','>=',date('Y-m-d'))
              ->orderBy('orders.id', 'DESC')
               ->get();
                //Log::info($ongoing);
				foreach($ongoing as $ongoings){
					$hour=$user->hours;
					if($hour==1)
					{
						 $date1 = date('Y-m-d H:i', strtotime($ongoings->service_date. '- '.$hour.' hour'));
					}
					else
					{
					$date1 = date('Y-m-d H:i', strtotime($ongoings->service_date. '- '.$hour.' hours'));	
					}
					//Log::info($date1);
						$date2 = date('Y-m-d H:i');
							
						if ($date1 == $date2) {
							$jsonFilePath = storage_path("firebase/bookme-f44a4-c16fbc6aa3e0.json");
 // Replace with your JSON key file path
               $getFcmKey = $this->getAccessToken($jsonFilePath);
                
                
                
                
                $fcmUrl = 'https://fcm.googleapis.com/v1/projects/bookme-f44a4/messages:send';
               
        $date = date('d-m-Y');
         //$user = $request->user;
        //$countuser = count($user);
        $date = date('d-m-Y');
      
            $url_aws=url('/');
           
       
            $notify_image = "N/A";
      
         

        $notification_title = "Reminder";
        
        
        $date = date('d-m-Y');
          
        $created_at = Carbon::now();
		
		  $get_device_id=array();
        
        $get_device_id[] = $user->device_id;
       
        //$loop =  count(array_chunk($get_device_id,600));  // count array chunk 1000
        //$arrayChunk = array_chunk($get_device_id,600);   // devide array in 1000 chunk
        //$device_id = array();
        
   $token='';
        //for($i=0; $i<$loop ;$i++)
        //{
            //foreach($arrayChunk[$i] as $all_device_id)
            //{       
                   
                        $device_id[] =  $user->device_id;
						if($user->device_id!='')
						{
							$day=date('l', strtotime($date1));
						 $url = 'https://fcm.googleapis.com/v1/projects/bookme-f44a4/messages:send';
            $body="Hi ".$user->name." your appointment at ".$ongoings->vendor_name." is today at ".date('h:i A', strtotime($ongoings->service_date))."";
            $customData=$url;
			
			
			 $data = [
    "message" => [
        "token" => $user->device_id, // Device FCM token
        "notification" => [
            "title" => $notification_title,
            "body" => $body,
            "image" => $notify_image, // Add the image URL here
        ],
        "data" => [  // Custom data that will be available in the app (this is where extra params are sent)
            "order_id" => $ongoings->id,  // Your custom extra parameter
            "type" => "order",  // Another extra parameter
         
        ],
        
        "apns" => [
            "headers" => [
                "apns-collapse-id" => "solo_changed_administrator",
                "content-available" => "1",
                "apns-priority" => "10",
            ],
            "payload" => [
                "aps" => [
                    "sound" => "default", // Default sound for iOS
                    "badge" => 0, // Set a specific badge number
                ]
            ]
        ],
        
    ]
];	
			
			
            
       
        //api_key in Firebase Console -> Project Settings -> CLOUD MESSAGING -> Server key
        $server_key = $getFcmKey;
        //header with content_type api key
        $headers = [
            "Authorization: Bearer $server_key",
            'Content-Type: application/json',
        ];
        // CURL request to route notification to FCM connection server (provided by Google)
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            $result = curl_exec($ch);
            if ($result === FALSE) {
                die('Oops! FCM Send Error: ' . curl_error($ch));
            }
			
            curl_close($ch);	
							
				$dd = DB::table('user_notification')
                    ->insert(['user_id'=>$user->id,
                     'noti_title'=>$notification_title,
                     'noti_message'=>$body,
                     'image'=>$notify_image,
                     'created_at'=>Carbon::now()]);		
               
            			}
			}    
		
			}
		}
						
		
		
		
	}
	
	
	
	 public function couponCodeStore($user_id,$vendorId,$coupon_code)
{
          

        // Validate the request with custom messages
      

       

        
        $events = DB::table('user_cart')
            ->select('Cart_ID', 'price')
            ->where('user_id', $user_id)
            ->where('user_cart.vendor_id', $vendorId)
            ->orderBy('Cart_ID', 'asc')
            ->get();

        $totalPrice = $events->sum(function($event) {
            return (float) $event->price;
        });

        if ($events->isNotEmpty()) {

           
            $couponCodeOffer = DB::table('coupon')
                ->where('coupon_code', $coupon_code)
                ->where(function ($query) {
                    $query->where(function ($query) {
                        $query->where('type', 'Fixed Amount')
                            ->where('applicable', 'All Users');
                    })
                    ->orWhere(function ($query) {
                        $query->where('type', 'Fixed Amount')
                            ->where('applicable', 'Specific Users');
                    })
                    ->orWhere(function ($query) {
                        $query->where('type', 'A percent amount discount')
                            ->where('applicable', 'All Users');
                    })
                    ->orWhere(function ($query) {
                        $query->where('type', 'A percent amount discount')
                            ->where('applicable', 'Specific Users');
                    });
                })
                ->where(function ($query) use ($vendorId) {
                    $query->where('available_for_vendors', 0)
                        ->orWhere(function ($query) use ($vendorId) {
                            $query->where('available_for_vendors', 1)
                                ->where('available_vendors', $vendorId);
                        });
                })
                ->select('type', 'amount')
                ->first();

            if ($couponCodeOffer) {
                $discountAmount = 0;

               
                if ($couponCodeOffer->type === 'Fixed Amount') {
                    
                    if ($couponCodeOffer->amount > $totalPrice) {
                        return response()->json([
                            'status' => '0',
                            'message' => 'The discount amount cannot be greater than the total price.',
                            'data' => []
                        ], 422);
                    }
                    
                    $discountAmount = $couponCodeOffer->amount;
                } elseif ($couponCodeOffer->type === 'A percent amount discount') {
                    $discountAmount = $totalPrice * ($couponCodeOffer->amount / 100);
                }

               
                $afterDiscount = max(0, $totalPrice - $discountAmount);

               
                return $discountAmount;
               
            } 
        }
      
   }  

	//Payment Methods Function
	public function getAPIUrlForCreateToken()
        {
           
               $url = $this->paymenturl."create-customer-unique-token";
         
            return $url;
        }
		
		public function getCustomerUniqueToken($phone)
        {
            $token = "";
            $phone = trim($phone);
            if (!empty($phone))
            {
                $token = $phone;
                $params = json_encode(["customerUniqueToken" => $token, ]);
                $curl = curl_init();
                curl_setopt_array($curl, [CURLOPT_URL => $this->getAPIUrlForCreateToken() , CURLOPT_RETURNTRANSFER => true, CURLOPT_USERAGENT => $this->getUserAgent(), CURLOPT_ENCODING => "", CURLOPT_MAXREDIRS => 10, CURLOPT_TIMEOUT => 0, CURLOPT_FOLLOWLOCATION => true, CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1, CURLOPT_CUSTOMREQUEST => "POST", CURLOPT_POSTFIELDS => $params, CURLOPT_HTTPHEADER => ["Accept: application/json", "Content-Type: application/json", "Authorization: Bearer ".$this->api_key."", ], ]);

                $response = curl_exec($curl);
                if ($response)
                {
                    $result = json_decode($response, true);
                    if (@$result["errors"])
                    {
                        $cards = ["error" => 1, "msg" => $result["message"]];
                    }
                    elseif (@$result["status"] == true)
                    {
                        $token = $token;
                    }
                    else
                    {
                        $cards = ["error" => 1, "msg" => $result["message"]];
                    }
                }
            }
            return $token;
        }
		
		
		public function getUserAgent(){
            $userAgent = 'UpaymentsLaravel';
            
            return $userAgent;
        }
		
		public function AddCard(Request $request)
		{
				$user_id=$request->user_id;
				$user=DB::table('users')->select('user_phone','email')
                   ->where('id',$user_id)->first();
				@$user_phone=@$user->user_phone;
				$email=$user->email;
				$flag=0;
				$phone="";
				if(is_numeric($user_phone))
				{
				$flag=1;
				$phone=$user_phone;
				}
				if(is_numeric($email))
				{
				$flag=1;
				$phone=$email;
				}
				
					if($flag==0)
					{
							$message = array('status'=>'0', 'message'=>'Please Add Phone Number to Your Profile');
							return $message;
							exit();
					}
					else
					{
						$customeruniquetoken=$this->getCustomerUniqueToken($phone);
						$curl = curl_init();

						  curl_setopt_array($curl, array(
						  CURLOPT_URL => $this->paymenturl.'add-card',
						  CURLOPT_RETURNTRANSFER => true,
						  CURLOPT_ENCODING => '',
						  CURLOPT_MAXREDIRS => 10,
						  CURLOPT_TIMEOUT => 0,
						  CURLOPT_FOLLOWLOCATION => true,
						  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
						  CURLOPT_CUSTOMREQUEST => 'POST',
						  CURLOPT_POSTFIELDS =>'{
							"returnUrl":"'.route('AddCardResultPage').'",
							"customerUniqueToken": '.$customeruniquetoken.'
						}',
						  CURLOPT_HTTPHEADER => array(
							'Accept: application/json',
							'Content-Type: application/json',
							'Authorization: Bearer '.$this->api_key.''
						  ),
						));

						$response = curl_exec($curl);

						curl_close($curl);
						$res=json_decode($response);
					//	dd($res);
						if($res->status!=false)
						{
						 	$message = array('status'=>'1', 'message'=>'Redirecting','data'=>$res->data->link);   
						}
						else
						{
						$message = array('status'=>'1', 'message'=>$res->message);
						}
						return $message;
						
					}
			
		}
		
		//Add Card Result Page
		public function AddCardResultPage(Request $request)
		{
			
		$message = array('status'=>'1', 'message'=>'Card Added Successfully');
        return $message;	
			
			
		}
		
		//listAllCards
		public function ListAllCards(Request $request)
		{
			$user_id=$request->user_id;
				$user=DB::table('users')->select('user_phone','email')
                   ->where('id',$user_id)->first();
				@$user_phone=@$user->user_phone;
				$email=$user->email;
				$flag=0;
				$phone="";
				if(is_numeric($user_phone))
				{
				$flag=1;
				$phone=$user_phone;
				}
				if(is_numeric($email))
				{
				$flag=1;
				$phone=$email;
				}
				
					if($flag==0)
					{
							$message = array('status'=>'1', 'message'=>'Please Add Phone Number to Your Profile');
							return $message;
							exit();
					}
					else
					{
					$customeruniquetoken=$this->getCustomerUniqueToken($phone);
					$curl = curl_init();

				  curl_setopt_array($curl, array(
				  CURLOPT_URL => $this->paymenturl.'retrieve-customer-cards',
				  CURLOPT_RETURNTRANSFER => true,
				  CURLOPT_ENCODING => '',
				  CURLOPT_MAXREDIRS => 10,
				  CURLOPT_TIMEOUT => 0,
				  CURLOPT_FOLLOWLOCATION => true,
				  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				  CURLOPT_CUSTOMREQUEST => 'POST',
				  CURLOPT_POSTFIELDS =>'{
				   
					"customerUniqueToken": '.$customeruniquetoken.'
				}',
				  CURLOPT_HTTPHEADER => array(
					'Accept: application/json',
					'Content-Type: application/json',
					'Authorization: Bearer '.$this->api_key.''
				  ),
				));

				$response = curl_exec($curl);

				curl_close($curl);
				$res=json_decode($response);
			if(gettype($res->data)=="object")
				{
					
				$message = array('status'=>'1', 'message'=>'Card Retrieved successfully','data'=>$res->data);
				return $message;
				exit();
				}
				{
				$message = array('status'=>'1', 'message'=>'There is no Cards Saved');
				return $message;
				exit();	
				}
						
					}
			
		}
		//paymentmethod
		public function upayments_method($total,$order_id,$user_id,$vendor_id)
		{
				$orderid=$order_id."".time();
				$user=DB::table('users')->select('user_phone','email','name')
                   ->where('id',$user_id)->first();
				$vendor=DB::table('vendor')->select('iban','comission')
                   ->where('vendor_id',$vendor_id)->first();
				   
				@$user_phone=@$user->user_phone;
				$email=$user->email;
				$name=$user->name;
				$flag=0;
				$phone="";
				if(is_numeric($user_phone))
				{
				$flag=1;
				$phone=$user_phone;
				}
				if(is_numeric($email))
				{
				$flag=1;
				$phone=$email;
				}
				$uniquetoken='';
				$customeruniquetoken='';
					if($flag==0)
					{
							//$message = array('status'=>'1', 'message'=>'Please Add Phone Number to Your Profile');
							//return $message;
							//exit();
					}
					else
					{
					$customeruniquetoken=$this->getCustomerUniqueToken($phone);
					
					
					}
			
					if($customeruniquetoken!='')
					{
				$curl = curl_init();

				  curl_setopt_array($curl, array(
				  CURLOPT_URL => "https://sandboxapi.upayments.com/api/v1/charge",
				  CURLOPT_RETURNTRANSFER => true,
				  CURLOPT_ENCODING => '',
				  CURLOPT_MAXREDIRS => 10,
				  CURLOPT_TIMEOUT => 0,
				  CURLOPT_FOLLOWLOCATION => true,
				  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				  CURLOPT_CUSTOMREQUEST => 'POST',
				  CURLOPT_POSTFIELDS =>'{
					
					"order": {
						"id": "'.$orderid.'",
						"reference": "'.$order_id.'",
						"description": "Booking",
						"currency": "KWD",
						"amount": '.number_format($total,2).'
					},
					
					"language": "en",
					"reference": {
						"id": "'.$order_id.'"
					},
					"tokens": {
						"customerUniqueToken": '.$customeruniquetoken.'
						
					},
					"customer": {
						"uniqueId": "'.$user_id.'",
						"name": "'.$name.'",
						"email": "",
						"mobile": ""
					},
					
					"extraMerchantData": [
					{
						"amount": '.number_format($total,2).',
						"knetCharge": '.$vendor->comission.',
						"knetChargeType": "percentage",
						"ccCharge": '.$vendor->comission.',
						"ccChargeType": "percentage",
						"ibanNumber": "'.$vendor->iban.'"
					}
				],
					"returnUrl": "'.route('resultpage').'",
					"cancelUrl": "'.route('resultpage').'",
					"notificationUrl": "'.route('notificationpage').'",
					"customerExtraData": "'.$order_id.'"
				}',
				  CURLOPT_HTTPHEADER => array(
					'Accept: application/json',
					'Content-Type: application/json',
					'Authorization: Bearer '.$this->api_key.''
				  ),
				));
					}
					else 
					{
					$curl = curl_init();

				  curl_setopt_array($curl, array(
				  CURLOPT_URL => "https://sandboxapi.upayments.com/api/v1/charge",
				  CURLOPT_RETURNTRANSFER => true,
				  CURLOPT_ENCODING => '',
				  CURLOPT_MAXREDIRS => 10,
				  CURLOPT_TIMEOUT => 0,
				  CURLOPT_FOLLOWLOCATION => true,
				  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				  CURLOPT_CUSTOMREQUEST => 'POST',
				  CURLOPT_POSTFIELDS =>'{
					
					"order": {
						"id": "'.$orderid.'",
						"reference": "'.$order_id.'",
						"description": "Booking",
						"currency": "KWD",
						"amount": '.number_format($total,2).'
					},
					
					"language": "en",
					"reference": {
						"id": "'.$order_id.'"
					},
					
					"customer": {
						"uniqueId": "'.$user_id.'",
						"name": "'.$name.'",
						"email": "",
						"mobile": ""
					},
					
					"extraMerchantData": [
					{
						"amount": '.number_format($total,2).',
						"knetCharge": '.$vendor->comission.',
						"knetChargeType": "percentage",
						"ccCharge": '.$vendor->comission.',
						"ccChargeType": "percentage",
						"ibanNumber": "'.$vendor->iban.'"
					}
				],
					"returnUrl": "'.route('resultpage').'",
					"cancelUrl": "'.route('resultpage').'",
					"notificationUrl": "'.route('notificationpage').'",
					"customerExtraData": "'.$order_id.'"
				}',
				  CURLOPT_HTTPHEADER => array(
					'Accept: application/json',
					'Content-Type: application/json',
					'Authorization: Bearer '.$this->api_key.''
				  ),
				));	
						
					}
					
				$response = curl_exec($curl);
		Log::info($response);
if (curl_errno($curl)) {
    echo $error_msg = curl_error($curl);
   
}
				curl_close($curl);
				$res=json_decode($response);
			
				return $res->data->link;
				
				exit();

			
			
			
			
		}
		
		//Results Page
		public function ResultsPage(Request $request)
		{
				$track_id=$request->track_id;

				$curl = curl_init();

				  curl_setopt_array($curl, array(
				  CURLOPT_URL => $this->paymenturl.'get-payment-status/'.$track_id.'',
				  CURLOPT_RETURNTRANSFER => true,
				  CURLOPT_ENCODING => '',
				  CURLOPT_MAXREDIRS => 10,
				  CURLOPT_TIMEOUT => 0,
				  CURLOPT_FOLLOWLOCATION => true,
				  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				  CURLOPT_CUSTOMREQUEST => 'GET',
				  CURLOPT_HTTPHEADER => array(
				  'Content-Type: application/json',
				   'Authorization: Bearer '.$this->api_key.''
				  ),
				));

		$response = curl_exec($curl);

		curl_close($curl);
		$res=json_decode($response);
		$order_id=$res->data->transaction->reference;
		if($res->data->transaction->result=="CAPTURED")
		{
				$orders = DB::table('orders')->where('id',$order_id)
    				->update(['status'=>1,'payment_method'=>$res->data->transaction->payment_type,'payment_id'=>$res->data->transaction->payment_id,'payment_status'=>'Paid','track_id'=>$res->data->transaction->track_id,'payment_date'=>$res->data->transaction->payment_date,'payment_total_price'=> $res->data->transaction->total_price,'reference'=>$res->data->transaction->reference]);
					$orders1 = DB::table('order_cart')->where('book_ID',$order_id)
    				->update(['status'=>1]);
					 $settings = DB::table('smtp_settings')->where('SMTP_ID', 1)->first();
					Config::set('mail', [
                'driver' => $settings->type,
                'host' => $settings->mail_host,
                'port' => $settings->mail_port,
                'from' => [
                    'address' => $settings->mail_from_address,
                    'name' => $settings->mail_from_name
                ],
                'encryption' => $settings->mail_encryption,
                'username' => $settings->mail_username,
                'password' => $settings->mail_password,
            ]);
			$subject="BookMe - Booking Confirmation";
			$messageBody='';
			$orders=DB::table('order_cart')->select('*')
                   ->where('book_ID',$order_id)->get();
			$order=DB::table('orders')->select('*')
                   ->where('id',$order_id)->first();
				   $user=DB::table('users')->select('name','email','device_id','id')
                   ->where('id',$order->user_id)->first();
				   
				   $vendor=DB::table('vendor')->select('vendor_name','vendor_name_ar','paymentcancelpolicy')
                   ->where('vendor_id',$order->vendor_id)->first();
				   if (App::getLocale() == 'ar') {
					$messageBody.='<table width="100%" align="center" cellpadding="0" cellspacing="0" border="0" bgcolor="#ededed" style="table-layout:fixed;margin:0 auto;mso-table-lspace:0pt;mso-table-rspace:0pt; margin-bottom:0px; padding-top: 0px; border-top:4px solid #008F96; ">
     
     
		<tbody>
          
            
            <tr>
            
			<td align="center">  
              
                
				<table width="700" bgcolor="#2a9444" align="center" cellpadding="0" cellspacing="0" border="0" class="table600" style="mso-table-lspace:0pt;mso-table-rspace:0pt;">
                    
                    
                    
					<tbody><tr>
						<td width="618" class="tdLogo" bgcolor="#2a9444" align="left"> 
                          <div style="background: #008F96;
    width: 700px;
    height: 25px;
    font-family: Arial, Helvetica, sans-serif;
    padding: 10px 15px; text-align: center;"> 
                             <p style=" text-align: center; font-weight:700; font-size:26px; padding: 0px 0px 0px; margin: 0px 0px; color: #fff;":>   تأكيد الحجز  </p> </p>
                              
                           
                            </div>    
                            
							<table width="730" align="left" cellpadding="0" cellspacing="0" border="0" bgcolor="#fff" class="table600Logo" style="mso-table-lspace:0pt;mso-table-rspace:0pt;">
								<tbody>
                                <tr>
								
									<td style="text-align: center; padding: 1px 0px;">
                                      
										
											<a href="#" target="_blank" style="text-align: center;">
                                                <img src="https://appbookme.com/images/logo.png" style="text-align: center;" alt="Logo Image" border="0" align="top" hspace="0" vspace="0" width="130" height="100"></a>

                                                <p style=" text-align: center; font-weight:700; font-size:20px; padding: 10px 0px 0px;"> حجزك تم بنجاح </p> 
                                                
                                              
                                               
												
                                                                
									</td>
								</tr>

                                <tr>
									
									<td style="text-align: right; padding: 5px 20px 30px 20px;">
									
                                      <p class="RegularTextTD" style="font-size: 18px;">'.$user->name.'</p>          
                                              
                                             <p class="RegularTextTD" style="line-height: 24px; font-size: 16px;">  شكراً لحجزك مع <strong> '.$vendor->vendor_name_ar.'</strong>. إذا كنت غير قادر على الحضور لأي سبب، يرجى إلغاء أو تعديل موعدك عبر تطبيق BookMe. شكراً لك!  </p>
												
                                                                
									</td>
								</tr>

                               
							</tbody></table>
							
						</td>
					</tr> 
				</tbody></table>         
            	</td>
		</tr>
	</tbody></table>       
        
        
        
        
      
      
      
      
      
      <!-- INVOICE SECTION -->
<table width="100%" bgcolor="#ededed" align="center" cellpadding="0" cellspacing="0" border="0" style="table-layout: fixed; margin: 0 auto; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> 
<tbody> 

 <tr> 


<td align="center"> 



<table width="730" align="center" cellpadding="0" cellspacing="0" border="0" class="table600" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> 
 <tbody> 
 <tr> 
    <td>


 <table width="730" align="left" cellpadding="0" cellspacing="0" border="0" class="table600" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 0px 10px; background: #fff;"> 
<tbody> 
<tr> 
<td> 
 
    
   <table border="#191818" cellspacing="0" cellpadding="0" class="item-table" style="background: #fff!important; width: 100%;
    border-spacing: 0;
    border-collapse:separate;
    margin-bottom: 0px;
    font-family: Arial, Helvetica, sans-serif; border: 1px solid #013133;  border-radius: 10px 10px 10px 10px;  -moz-border-radius: 10px 10px 10px 10px;
    -webkit-border-radius: 10px 10px 10px 10px; overflow: hidden;">

<thead style=" border-radius: 10px 10px 10px 10px;  -moz-border-radius: 10px 10px 10px 10px;
    -webkit-border-radius: 10px 10px 10px 10px; border-spacing: 0;  border-collapse:collapse;">
<tr style="">

    <th class="text-left" style="padding: 15px 10px;
    background: #fdfdfd;
   
    line-height: 22px; color: #191818;
    font-size: 1em; text-align: center;"> التاريخ والوقت </th>

<th class="text-left" style="padding: 15px 10px;
    background: #fdfdfd;
   
    line-height: 22px; color: #191818;
    font-size: 1em; text-align: center;"> الخدمات </th>

<th class="text-left" style="padding: 15px 10px;
background: #fdfdfd;

line-height: 22px; color: #191818;
font-size: 1em; text-align: center;">الموظف</th>


<th class="text-right total" style="padding: 15px 10px;
background: #fdfdfd;

line-height: 22px; color: #191818;
font-size: 1em; text-align: center;">المدة</th>
   
<th class="text-right total" style="padding: 15px 10px;
    background: #fdfdfd;
   
    line-height: 22px; color: #191818;
    font-size: 1em;text-align: center;">السعر</th>


</tr>
</thead>


<tbody>';
foreach($orders as $ordersingle)
{
$staff=DB::table('staff_profile')->select('staff_name','staff_name_ar')
                   ->where('staff_id',$ordersingle->staff_id)->first();
$messageBody.='

    <td class="text-center" style="padding: 15px 10px;
    background: #fdfdfd;
  
    font-size: 14px;
    line-height: 22px;text-align: center; direction: ltr;">
    <p>'.date('d-M-Y',strtotime($ordersingle->start_date)).' / '.date('h:i A',strtotime($ordersingle->start_date)).'</p>
</td>

<td class="text-left" style="padding: 15px 10px;
    background: #fdfdfd;
   
    font-size: 14px;
    line-height: 22px;text-align: center;">
<a target="_blank" href="#" style="color: #191818;  font-size: 14px;">
    '.$ordersingle->service_name.'

</a>

</td>

<td class="total" style="padding: 15px 10px;
    background: #fdfdfd;
 
    font-size: 14px;
    line-height: 22px; text-align: center;">'.$staff->staff_name_ar.'</td>

<td class="total" style="padding: 15px 10px;
    background: #fdfdfd;
  
    font-size: 14px;
    line-height: 22px; text-align: center;">'.$ordersingle->duration.' Min</td>

<td class="total" style="padding: 15px 10px;
    background: #fdfdfd;
   
    font-size: 14px;
    line-height: 22px; text-align: center;">KD '.number_format($ordersingle->price,3).'</td>
  
</tr>';
}
    



$messageBody.='</tbody>
</table>
    
       <table width="100%" class="table518b" align="left" cellpadding="0" cellspacing="0" border="0" style="background: #fff; padding: 20px 20px;">
   <tr>
    
    <td><p class="RegularTextTD" style="font-size: 16px; color: #000; text-align: center;"> إذا كان لديك أي أسئلة أو تحتاج إلى مساعدة، لا تتردد في التواصل مع فريق الدعم لدينا! </p></td>
    </tr> 
           
           
        <tr>
    
   
    </tr>    
       
       </table>
       
    <table width="100%" align="left" cellpadding="0" cellspacing="0" border="0" class="table518b" style="mso-table-lspace:0pt;mso-table-rspace:0pt; background: #fff; padding: 20px; text-align: center; border-top: 1px solid #013133;">
 <tbody>
    <tr>
        <!--SHIPPING ADDRESS AND Payment Method Information-->
        <td height="20" class="RegularTextTD" style="font-size: 16px; text-align: center; padding-bottom: 10px; color: #000; line-height: 24px;"> '.$vendor->paymentcancelpolicy.' </td>
        </tr> 
     
    <tr>
<!--SHIPPING ADDRESS AND Payment Method Information-->
<td height="20" class="RegularTextTD" style="font-size: 16px; text-align: center; padding-bottom: 10px; color: #000; font-weight: normal;"> إذا كان لديك أي استفسارات، يرجى إبلاغنا.  </td>
</tr> 
     
     <tr>
<!--SHIPPING ADDRESS AND Payment Method Information-->
<td height="20" class="RegularTextTD" style="font-size: 18px; text-align: center; padding-bottom: 10px; color: #000;;"><EMAIL></td>
</tr> 
     
    <tr>
   
   </tr>   
</tbody>
       
       
       


       
       </table> 
       
       
       
       
</table> 
    
    
    
    
</td>
</tr>
</tbody> 
</table> 

</td>
</tr>
</tbody> 
</table> ';

					
				   }
				   else 
				   {
			$messageBody.='<table width="100%" align="center" cellpadding="0" cellspacing="0" border="0" bgcolor="#ededed" style="table-layout:fixed;margin:0 auto;mso-table-lspace:0pt;mso-table-rspace:0pt; margin-bottom:0px; padding-top: 0px; border-top:4px solid #008F96; ">
     
     
		<tbody>
          
            
            <tr>
            
			<td align="center">  
              
                
				<table width="700" bgcolor="#2a9444" align="center" cellpadding="0" cellspacing="0" border="0" class="table600" style="mso-table-lspace:0pt;mso-table-rspace:0pt;">
                    
                    
                    
					<tbody><tr>
						<td width="618" class="tdLogo" bgcolor="#2a9444" align="left"> 
                          <div style="background: #008F96;
    width: 700px;
    height: 25px;
    font-family: Arial, Helvetica, sans-serif;
    padding: 10px 15px; text-align: center;"> 
                             <p style=" text-align: center; font-weight:700; font-size:26px; padding: 0px 0px 0px; margin: 0px 0px; color: #fff;":>   Booking Confirmation </p> </p>
                              
                           
                            </div>    
                            
							<table width="730" align="left" cellpadding="0" cellspacing="0" border="0" bgcolor="#fff" class="table600Logo" style="mso-table-lspace:0pt;mso-table-rspace:0pt;">
								<tbody>
                                <tr>
									
									<td style="text-align: center; padding: 1px 0px;">
                                      
										
											<a href="#" target="_blank" style="text-align: center;">
                                                <img src="https://appbookme.com/images/logo.png" style="text-align: center;" alt="Logo Image" border="0" align="top" hspace="0" vspace="0" width="130" height="100"></a>

                                                <p style=" text-align: center; font-weight:700; font-size:20px; padding: 10px 0px 0px;"> Your Booking was successful!</p> 
                                                
                                              
                                               
												
                                                                
									</td>
								</tr>

                                <tr>
									
									<td style="text-align: left; padding: 5px 20px 30px 20px;">
									
                                      <p class="RegularTextTD" style="font-size: 16x;">'.$user->name.'</p>          
                                              
                                             <p class="RegularTextTD" style="line-height: 24px; font-size: 14px;"> Thank you for your booking with <strong> '.$vendor->vendor_name.'</strong>. If for any reason you cannot make your appointment please cancel or modify your appointment via BookMe App. Thanks!</p>
												
                                                                
									</td>
								</tr>

                               
							</tbody></table>
							
						</td>
					</tr> 
				</tbody></table>         
            	</td>
		</tr>
	</tbody></table>       
        
        
        
        
      
      
      
      
      
      <!-- INVOICE SECTION -->
<table width="100%" bgcolor="#ededed" align="center" cellpadding="0" cellspacing="0" border="0" style="table-layout: fixed; margin: 0 auto; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> 
<tbody> 

 <tr> 


<td align="center"> 



<table width="730" align="center" cellpadding="0" cellspacing="0" border="0" class="table600" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> 
 <tbody> 
 <tr> 
    <td>


 <table width="730" align="left" cellpadding="0" cellspacing="0" border="0" class="table600" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 0px 10px; background: #fff;"> 
<tbody> 
<tr> 
<td> 
 
    
   <table border="#191818" cellspacing="0" cellpadding="0" class="item-table" style="background: #fff!important; width: 100%;
    border-spacing: 0;
    border-collapse:separate;
    margin-bottom: 0px;
    font-family: Arial, Helvetica, sans-serif; border: 1px solid #013133;  border-radius: 10px 10px 10px 10px;  -moz-border-radius: 10px 10px 10px 10px;
    -webkit-border-radius: 10px 10px 10px 10px; overflow: hidden;">

<thead style=" border-radius: 10px 10px 10px 10px;  -moz-border-radius: 10px 10px 10px 10px;
    -webkit-border-radius: 10px 10px 10px 10px; border-spacing: 0;  border-collapse:collapse;">
<tr style="">

    <th class="text-left" style="padding: 15px 10px;
    background: #fdfdfd;
   
    line-height: 22px; color: #191818;
    font-size: 1em; text-align: center;">Date & Time </th>

<th class="text-left" style="padding: 15px 10px;
    background: #fdfdfd;
   
    line-height: 22px; color: #191818;
    font-size: 1em; text-align: center;"> Service items </th>

<th class="text-left" style="padding: 15px 10px;
background: #fdfdfd;

line-height: 22px; color: #191818;
font-size: 1em; text-align: center;">Staff</th>


<th class="text-right total" style="padding: 15px 10px;
background: #fdfdfd;

line-height: 22px; color: #191818;
font-size: 1em; text-align: center;">Duration</th>
   
<th class="text-right total" style="padding: 15px 10px;
    background: #fdfdfd;
   
    line-height: 22px; color: #191818;
    font-size: 1em;text-align: center;">Price</th>


</tr>
</thead>


<tbody>';

foreach($orders as $ordersingle)
{
$staff=DB::table('staff_profile')->select('staff_name')
                   ->where('staff_id',$ordersingle->staff_id)->first();
$messageBody.='
<tr>

    <td class="text-center" style="padding: 15px 10px;
    background: #fdfdfd;
  
    font-size: 14px;
    line-height: 22px;text-align: center; direction: ltr;">
    <p> '.date('d-M-Y',strtotime($ordersingle->start_date)).' / '.date('h:i A',strtotime($ordersingle->start_date)).'</p>
</td>

<td class="text-left" style="padding: 15px 10px;
    background: #fdfdfd;
   
    font-size: 14px;
    line-height: 22px;text-align: center;">
<a target="_blank" href="#" style="color: #191818;  font-size: 14px;">
    '.$ordersingle->service_name.'

</a>

</td>

<td class="total" style="padding: 15px 10px;
    background: #fdfdfd;
 
    font-size: 14px;
    line-height: 22px; text-align: center;">'.$staff->staff_name.'</td>

<td class="total" style="padding: 15px 10px;
    background: #fdfdfd;
  
    font-size: 14px;
    line-height: 22px; text-align: center;">'.$ordersingle->duration.' Min</td>

<td class="total" style="padding: 15px 10px;
    background: #fdfdfd;
   
    font-size: 14px;
    line-height: 22px; text-align: center;">KD '.number_format($ordersingle->price,3).'</td>
  
</tr>';

}
$messageBody.='
    



</tbody>
</table>
    
       <table width="100%" class="table518b" align="left" cellpadding="0" cellspacing="0" border="0" style="background: #fff; padding: 20px 20px;">
   <tr>
    
    <td><p class="RegularTextTD" style="font-size: 14px; color: #000; text-align: center;">If you have any questions or need any help, don\'t hesitate to contact our support team!</p></td>
    </tr> 
           
           
        <tr>
    
   
    </tr>    
       
       </table>
       
    <table width="100%" align="left" cellpadding="0" cellspacing="0" border="0" class="table518b" style="mso-table-lspace:0pt;mso-table-rspace:0pt; background: #fff; padding: 20px; text-align: center; border-top: 1px solid #013133;">
 <tbody>
    <tr>
        <!--SHIPPING ADDRESS AND Payment Method Information-->
        <td height="20" class="RegularTextTD" style="font-size: 14px; text-align: center; padding-bottom: 10px; color: #000; line-height: 24px;">
            '.$vendor->paymentcancelpolicy.'
        </td>
        </tr> 
     
    <tr>
<!--SHIPPING ADDRESS AND Payment Method Information-->
<td height="20" class="RegularTextTD" style="font-size: 16px; text-align: center; padding-bottom: 10px; color: #000;;"> any questions please let us know  </td>
</tr> 
     
     <tr>
<!--SHIPPING ADDRESS AND Payment Method Information-->
<td height="20" class="RegularTextTD" style="font-size: 18px; text-align: center; padding-bottom: 10px; color: #000;;"><EMAIL></td>
</tr> 
     
    <tr>
   
   </tr>   
</tbody>
       
       
       


       
       </table> 
       
       
       
       
</table> 
    
    
    
    
</td>
</tr>
</tbody> 
</table> 

</td>
</tr>
</tbody> 
</table>';
				   }	   
			$emails = [$user->email];
            $data = [
                'subject' => $subject,
                'message1' => $messageBody
            ];
			if(!is_numeric($user->email))
			{
				if (App::getLocale() == 'ar') {
				Mail::send('emails.SendInvoicearabic', $data, function ($message) use ($emails,  $subject) {
                $message->to($emails)->subject($subject);
				 });	
				}
				else {
			Mail::send('emails.SendInvoice', $data, function ($message) use ($emails,  $subject) {
                $message->to($emails)->subject($subject);
				 });
				}
			}

          $jsonFilePath = storage_path("firebase/bookme-f44a4-c16fbc6aa3e0.json");
 // Replace with your JSON key file path
               $getFcmKey = $this->getAccessToken($jsonFilePath);
                
                
                
                
                $fcmUrl = 'https://fcm.googleapis.com/v1/projects/bookme-f44a4/messages:send';
               
        $date = date('d-m-Y');
         //$user = $request->user;
        //$countuser = count($user);
        $date = date('d-m-Y');
      
            $url_aws=url('/');
           
       
            $notify_image = "N/A";
      
         

        $notification_title = "Hey ".$user->name.", Your Booking has been placed";
                $notification_text = "Booking Successfully Placed: Your Booking id #".$order->Bookid." price KD ".$order->total_price. " is placed Successfully for ".date('d-M-Y',strtotime($order->service_date))."(".date('h:i A',strtotime($order->service_date)).").";
        
        
        $date = date('d-m-Y');
          
        $created_at = Carbon::now();
		
		  $get_device_id=array();
        
        $get_device_id[] = $user->device_id;
       
        //$loop =  count(array_chunk($get_device_id,600));  // count array chunk 1000
        //$arrayChunk = array_chunk($get_device_id,600);   // devide array in 1000 chunk
        //$device_id = array();
        
   $token='';
        //for($i=0; $i<$loop ;$i++)
        //{
            //foreach($arrayChunk[$i] as $all_device_id)
            //{       
                   
                        $device_id[] =  $user->device_id;
						if($user->device_id!='')
						{
							//$day=date('l', strtotime($date1));
						 $url = 'https://fcm.googleapis.com/v1/projects/bookme-f44a4/messages:send';
            //$body="Hi ".$user->name." your appointment at ".$ongoings->vendor_name." is  today at ".date('H:i A', strtotime($date1))."";
            $customData=$url;
			
			
			 $data = [
    "message" => [
        "token" => $user->device_id, // Device FCM token
        "notification" => [
            "title" => $notification_title,
            "body" => $notification_text,
            "image" => $notify_image, // Add the image URL here
        ],
        
        "apns" => [
            "headers" => [
                "apns-collapse-id" => "solo_changed_administrator",
                "content-available" => "1",
                "apns-priority" => "10",
            ],
            "payload" => [
                "aps" => [
                    "sound" => "default", // Default sound for iOS
                    "badge" => 0, // Set a specific badge number
                ]
            ]
        ],
    ]
];	
			
			
            
       
        //api_key in Firebase Console -> Project Settings -> CLOUD MESSAGING -> Server key
        $server_key = $getFcmKey;
        //header with content_type api key
        $headers = [
            "Authorization: Bearer $server_key",
            'Content-Type: application/json',
        ];
        // CURL request to route notification to FCM connection server (provided by Google)
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            $result = curl_exec($ch);
            if ($result === FALSE) {
                die('Oops! FCM Send Error: ' . curl_error($ch));
            }
			
            curl_close($ch);	
							
				$dd = DB::table('user_notification')
                    ->insert(['user_id'=>$user->id,
                     'noti_title'=>$notification_title,
                     'noti_message'=>$notification_text,
                     'image'=>$notify_image,
                     'created_at'=>Carbon::now()]);	
           
						}
					
					
		}
		else
		{
		$orders = DB::table('orders')->where('id',$order_id)
    				->update(['status'=>3,'payment_method'=>$res->data->transaction->payment_type,'payment_id'=>$res->data->transaction->payment_id,'payment_status'=>'Not Paid','track_id'=>$res->data->transaction->track_id,'payment_date'=>$res->data->transaction->payment_date,'payment_total_price'=> $res->data->transaction->total_price,'reference'=>$res->data->transaction->reference]);
$orders1 = DB::table('order_cart')->where('book_ID',$order_id)
    				->update(['status'=>3]);					
		}
			
		$ordersuccessed = DB::table('orders')->select('id as bookid','total_price','vendor_id','user_id','service_date','mobile','service_time','status','coupon_id','coupon_discount','payment_method','payment_id','payment_status','track_id','payment_date')
                           ->where('id',$order_id)
                           ->first();
	
	
	
$message = array('status'=>'1', 'message'=>'Payment Details', 'data'=>$ordersuccessed );
				return $message;	
			
			
		}
		
		public function Notificationpage()
		{
		$json = file_get_contents('php://input');
		$req_result = json_decode($json, true);
		$payment_id=$req_result['payment_id'];
		$result=$req_result['result'];
		$post_date=$req_result['post_date'];
		$tran_id=$req_result['tran_id'];
		$ref=$req_result['ref'];
		$track_id=$req_result['track_id'];
		$payment_type=$req_result['payment_type'];
		$invoice_id=$req_result['invoice_id'];		
		$curl = curl_init();

				  curl_setopt_array($curl, array(
				  CURLOPT_URL => $this->paymenturl.'get-payment-status/'.$track_id.'',
				  CURLOPT_RETURNTRANSFER => true,
				  CURLOPT_ENCODING => '',
				  CURLOPT_MAXREDIRS => 10,
				  CURLOPT_TIMEOUT => 0,
				  CURLOPT_FOLLOWLOCATION => true,
				  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				  CURLOPT_CUSTOMREQUEST => 'GET',
				  CURLOPT_HTTPHEADER => array(
				  'Content-Type: application/json',
				   'Authorization: Bearer '.$this->api_key.''
				  ),
				));

		$response = curl_exec($curl);

		curl_close($curl);
		$res=json_decode($response);
		$order_id=$res->data->transaction->reference;
		if($res->data->transaction->result=="CAPTURED")
		{
				$orders = DB::table('orders')->where('id',$order_id)
    				->update(['status'=>1,'payment_method'=>$res->data->transaction->payment_type,'payment_id'=>$res->data->transaction->payment_id,'payment_status'=>'Paid','track_id'=>$res->data->transaction->track_id,'payment_date'=>$res->data->transaction->payment_date,'payment_total_price'=> $res->data->transaction->total_price,'reference'=>$res->data->transaction->reference]);
					$orders1 = DB::table('order_cart')->where('book_ID',$order_id)
    				->update(['status'=>1]);
		}
		else
		{
		$orders = DB::table('orders')->where('id',$order_id)
    				->update(['status'=>3,'payment_method'=>$res->data->transaction->payment_type,'payment_id'=>$res->data->transaction->payment_id,'payment_status'=>'Not Paid','track_id'=>$res->data->transaction->track_id,'payment_date'=>$res->data->transaction->payment_date,'payment_total_price'=> $res->data->transaction->total_price,'reference'=>$res->data->transaction->reference]);
$orders1 = DB::table('order_cart')->where('book_ID',$order_id)
    				->update(['status'=>3]);					
		}	
			
		}
		
		public function COD(Request $request)
		{
				$client=$request->user_id;
	@$coupon_code=$request->coupon_code;
	$paymentstatus="Not Paid";
	$paymentmethod="";
	$serviceat='Salon';
	$date = date('Y-m-d');
	$notes=$request->notes;	
		
	
$totaltime=0;
				$pricetotal=0;
 $orders = 1;
   
   $events=DB::table('user_cart')
                ->where('user_id',$client)->orderBy('Cart_ID', 'asc')
                ->get();
$totcount=count($events);
foreach($events as $key=> $event)
			{
				
				$day = date('l', strtotime($event->start_date));
				$staff=$event->staff_id;
				$time=$event->start_time;
				$vendor_id=$event->vendor_id;
				$services=$event->service_id;
				$selected_date=$event->start_date;
				$pricetotal=$pricetotal+$event->price;
				$users=DB::table('users')->select('user_phone')->where('id',$client)->first();
				@$mobile=@$users->user_phone;
	
	/*
	$slots= $this->getTimeSlotbook($selected_date, $day, $vendor_id,$services, $staff);
	
	if(in_array($time,$slots))
	{
		
		
					
				
	}
	else
	{
	$message = array('status'=>'0', 'message'=>'No Time Slot');
	return $message;	
	}
	*/
	
	
			}
			$coupondiscount=0;
			if($coupon_code!='')
			{
			@$coupondiscount=$this->couponCodeStore($client,$vendor_id,$coupon_code);
			
			}
			$bookdate1=$date;
			$bookID="BOOK".rand().time();
			
			$insert = DB::table('orders')
    				->insertGetId(['user_id'=>$client,'vendor_id'=> $vendor_id,'Bookid'=>$bookID,'total_price'=>$pricetotal-$coupondiscount,'service_date'=>$events[0]->start_date,'payment_method'=>$paymentmethod,'payment_status'=>$paymentstatus,'status'=>1,'payment_gateway'=>$paymentmethod,'bookdate'=>$selected_date,'created_at'=>$bookdate1,'updated_at'=>$bookdate1,'Bookingat'=>$serviceat,'notes'=>$notes,'service_time'=> $events[0]->start_time."-".$events[$totcount-1]->end_time,'mobile'=>$mobile,'coupon_id'=>$coupon_code,'coupon_discount'=>$coupondiscount]);
				$order_id=$insert;	
					if($insert!='')
					{
						foreach($events as $key=> $event)
						{
						$insert1 = DB::table('order_cart')
    				->insert(['status'=>3,'service_name'=>$event->service_name,'service_id'=>$event->service_id,'price'=>$event->price,'user_id'=>$event->user_id,'vendor_id'=>$event->vendor_id,'staff_id'=>$event->staff_id,'book_ID'=>$insert,'start_date'=>$event->start_date,'duration'=>$event->duration,'sequence'=>$key]);	
						}
					}
	
	/*
	$ordersuccessed = DB::table('orders')->select('id as bookid','total_price','vendor_id','user_id','service_date','mobile','service_time','status','coupon_id','coupon_discount')
                           ->where('id',$insert)
                           ->first();*/
	$total=$pricetotal-$coupondiscount;
	$link="";

		
		
				$orders = DB::table('orders')->where('id',$order_id)
    				->update(['status'=>1,'payment_method'=>'COD','payment_id'=>'','payment_status'=>'Not Paid','track_id'=>'','payment_date'=>'','payment_total_price'=>'','reference'=>'']);
					$orders1 = DB::table('order_cart')->where('book_ID',$order_id)
    				->update(['status'=>1]);
					 $settings = DB::table('smtp_settings')->where('SMTP_ID', 1)->first();
					Config::set('mail', [
                'driver' => $settings->type,
                'host' => $settings->mail_host,
                'port' => $settings->mail_port,
                'from' => [
                    'address' => $settings->mail_from_address,
                    'name' => $settings->mail_from_name
                ],
                'encryption' => $settings->mail_encryption,
                'username' => $settings->mail_username,
                'password' => $settings->mail_password,
            ]);
			$subject="BookMe - Booking Confirmation";
			$messageBody='';
			$orders=DB::table('order_cart')->select('*')
                   ->where('book_ID',$order_id)->get();
			$order=DB::table('orders')->select('*')
                   ->where('id',$order_id)->first();
				   $user=DB::table('users')->select('name','email','device_id','id')
                   ->where('id',$order->user_id)->first();
				   
				   $vendor=DB::table('vendor')->select('vendor_name','vendor_name_ar','paymentcancelpolicy')
                   ->where('vendor_id',$order->vendor_id)->first();
				   if (App::getLocale() == 'ar') {
					$messageBody.='<table width="100%" align="center" cellpadding="0" cellspacing="0" border="0" bgcolor="#ededed" style="table-layout:fixed;margin:0 auto;mso-table-lspace:0pt;mso-table-rspace:0pt; margin-bottom:0px; padding-top: 0px; border-top:4px solid #008F96; ">
     
     
		<tbody>
          
            
            <tr>
            
			<td align="center">  
              
                
				<table width="700" bgcolor="#2a9444" align="center" cellpadding="0" cellspacing="0" border="0" class="table600" style="mso-table-lspace:0pt;mso-table-rspace:0pt;">
                    
                    
                    
					<tbody><tr>
						<td width="618" class="tdLogo" bgcolor="#2a9444" align="left"> 
                          <div style="background: #008F96;
    width: 700px;
    height: 25px;
    font-family: Arial, Helvetica, sans-serif;
    padding: 10px 15px; text-align: center;"> 
                             <p style=" text-align: center; font-weight:700; font-size:26px; padding: 0px 0px 0px; margin: 0px 0px; color: #fff;":>   تأكيد الحجز  </p> </p>
                              
                           
                            </div>    
                            
							<table width="730" align="left" cellpadding="0" cellspacing="0" border="0" bgcolor="#fff" class="table600Logo" style="mso-table-lspace:0pt;mso-table-rspace:0pt;">
								<tbody>
                                <tr>
								
									<td style="text-align: center; padding: 1px 0px;">
                                      
										
											<a href="#" target="_blank" style="text-align: center;">
                                                <img src="https://appbookme.com/images/logo.png" style="text-align: center;" alt="Logo Image" border="0" align="top" hspace="0" vspace="0" width="130" height="100"></a>

                                                <p style=" text-align: center; font-weight:700; font-size:20px; padding: 10px 0px 0px;"> حجزك تم بنجاح </p> 
                                                
                                              
                                               
												
                                                                
									</td>
								</tr>

                                <tr>
									
									<td style="text-align: right; padding: 5px 20px 30px 20px;">
									
                                      <p class="RegularTextTD" style="font-size: 18px;">'.$user->name.'</p>          
                                              
                                             <p class="RegularTextTD" style="line-height: 24px; font-size: 16px;">  شكراً لحجزك مع <strong> '.$vendor->vendor_name_ar.'</strong>. إذا كنت غير قادر على الحضور لأي سبب، يرجى إلغاء أو تعديل موعدك عبر تطبيق BookMe. شكراً لك!  </p>
												
                                                                
									</td>
								</tr>

                               
							</tbody></table>
							
						</td>
					</tr> 
				</tbody></table>         
            	</td>
		</tr>
	</tbody></table>       
        
        
        
        
      
      
      
      
      
      <!-- INVOICE SECTION -->
<table width="100%" bgcolor="#ededed" align="center" cellpadding="0" cellspacing="0" border="0" style="table-layout: fixed; margin: 0 auto; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> 
<tbody> 

 <tr> 


<td align="center"> 



<table width="730" align="center" cellpadding="0" cellspacing="0" border="0" class="table600" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> 
 <tbody> 
 <tr> 
    <td>


 <table width="730" align="left" cellpadding="0" cellspacing="0" border="0" class="table600" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 0px 10px; background: #fff;"> 
<tbody> 
<tr> 
<td> 
 
    
   <table border="#191818" cellspacing="0" cellpadding="0" class="item-table" style="background: #fff!important; width: 100%;
    border-spacing: 0;
    border-collapse:separate;
    margin-bottom: 0px;
    font-family: Arial, Helvetica, sans-serif; border: 1px solid #013133;  border-radius: 10px 10px 10px 10px;  -moz-border-radius: 10px 10px 10px 10px;
    -webkit-border-radius: 10px 10px 10px 10px; overflow: hidden;">

<thead style=" border-radius: 10px 10px 10px 10px;  -moz-border-radius: 10px 10px 10px 10px;
    -webkit-border-radius: 10px 10px 10px 10px; border-spacing: 0;  border-collapse:collapse;">
<tr style="">

    <th class="text-left" style="padding: 15px 10px;
    background: #fdfdfd;
   
    line-height: 22px; color: #191818;
    font-size: 1em; text-align: center;"> التاريخ والوقت </th>

<th class="text-left" style="padding: 15px 10px;
    background: #fdfdfd;
   
    line-height: 22px; color: #191818;
    font-size: 1em; text-align: center;"> الخدمات </th>

<th class="text-left" style="padding: 15px 10px;
background: #fdfdfd;

line-height: 22px; color: #191818;
font-size: 1em; text-align: center;">الموظف</th>


<th class="text-right total" style="padding: 15px 10px;
background: #fdfdfd;

line-height: 22px; color: #191818;
font-size: 1em; text-align: center;">المدة</th>
   
<th class="text-right total" style="padding: 15px 10px;
    background: #fdfdfd;
   
    line-height: 22px; color: #191818;
    font-size: 1em;text-align: center;">السعر</th>


</tr>
</thead>


<tbody>';
foreach($orders as $ordersingle)
{
$staff=DB::table('staff_profile')->select('staff_name','staff_name_ar')
                   ->where('staff_id',$ordersingle->staff_id)->first();
$messageBody.='

    <td class="text-center" style="padding: 15px 10px;
    background: #fdfdfd;
  
    font-size: 14px;
    line-height: 22px;text-align: center; direction: ltr;">
    <p>'.date('d-M-Y',strtotime($ordersingle->start_date)).' / '.date('h:i A',strtotime($ordersingle->start_date)).'</p>
</td>

<td class="text-left" style="padding: 15px 10px;
    background: #fdfdfd;
   
    font-size: 14px;
    line-height: 22px;text-align: center;">
<a target="_blank" href="#" style="color: #191818;  font-size: 14px;">
    '.$ordersingle->service_name.'

</a>

</td>

<td class="total" style="padding: 15px 10px;
    background: #fdfdfd;
 
    font-size: 14px;
    line-height: 22px; text-align: center;">'.$staff->staff_name_ar.'</td>

<td class="total" style="padding: 15px 10px;
    background: #fdfdfd;
  
    font-size: 14px;
    line-height: 22px; text-align: center;">'.$ordersingle->duration.' Min</td>

<td class="total" style="padding: 15px 10px;
    background: #fdfdfd;
   
    font-size: 14px;
    line-height: 22px; text-align: center;">KD '.number_format($ordersingle->price,3).'</td>
  
</tr>';
}
    



$messageBody.='</tbody>
</table>
    
       <table width="100%" class="table518b" align="left" cellpadding="0" cellspacing="0" border="0" style="background: #fff; padding: 20px 20px;">
   <tr>
    
    <td><p class="RegularTextTD" style="font-size: 16px; color: #000; text-align: center;"> إذا كان لديك أي أسئلة أو تحتاج إلى مساعدة، لا تتردد في التواصل مع فريق الدعم لدينا! </p></td>
    </tr> 
           
           
        <tr>
    
   
    </tr>    
       
       </table>
       
    <table width="100%" align="left" cellpadding="0" cellspacing="0" border="0" class="table518b" style="mso-table-lspace:0pt;mso-table-rspace:0pt; background: #fff; padding: 20px; text-align: center; border-top: 1px solid #013133;">
 <tbody>
    <tr>
        <!--SHIPPING ADDRESS AND Payment Method Information-->
        <td height="20" class="RegularTextTD" style="font-size: 16px; text-align: center; padding-bottom: 10px; color: #000; line-height: 24px;"> '.$vendor->paymentcancelpolicy.' </td>
        </tr> 
     
    <tr>
<!--SHIPPING ADDRESS AND Payment Method Information-->
<td height="20" class="RegularTextTD" style="font-size: 16px; text-align: center; padding-bottom: 10px; color: #000; font-weight: normal;"> إذا كان لديك أي استفسارات، يرجى إبلاغنا.  </td>
</tr> 
     
     <tr>
<!--SHIPPING ADDRESS AND Payment Method Information-->
<td height="20" class="RegularTextTD" style="font-size: 18px; text-align: center; padding-bottom: 10px; color: #000;;"><EMAIL></td>
</tr> 
     
    <tr>
   
   </tr>   
</tbody>
       
       
       


       
       </table> 
       
       
       
       
</table> 
    
    
    
    
</td>
</tr>
</tbody> 
</table> 

</td>
</tr>
</tbody> 
</table> ';

					
				   }
				   else 
				   {
			$messageBody.='<table width="100%" align="center" cellpadding="0" cellspacing="0" border="0" bgcolor="#ededed" style="table-layout:fixed;margin:0 auto;mso-table-lspace:0pt;mso-table-rspace:0pt; margin-bottom:0px; padding-top: 0px; border-top:4px solid #008F96; ">
     
     
		<tbody>
          
            
            <tr>
            
			<td align="center">  
              
                
				<table width="700" bgcolor="#2a9444" align="center" cellpadding="0" cellspacing="0" border="0" class="table600" style="mso-table-lspace:0pt;mso-table-rspace:0pt;">
                    
                    
                    
					<tbody><tr>
						<td width="618" class="tdLogo" bgcolor="#2a9444" align="left"> 
                          <div style="background: #008F96;
    width: 700px;
    height: 25px;
    font-family: Arial, Helvetica, sans-serif;
    padding: 10px 15px; text-align: center;"> 
                             <p style=" text-align: center; font-weight:700; font-size:26px; padding: 0px 0px 0px; margin: 0px 0px; color: #fff;":>   Booking Confirmation </p> </p>
                              
                           
                            </div>    
                            
							<table width="730" align="left" cellpadding="0" cellspacing="0" border="0" bgcolor="#fff" class="table600Logo" style="mso-table-lspace:0pt;mso-table-rspace:0pt;">
								<tbody>
                                <tr>
									
									<td style="text-align: center; padding: 1px 0px;">
                                      
										
											<a href="#" target="_blank" style="text-align: center;">
                                                <img src="https://appbookme.com/images/logo.png" style="text-align: center;" alt="Logo Image" border="0" align="top" hspace="0" vspace="0" width="130" height="100"></a>

                                                <p style=" text-align: center; font-weight:700; font-size:20px; padding: 10px 0px 0px;"> Your Booking was successful!</p> 
                                                
                                              
                                               
												
                                                                
									</td>
								</tr>

                                <tr>
									
									<td style="text-align: left; padding: 5px 20px 30px 20px;">
									
                                      <p class="RegularTextTD" style="font-size: 16x;">'.$user->name.'</p>          
                                              
                                             <p class="RegularTextTD" style="line-height: 24px; font-size: 14px;"> Thank you for your booking with <strong> '.$vendor->vendor_name.'</strong>. If for any reason you cannot make your appointment please cancel or modify your appointment via BookMe App. Thanks!</p>
												
                                                                
									</td>
								</tr>

                               
							</tbody></table>
							
						</td>
					</tr> 
				</tbody></table>         
            	</td>
		</tr>
	</tbody></table>       
        
        
        
        
      
      
      
      
      
      <!-- INVOICE SECTION -->
<table width="100%" bgcolor="#ededed" align="center" cellpadding="0" cellspacing="0" border="0" style="table-layout: fixed; margin: 0 auto; mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> 
<tbody> 

 <tr> 


<td align="center"> 



<table width="730" align="center" cellpadding="0" cellspacing="0" border="0" class="table600" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;"> 
 <tbody> 
 <tr> 
    <td>


 <table width="730" align="left" cellpadding="0" cellspacing="0" border="0" class="table600" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 0px 10px; background: #fff;"> 
<tbody> 
<tr> 
<td> 
 
    
   <table border="#191818" cellspacing="0" cellpadding="0" class="item-table" style="background: #fff!important; width: 100%;
    border-spacing: 0;
    border-collapse:separate;
    margin-bottom: 0px;
    font-family: Arial, Helvetica, sans-serif; border: 1px solid #013133;  border-radius: 10px 10px 10px 10px;  -moz-border-radius: 10px 10px 10px 10px;
    -webkit-border-radius: 10px 10px 10px 10px; overflow: hidden;">

<thead style=" border-radius: 10px 10px 10px 10px;  -moz-border-radius: 10px 10px 10px 10px;
    -webkit-border-radius: 10px 10px 10px 10px; border-spacing: 0;  border-collapse:collapse;">
<tr style="">

    <th class="text-left" style="padding: 15px 10px;
    background: #fdfdfd;
   
    line-height: 22px; color: #191818;
    font-size: 1em; text-align: center;">Date & Time </th>

<th class="text-left" style="padding: 15px 10px;
    background: #fdfdfd;
   
    line-height: 22px; color: #191818;
    font-size: 1em; text-align: center;"> Service items </th>

<th class="text-left" style="padding: 15px 10px;
background: #fdfdfd;

line-height: 22px; color: #191818;
font-size: 1em; text-align: center;">Staff</th>


<th class="text-right total" style="padding: 15px 10px;
background: #fdfdfd;

line-height: 22px; color: #191818;
font-size: 1em; text-align: center;">Duration</th>
   
<th class="text-right total" style="padding: 15px 10px;
    background: #fdfdfd;
   
    line-height: 22px; color: #191818;
    font-size: 1em;text-align: center;">Price</th>


</tr>
</thead>


<tbody>';

foreach($orders as $ordersingle)
{
$staff=DB::table('staff_profile')->select('staff_name')
                   ->where('staff_id',$ordersingle->staff_id)->first();
$messageBody.='
<tr>

    <td class="text-center" style="padding: 15px 10px;
    background: #fdfdfd;
  
    font-size: 14px;
    line-height: 22px;text-align: center; direction: ltr;">
    <p> '.date('d-M-Y',strtotime($ordersingle->start_date)).' / '.date('h:i A',strtotime($ordersingle->start_date)).'</p>
</td>

<td class="text-left" style="padding: 15px 10px;
    background: #fdfdfd;
   
    font-size: 14px;
    line-height: 22px;text-align: center;">
<a target="_blank" href="#" style="color: #191818;  font-size: 14px;">
    '.$ordersingle->service_name.'

</a>

</td>

<td class="total" style="padding: 15px 10px;
    background: #fdfdfd;
 
    font-size: 14px;
    line-height: 22px; text-align: center;">'.$staff->staff_name.'</td>

<td class="total" style="padding: 15px 10px;
    background: #fdfdfd;
  
    font-size: 14px;
    line-height: 22px; text-align: center;">'.$ordersingle->duration.' Min</td>

<td class="total" style="padding: 15px 10px;
    background: #fdfdfd;
   
    font-size: 14px;
    line-height: 22px; text-align: center;">KD '.number_format($ordersingle->price,3).'</td>
  
</tr>';

}
$messageBody.='
    



</tbody>
</table>
    
       <table width="100%" class="table518b" align="left" cellpadding="0" cellspacing="0" border="0" style="background: #fff; padding: 20px 20px;">
   <tr>
    
    <td><p class="RegularTextTD" style="font-size: 14px; color: #000; text-align: center;">If you have any questions or need any help, don\'t hesitate to contact our support team!</p></td>
    </tr> 
           
           
        <tr>
    
   
    </tr>    
       
       </table>
       
    <table width="100%" align="left" cellpadding="0" cellspacing="0" border="0" class="table518b" style="mso-table-lspace:0pt;mso-table-rspace:0pt; background: #fff; padding: 20px; text-align: center; border-top: 1px solid #013133;">
 <tbody>
    <tr>
        <!--SHIPPING ADDRESS AND Payment Method Information-->
        <td height="20" class="RegularTextTD" style="font-size: 14px; text-align: center; padding-bottom: 10px; color: #000; line-height: 24px;">
            '.$vendor->paymentcancelpolicy.'
        </td>
        </tr> 
     
    <tr>
<!--SHIPPING ADDRESS AND Payment Method Information-->
<td height="20" class="RegularTextTD" style="font-size: 16px; text-align: center; padding-bottom: 10px; color: #000;;"> any questions please let us know  </td>
</tr> 
     
     <tr>
<!--SHIPPING ADDRESS AND Payment Method Information-->
<td height="20" class="RegularTextTD" style="font-size: 18px; text-align: center; padding-bottom: 10px; color: #000;;"><EMAIL></td>
</tr> 
     
    <tr>
   
   </tr>   
</tbody>
       
       
       


       
       </table> 
       
       
       
       
</table> 
    
    
    
    
</td>
</tr>
</tbody> 
</table> 

</td>
</tr>
</tbody> 
</table>';
				   }	   
			$emails = [$user->email];
            $data = [
                'subject' => $subject,
                'message1' => $messageBody
            ];
			if(!is_numeric($user->email))
			{
				if (App::getLocale() == 'ar') {
				//Mail::send('emails.SendInvoicearabic', $data, function ($message) use ($emails,  $subject) {
                //$message->to($emails)->subject($subject);
				 //});	
				}
				else {
			//Mail::send('emails.SendInvoice', $data, function ($message) use ($emails,  $subject) {
               // $message->to($emails)->subject($subject);
				 //});
				}
			}

          $jsonFilePath = storage_path("firebase/bookme-f44a4-c16fbc6aa3e0.json");
 // Replace with your JSON key file path
               $getFcmKey = $this->getAccessToken($jsonFilePath);
                
                
                
                
                $fcmUrl = 'https://fcm.googleapis.com/v1/projects/bookme-f44a4/messages:send';
               
        $date = date('d-m-Y');
         //$user = $request->user;
        //$countuser = count($user);
        $date = date('d-m-Y');
      
            $url_aws=url('/');
           
       
            $notify_image = "N/A";
      
         

        $notification_title = "Hey ".$user->name.", Your Booking has been placed";
                $notification_text = "Booking Successfully Placed: Your Booking id #".$order->Bookid." price KD ".$order->total_price. " is placed Successfully for ".date('d-M-Y',strtotime($order->service_date))."(".date('h:i A',strtotime($order->service_date)).").";
        
        
        $date = date('d-m-Y');
          
        $created_at = Carbon::now();
		
		  $get_device_id=array();
        
        $get_device_id[] = $user->device_id;
       
        //$loop =  count(array_chunk($get_device_id,600));  // count array chunk 1000
        //$arrayChunk = array_chunk($get_device_id,600);   // devide array in 1000 chunk
        //$device_id = array();
        
   $token='';
        //for($i=0; $i<$loop ;$i++)
        //{
            //foreach($arrayChunk[$i] as $all_device_id)
            //{       
                   
                        $device_id[] =  $user->device_id;
						if($user->device_id!='')
						{
							//$day=date('l', strtotime($date1));
						 $url = 'https://fcm.googleapis.com/v1/projects/bookme-f44a4/messages:send';
            //$body="Hi ".$user->name." your appointment at ".$ongoings->vendor_name." is  today at ".date('H:i A', strtotime($date1))."";
            $customData=$url;
			
			
			 $data = [
    "message" => [
        "token" => $user->device_id, // Device FCM token
        "notification" => [
            "title" => $notification_title,
            "body" => $notification_text,
            "image" => $notify_image, // Add the image URL here
        ],
        
        "apns" => [
            "headers" => [
                "apns-collapse-id" => "solo_changed_administrator",
                "content-available" => "1",
                "apns-priority" => "10",
            ],
            "payload" => [
                "aps" => [
                    "sound" => "default", // Default sound for iOS
                    "badge" => 0, // Set a specific badge number
                ]
            ]
        ],
    ]
];	
			
			
            
       
        //api_key in Firebase Console -> Project Settings -> CLOUD MESSAGING -> Server key
        $server_key = $getFcmKey;
        //header with content_type api key
        $headers = [
            "Authorization: Bearer $server_key",
            'Content-Type: application/json',
        ];
        // CURL request to route notification to FCM connection server (provided by Google)
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            $result = curl_exec($ch);
            if ($result === FALSE) {
                die('Oops! FCM Send Error: ' . curl_error($ch));
            }
			
            curl_close($ch);	
							
				$dd = DB::table('user_notification')
                    ->insert(['user_id'=>$user->id,
                     'noti_title'=>$notification_title,
                     'noti_message'=>$notification_text,
                     'image'=>$notify_image,
                     'created_at'=>Carbon::now()]);	
           
						}
					
					
		
			
		$ordersuccessed = DB::table('orders')->select('id as bookid','total_price','vendor_id','user_id','service_date','mobile','service_time','status','coupon_id','coupon_discount','payment_method','payment_id','payment_status','track_id','payment_date')
                           ->where('id',$order_id)
                           ->first();
	
	
	
$message = array('status'=>'1', 'message'=>'Payment Details', 'data'=>$ordersuccessed );
				return $message;	
			
			
		}
		
		
			
					

}
