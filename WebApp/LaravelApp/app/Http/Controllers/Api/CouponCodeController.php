<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

class CouponCodeController extends Controller
{
    public function couponCodeStore(Request $request)
{
    try {
        
          $customMessages = [
            'user_id.exists' => 'The selected user does not exist in our records.',
            'vendor_id.exists' => 'The selected vendor does not exist in our records.',
            'coupon_code.exists' => 'The provided coupon code is invalid or not available.',
            'user_id.required' => 'User ID is required.',
            'vendor_id.required' => 'Vendor ID is required.',
            'coupon_code.required' => 'Coupon code is required.',
        ];

        // Validate the request with custom messages
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'vendor_id' => 'required|exists:vendor,vendor_id',
            'coupon_code' => 'required|exists:coupon,coupon_code'
        ], $customMessages);

        $vendorId = $request->vendor_id;

        
        $events = DB::table('user_cart')
            ->select('Cart_ID', 'price')
            ->where('user_id', $request->user_id)
            ->where('user_cart.vendor_id', $vendorId)
            ->orderBy('Cart_ID', 'asc')
            ->get();

        $totalPrice = $events->sum(function($event) {
            return (float) $event->price;
        });

        if ($events->isNotEmpty()) {

           
            $couponCodeOffer = DB::table('coupon')
                ->where('coupon_code', $request->coupon_code)
                ->where(function ($query) {
                    $query->where(function ($query) {
                        $query->where('type', 'Fixed Amount')
                            ->where('applicable', 'All Users');
                    })
                    ->orWhere(function ($query) {
                        $query->where('type', 'Fixed Amount')
                            ->where('applicable', 'Specific Users');
                    })
                    ->orWhere(function ($query) {
                        $query->where('type', 'A percent amount discount')
                            ->where('applicable', 'All Users');
                    })
                    ->orWhere(function ($query) {
                        $query->where('type', 'A percent amount discount')
                            ->where('applicable', 'Specific Users');
                    });
                })
                ->where(function ($query) use ($vendorId) {
                    $query->where('available_for_vendors', 0)
                        ->orWhere(function ($query) use ($vendorId) {
                            $query->where('available_for_vendors', 1)
                                ->where('available_vendors', $vendorId);
                        });
                })
                ->select('type', 'amount')
                ->first();

            if ($couponCodeOffer) {
                $discountAmount = 0;

               
                if ($couponCodeOffer->type === 'Fixed Amount') {
                   $message1=__('messages.Thediscountamountcannotbegreater');
                    if ($couponCodeOffer->amount > $totalPrice) {
                        return response()->json([
                            'status' => '0',
                            'message' =>  $message1,
                            'data' => []
                        ], 422);
                    }
                    
                    $discountAmount = $couponCodeOffer->amount;
                } elseif ($couponCodeOffer->type === 'A percent amount discount') {
                    $discountAmount = $totalPrice * ($couponCodeOffer->amount / 100);
                }

               
                $afterDiscount = max(0, $totalPrice - $discountAmount);
                
             

               
                return response()->json([
                    'status' => '1',
                    'message' => 'Success',
                    'data' => ['afterDiscountTotalPrice' => $afterDiscount,
                    'deductedAmount' => $discountAmount,]
                ], 200);
            } else {
                 $message1=__('messages.Invalidornon-applicable');
                return response()->json([
                    'status' => '0',
                    'message' => $message1,
                    'data' => []
                ], 422);
            }
        } else {
          
           
            return response()->json([
                'status' => '0',
                'message' => 'Your cart is empty.',
                'data' => []
            ], 422);
        }
    }  catch (\Illuminate\Validation\ValidationException $e) {
        
         // Check if the error is specifically for the coupon_code field
        if (isset($e->errors()['coupon_code'])) {
			 $message1=__('messages.Theprovidedcouponcodeisinvalid');
            return response()->json([
                'status' => '0',
                'message' => $message1,
                'data' => null,
            ], 422);
        }
        
        
        
        // Manually format the error messages into the desired structure
        $formattedErrors = [];
        foreach ($e->errors() as $field => $messages) {
            // Add only the first message for each field to avoid repetition
            $formattedErrors[$field] = $messages[0];
        }

        // Return the formatted response
        return response()->json([
            'status' => '0',
            'message' => 'Validation error',
            'data' => $formattedErrors
        ], 422);
    }
}

}

