<?php

namespace App\Http\Controllers\Api;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Carbon\Carbon;
use App\Models\User;
use JWTAuth;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;
use Hash;
use App\Mail\SendMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Config;
use Twilio\Rest\Client;
use App\Services\LocalizationService;
use Illuminate\Support\Facades\App;
class UserController extends Controller
{

 private $noreply_email = '<EMAIL>';
    
    

    /**
     * Get the token array structure.
     *
     * @param  string $token
     *
     * @return \Illuminate\Http\JsonResponse
     */
    

	public function social_login(Request $request)
    {
    $logintype = $request->type;
    $apple_id = $request->apple_id;
	 $name = $request->name;
	 $email = $request->user_email;
	 $user_email = $request->user_email;
	 $device_id= $request->device_id;
    if($logintype == 'google'){
       
	  
       $checkuser = DB::table('users')
                  ->where('email',$user_email)->where('block', 0)->first();
             if($checkuser){
          $updateDeviceId = DB::table('users')
    		              ->where('email',$user_email)
    		              ->update(['name'=>$name,'device_id'=>$device_id,'phone_verified'=>1,'google_id'=>1]);
            $user = User::where('email',$user_email)
                    ->first();
              //$token = JWTAuth::fromUser($user);  
              $tokenResult = $user->createToken('Personal Access Token');
			  $token = $tokenResult->plainTextToken;
             
            $data=array('user'=>$user);
                 $message1=__('messages.loginsuccessfully');      
                 $message = array('status'=>'1', 'message'=>$message1,'data'=>$data, 'token'=>$token);
                return $message;
      }else{
          $created_at = Carbon::now();
        $updated_at = Carbon::now(); 
		  $name=$request->name;
		  
		   
			 
			 
			   $num = mt_rand(100000,999999); 
			    $password = Hash::make($num);
				
				
				$checkUser = DB::table('users')
    					
                        ->where('email', $email)
    					->first();
						
						if($checkUser){
							$message1=__('messages.Useralreadyregistered'); 
    		$message = array('status'=>'1', 'message'=>$message1);
            return $message;
			exit();
    	}
				
		 $insertID=DB::table('users')
                                ->insertGetId
    				(['name'=>$name,'user_phone'=>'','email'=>$email,'dob'=>'','Gender'=>'','password'=>$password,'device_id'=>$device_id,'phone_verified'=>1,'google_id'=>1]);
					 
					$user = User::where('id',$insertID)
                    ->first(); 
              //$token = JWTAuth::fromUser($user);
			  $tokenResult = $user->createToken('Personal Access Token');
			  $token = $tokenResult->plainTextToken;
              $data=array('user'=>$user );
                        
                 
					if($insertID)
					{
						 $message1=__('messages.loginsuccessfully');  
				$message = array('status'=>'1', 'message'=>$message1,'data'=>$data, 'token'=>$token);
                return $message;
					}
					else
					{
						 $message1=__('messages.loginfailed'); 
					$message = array('status'=>'1', 'message'=>$message1);
					return $message;
					}
      }
    }
    else{
     
       if($email!='')
	   {
        $checkuser = DB::table('users')
                 
				 
                  ->where('apple_id',$apple_id)
                  ->orWhere('email', $email)
                  ->first();
	   }
	   else
	   {
		  $checkuser = DB::table('users')
                 
				 
                  ->where('apple_id',$apple_id)
                
                  ->first(); 
	   }

      if($checkuser){
         if($email!='')
	   {
    		 $user = User::where('apple_id',$apple_id)
                          ->orWhere('email', $email)
                    ->first();
					$updateDeviceId = DB::table('users')
    		             ->where('email', $email)
    		              ->update(['apple_id'=>$apple_id,'device_id'=>$device_id,'phone_verified'=>1]);
	   }
		else
		{
		$user = User::where('apple_id',$apple_id)
                         
                    ->first();
$updateDeviceId = DB::table('users')
    		             ->where('apple_id', $apple_id)
    		              ->update(['device_id'=>$device_id,'phone_verified'=>1]);					
		}
		 
            //$token = JWTAuth::fromUser($user); 
              $tokenResult = $user->createToken('Personal Access Token');
			  $token = $tokenResult->plainTextToken;  
            $data=array('user'=>$checkuser );
                       
            $message1=__('messages.loginsuccessfully');
                 $message = array('status'=>'1', 'message'=>$message1,'data'=>$data, 'token'=>$token);
                return $message;
      }else{
        $created_at = Carbon::now();
        $updated_at = Carbon::now(); 
		  $name=$request->name;
		  
		   
			 
			 
			   $num = mt_rand(100000,999999); 
			    $password = Hash::make($num);
				
				
				$checkUser = DB::table('users')
    					
                        ->where('email', $email)
    					->first();
						
						if($checkUser){
							$message1=__('messages.Useralreadyregistered');
    		$message = array('status'=>'1', 'message'=>$message1);
            return $message;
			exit();
    	}
			$dob="0000-00-00";	
		 $insertID=DB::table('users')
                                ->insertGetId
    				(['name'=>$name,'user_phone'=>'','email'=>$email,'dob'=>'0000-00-00','Gender'=>'','password'=>$password,'apple_id'=>$apple_id,'device_id'=>$device_id,'phone_verified'=>1]);
					
					$user1= DB::table('users')->select('id','name','user_phone','email','dob','Gender')
    					->where('id', $insertID)->first();
                     $user = User::where('id',$insertID)
                    ->first(); 
    				//$token = JWTAuth::fromUser($user); 
                $tokenResult = $user->createToken('Personal Access Token');
			  $token = $tokenResult->plainTextToken;
            $data=array('user'=>$user );	
              
					if($insertID)
					{
						  $message1=__('messages.loginsuccessfully');
				$message = array('status'=>'1', 'message'=> $message1,'data'=>$data, 'token'=>$token);
                return $message;
					}
					else
					{
						$message1=__('messages.loginfailed');
					$message = array('status'=>'1', 'message'=>$message1);
					return $message;
					}
      }
    }

   }
  



    public function login_with_email(Request $request)
    
     {
    	$user_email = $request->user_email;
    	$user_password = $request->password;
    	$device_id = $request->device_id;
    
    	
            
    	$checkUser = DB::table('users')
    					->where('email', $user_email)->where('block', 0)
    					->first();
    					
    	/* $checkitemss = DB::table('product_order_details')
            ->where('user_id',$checkUser->id)
            ->where('status', "incart")
            ->count();
          
                */
           
                         
                         
                         
    	if($checkUser){
    	 
    		   $checkUserreg = DB::table('users')
            					->where('email', $user_email)
            					->first();
    
            if(Hash::check($user_password, $checkUserreg->password)){
    		   $updateDeviceId = DB::table('users')
    		                       ->where('email', $user_email)
    		                       ->update(['device_id'=>$device_id]);
    		                       
    		         $user = User::where('email', $user_email)
                    ->first();
                    $data=array('user'=>$user);    
                 //$token = JWTAuth::fromUser($user);
	$tokenResult = $user->createToken('Personal Access Token');
    $token = $tokenResult->plainTextToken;	
 $message1=__('messages.loginsuccessfully');	
    			$message = array('status'=>'1', 'message'=>$message1, 'data'=>$data, 'token'=>$token);
	        	return $message;
                 
    	   }
    	   else{
			   $message1=__('messages.wrongpassword');
    		$message = array('status'=>'0', 'message'=>$message1);
	        return $message;
    	}
    
	}	else{
		$message1=__('messages.UsernotRegistered');
    		$message = array('status'=>'0', 'message'=>'User not Registered or User Removed');
	        return $message;
    	}
     }
    
    
   
    
  public function validates(Request $request)
    {
      
            return response()->json(['error' => 'UnAuthorised'], 401);
        
    }
	
	 public function updateuserprofile(Request $request)
    
     {
		  $user_id=$request->user_id;
		  $name=$request->name;
		  $user_phone=$request->user_phone;
		    $email=$request->email;
			 $dob=$request->date_of_birth;
			  $Gender=$request->Gender;
			   $image=$request->image;
			   $update1='';
			   if($request->image){
				  
            $Image = $request->image;
            $fileName = date('dmyhisa').'-'.$Image->getClientOriginalName();
            $fileName = str_replace(" ", "-", $fileName);
             $Image->move('public/uploads/', $fileName);
            $featuredimage = 'public/uploads/'.$fileName;
			
			 $update1=DB::table('users')->where('id',$user_id)
                                ->update
    				(['image'=>$featuredimage]);
			
        }
		 $update=DB::table('users')->where('id',$user_id)
                                ->update
    				(['name'=>$name,'user_phone'=>$user_phone,'email'=>$email,'dob'=>$dob,'Gender'=>$Gender]); 
					
					if($update or $update1 )
					{
						$message1=__('messages.UserProfileUpdated');
					$message = array('status'=>'1', 'message'=>$message1);
          return $message;	
					}
					else
					{
						$message1=__('messages.UserProfileFailed');
					$message = array('status'=>'1', 'message'=>$message1);
					return $message;
					}
	 }
	 
	 public function updatelanguage(Request $request)
    
     {
		  $user_id=$request->user_id;
		  $language=$request->language;
		 
			
			 $update1=DB::table('users')->where('id',$user_id)
                                ->update
    				(['language'=>$language]);
		
					$message = array('status'=>'1', 'message'=>"success");
				return $message;	
	 }
	 
	 public function viewuserprofile(Request $request)
    
     {
		  $user_id=$request->user_id;
		  
			   
		 $data=DB::table('users')->select('name','user_phone','email','dob','Gender',DB::raw("CONCAT('public/', image) AS image"))->where('id',$user_id)->first();
                               
					
					if($data)
					{
					$message = array('status'=>'1', 'message'=>'User Profile Found','data'=>$data);
          return $message;	
					}
					else
					{
					$message = array('status'=>'1', 'message'=>'User Profile Not Found');
					return $message;
					}
	 }
	  public function RegisterUser(Request $request)
    
     {
		$created_at = Carbon::now();
        $updated_at = Carbon::now(); 
		  $name=$request->name;
		  //$user_phone=$request->user_phone;
		    $email=strtolower($request->email);
			 $dob=$request->date_of_birth;
			  $Gender=$request->Gender;
			 
			   
			    $password = Hash::make($request->password);
				$device_id = $request->device_id;
				
				$checkUser = DB::table('users')
    					->where('email', $email)->where('block', 0)
                        //->orwhere('email', $email)
    					->first();
						if($checkUser && $checkUser->phone_verified==0){
							$delUser = DB::table('users')
									->where('id', $checkUser->id)
									->delete();
									}
									if($checkUser && $checkUser->phone_verified==1){
									    	$message1=__('messages.Useralreadyregistered');
					$message = array('status'=>'0', 'message'=>$message1);
					return $message;
										//$message = array('status'=>'0', 'message'=>'This username already exists. Please try another username');
										//return $message;
									}
									$generator = "1357902468";
									$n=4;
									$otp = ""; 
  
    for ($i = 1; $i <= $n; $i++) { 
        $otp .= substr($generator, (rand()%(strlen($generator))), 1); 
    } 
	if(is_numeric($email))
	{
		$this->sendsms($email,$otp);
	}
	else
	{
	
	$app_name = "BookMe";
	$settings=DB::table('smtp_settings')
					   ->where('SMTP_ID',1)
					   ->first();
					   
					   $config = array(
	'driver'     => $settings->type,
	'host'       => $settings->mail_host,
	'port'       => $settings->mail_port,
	'from'       => array('address' =>$settings->mail_from_address, 'name' =>$settings->mail_from_name ),
	'encryption' => $settings->mail_encryption,
	'username'   => $settings->mail_username,
	'password'   => $settings->mail_password
  );
  Config::set('mail', $config);
					   

		
		$emails = [$email];
 $data = array('name'=>$name, 'subject'=>"Registration - OTP", 'otp'=>$otp);
  if (App::getLocale() == 'ar') {
    Mail::send('admin.mail.otparabic',$data, function($message) use ($emails)
{	
$message->to($emails)->subject("OTP Verification");

});  
  }
  else {
Mail::send('admin.mail.otp',$data, function($message) use ($emails)
{	
$message->to($emails)->subject("OTP Verification");

});
	}
	}
	
		 $insertID=DB::table('users')
                                ->insertGetId
    				(['name'=>$name,'email'=>$email,'dob'=>$dob,'Gender'=>$Gender,'password'=>$password,'otp'=>$otp,'expires_at'=>Carbon::now()->addMinutes(5)]);
					
					$user1= DB::table('users')->select('id','name','user_phone','email','dob','Gender')
    					->where('id', $insertID)->first();
                      
    					
              
					if($insertID)
					{
						
				$message = array('status'=>'1', 'message'=>'Verify OTP', 'data'=>$user1);
				return $message;
					}
					else
					{
						$message1=__('messages.UserRegistrationFailed');
					$message = array('status'=>'1', 'message'=>'User Registration Failed');
					return $message;
					}
	 }
	  public function resendotpregistration(Request $request)
	  {
		  $user_id=$request->user_id;
		  $user1= DB::table('users')->select('id','name','email')
    					->where('id', $user_id)->first();
		$generator = "1357902468";
									$n=4;
									$otp = "";
									$email=$user1->email;
									$name=$user1->name;
  
    for ($i = 1; $i <= $n; $i++) { 
        $otp .= substr($generator, (rand()%(strlen($generator))), 1); 
    } 
	if(is_numeric($email))
	{
		$this->sendsms($email,$otp);
	}
	else
	{
	
	$app_name = "BookMe";
	$settings=DB::table('smtp_settings')
					   ->where('SMTP_ID',1)
					   ->first();
					   
					   $config = array(
	'driver'     => $settings->type,
	'host'       => $settings->mail_host,
	'port'       => $settings->mail_port,
	'from'       => array('address' =>$settings->mail_from_address, 'name' =>$settings->mail_from_name ),
	'encryption' => $settings->mail_encryption,
	'username'   => $settings->mail_username,
	'password'   => $settings->mail_password
  );
  Config::set('mail', $config);
					   

		
		$emails = [$email];
 $data = array('name'=>$name, 'subject'=>"Registration - OTP", 'otp'=>$otp);
 if (App::getLocale() == 'ar') {
    Mail::send('admin.mail.otparabic',$data, function($message) use ($emails)
{	
$message->to($emails)->subject("OTP Verification");

});  
  }
  else {
Mail::send('admin.mail.otp',$data, function($message) use ($emails)
{	
$message->to($emails)->subject("OTP Verification");

});
}

	}
$getUser2 = User::where('id', $user_id)
							 ->update(['otp'=>$otp,'expires_at'=>Carbon::now()->addMinutes(5)]);	
		 $message = array('status'=>'1', 'message'=>"OTP Sent");
							 return $message;  
	  }
	 public function verifyotpregistration(Request $request)
	 {
		 $id = $request->user_id;
		  $otp= $request->otp;
		 // check for otp verify
		 $getUser = DB::table('users')
				   ->where('id', $id)
					 ->first();
			  
			 
		 $user_name =  $getUser->name;
	
		 $user_email = $getUser->email;
			$otpdatabase= $getUser->otp;		 
					 
		 if($getUser){
			   if($otpdatabase ==  $otp && Carbon::now()->lessThanOrEqualTo($getUser->expires_at)){
			 //if($otpdatabase ==  $otp){
				 // verify phone
				 $getUser2 = User::where('id', $id)
							 ->update(['phone_verified'=>1]);
							 $user = User::where('id',$id)
                    ->first();
							 $tokenResult = $user->createToken('Personal Access Token');
			  $token = $tokenResult->plainTextToken;
              $data=array('user'=>$user );
			  $message1=__('messages.UserRegisteredSuccessfully');
							 $message = array('status'=>'1', 'message'=>$message1, 'data'=> $data,'token'=>$token);
							 return $message;
				 
				 }
				 
				
			 
			 else{
				
		  $message1=__('messages.WrongOTP');
				 $message = array('status'=>'0', 'message'=>$message1);
				 return $message;
			 }
		
		 }
		 else{
		  $message1=__('messages.Usernotregistered');
			 $message = array('status'=>'0', 'message'=>$message1);
			 return $message;
		 }
		 
	 }
	 public function sendsms($phone,$otp)
	 {
	     $text=urlencode("Your BookMe verification code is:'.$otp.'");
	     $url="https://api.future-club.com/falconapi/fccsms.aspx?IID=2728&UID=UsrAMB&S=BookMe&G=965$phone&M=$text&L=L";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
$headers = [
    "x-api-key: 1a634556-5b04-48e6-9829-fb594bf162d0", // Replace with your actual API key
];
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
$result = curl_exec($ch);
curl_close($ch);
Log::info($result);
	     
	 }
	 /*
	 public function sendsms($phone,$otp)
	 {
		
                           
       $twilsid = "**********************************";  
       $twiltoken = "2fd046d0426a7127df23bb0c8a68145d"; 
       $twilphone = "+***********"; 
         // send SMS
        // Your Account SID and Auth Token from twilio.com/console
        $sid = $twilsid;
        $token = $twiltoken;
        $client = new Client($sid, $token);
        $user = "+965".$phone;
        // Use the client to do fun stuff like send text messages!
        $client->messages->create(
            // the number you'd like to send the message to
            $user,
            array(
                // A Twilio phone number you purchased at twilio.com/console
                'from' => $twilphone,
                // the body of the text message you'd like to send
                'body' => "Your BookMe verification code is:'.$otp.'"
               
            )
        );

	 }
	 */
	 
	 public function changePassword(Request $request)
    {
        $id = $request->user_id;
        $password = Hash::make($request->password);
        $currentPassword = $request->current_password;
        
        $getUser =DB::table('users')->select('id','name','user_phone','email','dob','Gender', 'password')
                    ->where('id', $id)->first();
    
                    
        if($getUser){
              
               // If current_password is provided, validate it
        if ($currentPassword !== null) {
            if (!Hash::check($currentPassword, $getUser->password)) {
                $message1 = __('messages.wrongpassword');
                return [
                    'status' => '0',
                    'message' => $message1,
                ];
            }
        }
              
              
              
    		  $updated= DB::table('users')
                            ->where('id', $id)
                            ->update(['password'=>$password]);
                                
            if($updated){
    		      $message1=__('messages.Passwordchanged');                   
    			$message = array('status'=>'1', 'message'=>$message1, 'data'=>$getUser);
	        	return $message; 
            }
            else{
				$message1=__('messages.TryAgainLater');
                $message = array('status'=>'1', 'message'=>'Try Again Later');
	        	return $message; 
            }
                 
    	   
    	   
            
            
          
        }
        else{
			$message1=__('messages.Usernotregistered');
            $message = array('status'=>'1', 'message'=>$message1);
            return $message;
        }
		
	



    }
	
		public function logout(Request $request) {
		
		/*
		$forever=true;
			$token = \JWTAuth::parseToken();
\JWTAuth::manager()->invalidate(
    new \Tymon\JWTAuth\Token($request->token),
    
		$forever
);
   */
		 $request->user()->tokens()->delete();

    return response()->json([
        'status' => 'success',
        'message' => 'logout'
    ], 200);
							}
							
							
	public function	addAddress(Request $request)
    {
		 $Customer_ID = $request->user_id;
		  $Name = $request->Name;
		   $Phone_Number = $request->Phone_Number;
		    @$E_mail = $request->E_mail;
			 $Area = $request->Area;
			  $Block = $request->Block;
			   $Street = $request->Street;
			    $HouseNo = $request->HouseNo;
				@$Avenue = $request->Avenue;
				
				$getUser =DB::table('users')->select('id','name','user_phone','email','dob','Gender')
                    ->where('id', $Customer_ID)->first();
            
                   
                    
        if($getUser){
			
			$insertID=DB::table('customeraddress')
                                ->insertGetId
    				(['Customer_ID'=>$Customer_ID,'Name'=>$Name,'Phone_Number'=>$Phone_Number,'E_mail'=>$E_mail,'Area'=>$Area,'Block'=>$Block,'Street'=>$Street,'HouseNo'=>$HouseNo,'Avenue'=>$Avenue]);
			$message1=__('messages.AddressAdded');
			$message = array('status'=>'1', 'message'=>$message1);
	        	return $message; 
		}
		else
		{
		$message = array('status'=>'1', 'message'=>'There is no User Exist');
	        	return $message;	
		}

	}

		public function	editAddress(Request $request)
    {
			$Address_ID= $request->Address_ID;
		 
			$Customer_ID = $request->user_id;
		  $Name = $request->Name;
		   $Phone_Number = $request->Phone_Number;
		    @$E_mail = $request->E_mail;
			 @$Area = $request->Area;
			  $Block = $request->Block;
			   $Street = $request->Street;
			    $HouseNo = $request->HouseNo;
				$Avenue = $request->Avenue;
				
				$getUser =DB::table('users')->select('id','name','user_phone','email','dob','Gender')
                    ->where('id', $Customer_ID)->first();
            
                   
                    
        if($getUser){
			
			$update=DB::table('customeraddress')->where('Address_ID',$Address_ID)->where('Customer_ID',$Customer_ID)
                                ->update
    				(['Name'=>$Name,'Phone_Number'=>$Phone_Number,'E_mail'=>$E_mail,'Area'=>$Area,'Block'=>$Block,'Street'=>$Street,'HouseNo'=>$HouseNo,'Avenue'=>$Avenue]);
			$message1=__('messages.AddressUpdatedSuccessfully');
			$message = array('status'=>'1', 'message'=>$message1);
	        	return $message; 
		}
		else
		{
		$message = array('status'=>'1', 'message'=>'There is no User Exist');
	        	return $message;	
		}

	}

	public function	deleteAddress(Request $request)
    {
			$Address_ID= $request->Address_ID;
		 
			$Customer_ID = $request->user_id;
		 
				
				$getUser =DB::table('users')->select('id','name','user_phone','email','dob','Gender')
                    ->where('id', $Customer_ID)->first();
            
                   
                    
        if($getUser){
			
			$update=DB::table('customeraddress')->where('Address_ID',$Address_ID)->where('Customer_ID',$Customer_ID)
                                ->delete();
    		$message1=__('messages.AddressDeletedSuccessfully');		
			$message = array('status'=>'1', 'message'=>$message1);
	        	return $message; 
		}
		else
		{
		$message = array('status'=>'1', 'message'=>'There is no User Exist');
	        	return $message;	
		}

	}
	public function	MakeDefaultAddress(Request $request)
    {
	$Address_ID= $request->Address_ID;
		 
	$Customer_ID = $request->user_id;	
	$update=DB::table('customeraddress')->where('Customer_ID',$Customer_ID)
                                ->update(['defaultaddress'=>'No']);
	$update=DB::table('customeraddress')->where('Customer_ID',$Customer_ID)->where('Address_ID',$Address_ID)
                                ->update(['defaultaddress'=>'Yes']);
								if($update)
								{
									$message1=__('messages.DefaultAddressUpdatedSuccessfully');
	$message = array('status'=>'1', 'message'=>$message1);
	return $message;
								}
	else
		{
		$message = array('status'=>'1', 'message'=>'There is no Address Exist');
	        	return $message;	
		}	
		
	}


			public function	viewAddresses(Request $request)
    {
			
		 
			$Customer_ID = $request->user_id;
		
				
				$getUser =DB::table('users')->select('id','name','user_phone','email','dob','Gender')
                    ->where('id', $Customer_ID)->first();
            
                   
                    
        if($getUser){
			
			$getAddresses =DB::table('customeraddress')
                    ->where('Customer_ID', $Customer_ID)->get();
					if($getAddresses)
					{
			$message = array('status'=>'1', 'message'=>'Addresses Found','data'=>$getAddresses);
	        	return $message;
					}
					else
					{
					$message = array('status'=>'1', 'message'=>'No Addresses Found');
	        	return $message;	
					}
		}
		else
		{
		$message = array('status'=>'1', 'message'=>'There is no User Exist');
	        	return $message;	
		}

	}
		public function Arealist(Request $request)
    {
		
		
					
					$areas=DB::table('areas')->select('Area_Title')
                ->where('enable',1)->get();
				
				
				
					if($areas)
					{
			$message = array('status'=>'1', 'message'=>'Area Found','data'=>$areas);
	        	return $message;
					}
					else
					{
					$message = array('status'=>'1', 'message'=>'No Area Found');
	        	return $message;	
					}
		
		
		
	}
	
	 public function forgotPassword(Request $request)
    {
        $user_email = $request->user_email;
        $checkUser = DB::table('users')->select('id','user_phone','name','image','email','otp')
                        ->where('email', $user_email)
                   
                        ->first();
		if(!$checkUser)
		{
		$message = array('status'=>'1', 'message'=>'User not registered');
	    return $message;
		exit();		
		}
        $user_name = $checkUser->name;   
         $chars ="0123456789";
                    $otp = "";
                    for ($i = 0; $i < 6; $i++){
                       $otp .= $chars[mt_rand(0, strlen($chars)-1)];
                    }
        if($checkUser){
               
    
                $updateOtp = DB::table('users')
                                ->where('email', $user_email)
                                ->update(['otp'=>$otp, 'expires_at' => Carbon::now()->addMinutes(5)]);
                                
            if($updateOtp){
                if(is_numeric($user_email))
	{
		$this->sendsms($user_email,$otp);
		$mobile="Mobile";
	}
	else
	{
	    $mobile="Mail";
    		     $otpmsg = $this->forgetpassword($user_email,$otp,$user_name); 
	}
    			$message = array('status'=>'1', 'message'=>'Check Your '.$mobile.' for OTP', 'data'=>$checkUser);
	        	return $message; 
            }
            else{
                $message = array('status'=>'1', 'message'=>'Something Went wrong! Please Try Again');
	        	return $message; 
            }
        }                
        else{
            $message = array('status'=>'1', 'message'=>'User not registered');
	        return $message;
        }
        
    }
    
    public function verifyOtp(Request $request)
    {
        $user_email = $request->user_email;
        $otp = $request->otp;
       
       
        $getUser = DB::table('users')->select('id','user_phone','name','image','email','otp','expires_at')
                    ->where('email', $user_email)
                  
                    ->first();
                    
        if($getUser){
            $getotp = $getUser->otp;
            
            if($otp == $getotp && Carbon::now()->lessThanOrEqualTo($getUser->expires_at)){
                $message = array('status'=>'1', 'message'=>"Otp Matched Successfully", 'data'=>$getUser);
                return $message;
            }
            else{
                $message = array('status'=>'1', 'message'=>"Wrong OTP or OTP has expired!");
                return $message;
            }
        }
        else{
            $message = array('status'=>'1', 'message'=>"User not registered");
            return $message;
        }
    }
	
	public function resendotp(Request $request)
    {
        $user_email = $request->user_email;
       $chars ="0123456789";
                    $otp = "";
					for ($i = 0; $i<6; $i++) 
						{
					$otp .= mt_rand(0,9);
						}
                    //for ($i = 0; $i < 6; $i++){
                       //$otp .== $chars[mt_rand(0, strlen($chars)-1)];
                    //}
        
        // check for otp verify
        $getUser =DB::table('users')->select('id','user_phone','name','image','email','otp')
                        ->where('email', $user_email)->first(); 
                    
  
        if($getUser){
          
               $updateOtp = DB::table('users')
                                ->where('email', $user_email)
                                ->update(['otp'=>$otp, 'expires_at' => Carbon::now()->addMinutes(5)]);
                                
            if($updateOtp){
                if(is_numeric($user_email))
	{
		$this->sendsms($user_email,$otp);
		$mobile="Mobile";
	}
	else
	{
	    $mobile="Mail";
    		     $otpmsg = $this->forgetpassword($user_email,$otp,$getUser->name); 
	}
    		                      
    			$message = array('status'=>'1', 'message'=>'Check Your '.$mobile.' for OTP', 'data'=> $getUser);
	        	return $message; 
            }
            else{
                $message = array('status'=>'1', 'message'=>'Something Went wrong! Please Try Again');
	        	return $message; 
            }         
        }
        else{
            $message = array('status'=>'1', 'message'=>"User not found");
            return $message;
        }
        
    }
							
 public function forgetpassword($user_email,$otp,$user_name) {
      
       $app_name = "BookMe";
        $settings=DB::table('smtp_settings')
    	                   ->where('SMTP_ID',1)
    	                   ->first();
						   
						   $config = array(
        'driver'     => $settings->type,
        'host'       => $settings->mail_host,
        'port'       => $settings->mail_port,
        'from'       => array('address' =>$settings->mail_from_address, 'name' =>$settings->mail_from_name ),
        'encryption' => $settings->mail_encryption,
        'username'   => $settings->mail_username,
        'password'   => $settings->mail_password
      );
      Config::set('mail', $config);
						   
        ///$data = array('to' => $user_email, 'from' => $settings->mail_from_address, 'to-name'=>$user_name, 'from-name' => $app_name);
//$emails = [$user->email];
        /*Mail::send('cityadmin.mail.forget', compact('user_email','otp','user_name'), function ($m) use ($data){
                $m->from($data['from'], $data['from-name']);
                $m->to($data['to'], $data['to-name'])->subject("OTP for Forget Password");
            });
            */
			
			$emails = [$user_email];
	 $data = array('user_name'=>$user_name, 'subject'=>"OTP for Forgot Password", 'otp'=>$otp);
	   if (App::getLocale() == 'ar') {
	      Mail::send('admin.mail.forgetarabic',$data, function($message) use ($emails)
{	
    $message->to($emails)->subject("OTP for Forgot Password");
	
});    
	   }
	   else {
   Mail::send('admin.mail.forget',$data, function($message) use ($emails)
{	
    $message->to($emails)->subject("OTP for Forgot Password");
	
});
}
        return "send";
    }
	public function DeleteAccount(Request $request)
	{
		$user_id=$request->user_id;
		$checkUser = DB::table('users')
                        ->where('id', $user_id)
						->first();
						 if($checkUser){
							$updateOtp = DB::table('users')
                                ->where('id', $user_id)
                                ->update(['block'=>1]); 
								$message = array('status'=>'1', 'message'=>'User Account Deleted Successfully');
								return $message;
						 }
						 else
						 {
							$message = array('status'=>'0', 'message'=>'There is no user Exists');
							return $message; 
						 }
                        
	}
	
	
}
