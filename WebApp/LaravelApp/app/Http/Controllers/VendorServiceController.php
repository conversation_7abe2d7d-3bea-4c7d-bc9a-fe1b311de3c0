<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;
use Illuminate\Support\Str;

class VendorServiceController extends Controller
{
     public function vendorServices(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor_id = Session::get('cityid');
			$servicesadmin=DB::table('services')
    	                   ->orderBy('Services_ID', 'asc')
    	                   ->get();
						   
			$servicescategory=DB::table('vendor_servicecategory')->where('vendor_id',$vendor_id)
    	                   ->orderBy('Cat_ID', 'asc')
    	                   ->get();	

	$services=DB::table('vendor_services')->select('Name','Image','servicesfor','Service_Price','Service_Time','Service_Category','Staff_ID','vendor_services.vendor_id','vendor_services.enabled','price','vendor_servicecategory.Cat_Name','ServicesID')->where('vendor_services.vendor_id',$vendor_id)->join('vendor_servicecategory', 'vendor_services.Service_Category', '=','vendor_servicecategory.Cat_ID')
    	                   ->orderBy('ServicesID', 'asc')
    	                   ->get();	

$staff=DB::table('staff_profile')->where('vendor_id',$vendor_id)
    	                   ->orderBy('staff_id', 'asc')
    	                   ->get();							   
						   
			return view('vendor.services',compact('servicesadmin','servicescategory','services','staff'));			   
			
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
	}

 public function vendorServicesSave(Request $request)
    {
		if(Session::has('cityvendor'))
		{
		$vendor_id = Session::get('cityid');
		$services=$request->services;
		$Image=$request->Image;
		$servicesname=$request->servicesname;
		$servicesnamear=$request->servicesnamear;
		$servicesfor=$request->servicesfor;
		$serviceprice=$request->serviceprice;
		$servicetime=$request->servicetime;
		$category=$request->category;
		$events=$request->events;
		  $this->validate(
         $request,
         [	
		 'services'=>'required_without:servicesname',
		'servicesname'=>'required_without:services',
		'servicesnamear'=>'required_without:services',
		 'Image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg,webp|max:2048',
         'servicesfor'=>'required',
         'serviceprice'=>'required',
		'servicetime'=>'required',
		'category'=>'required',
		
				
				
         		
         ]

);
    $service='';
    $servicear='';
    if($services=='')
    {
    $service=$servicesname;
     $servicear=$servicesnamear;	
    }
    if($servicesname=='')
    {
    $service=$services;
    $servicear=$servicesnamear;
    }
    if ($request->hasFile('Image')) {
        $Image = $request->file('Image');
        $fileName = date('dmyhisa') . '-' . $Image->getClientOriginalName();
        $fileName = str_replace(" ", "-", $fileName);
        $Image->move('uploads/', $fileName);
        $featuredimage = 'uploads/' . $fileName;
    } else {
        $featuredimage = ''; 
    }
    $insert = DB::table('vendor_services')->insertGetId([
    'Name' => $service,
    'Name_ar' =>  $servicear,
    'Image' => $featuredimage,
    'servicesfor' => $servicesfor,
    'Service_Price' => $serviceprice,
    'Service_Time' => $servicetime,
    'Service_Category' => $category,
    'vendor_id' => $vendor_id,
    'price' => $serviceprice,
	'events'=>($events ?? 0)
]);
if($events==1)
{
	$dates = $request->input('service_date');
    $fromTimes = $request->input('service_time_from');
    $toTimes = $request->input('service_time_to');
    $maxPersons = $request->input('max_persons');
	$date=date('Y-m-d H:i:s');
foreach ($dates as $index => $date) {	
$insert1 = DB::table('vendor_service_schedule')->insertGetId([
    'service_id' => $insert,
    'service_date' =>  $date,
    'service_time_from' => $fromTimes[$index],
    'service_time_to' => '',
    'max_persons' => $maxPersons[$index],
    'created_at' => $date,
   
]);	
}
}
    return redirect()->back()->with('message', 'Added Successfully');
    		}
    		else
    		{
    		return redirect()->route('login')->withErrors('please login first');	
    		}
    	}

	 public function vendorServicesEdit(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor_id = Session::get('cityid');
			$servicesadmin=DB::table('services')
    	                   ->orderBy('Services_ID', 'asc')
    	                   ->get();
						   
			$servicescategory=DB::table('vendor_servicecategory')->where('vendor_id',$vendor_id)
    	                   ->orderBy('Cat_ID', 'asc')
    	                   ->get();	

	$services=DB::table('vendor_services')->select('*')->where('vendor_services.vendor_id',$vendor_id)->join('vendor_servicecategory', 'vendor_services.Service_Category', '=','vendor_servicecategory.Cat_ID')
    	                   ->where('ServicesID',$request->id)->orderBy('ServicesID', 'asc')
    	                   ->first();	
		$events=$services->event;
		
			if($events==1)
			{
			$servicesevents=DB::table('vendor_service_schedule')->select('*')->where('service_id',$request->id)
    	                  
    	                   ->first();	
			}
			else
			{
			$servicesevents=array();	
			}
						   
			return view('vendor.edit-services',compact('servicesadmin','servicescategory','services','servicesevents'));			   
			
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
	}
	
	public function vendorServicesUpdate(Request $request)
    {
		if(Session::has('cityvendor'))
		{
		$vendor_id = Session::get('cityid');
		$services=$request->services;
		$Image=$request->Image;
		$servicesname=$request->servicesname;
		$servicesnamear=$request->servicesnamear;
		$servicesfor=$request->servicesfor;
		$serviceprice=$request->serviceprice;
		$servicetime=$request->servicetime;
		$category=$request->category;
		$staff_name=$request->staff;
		  $this->validate(
         $request,
         [	
		 'services'=>'required_without:servicesname',
		'servicesname'=>'required_without:services',
		'servicesnamear'=>'required_without:services',
	    'Image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg,webp|max:2048',
         'servicesfor'=>'required',
         'serviceprice'=>'required',
		'servicetime'=>'required',
		'category'=>'required',
		
				
				
         		
         ]

);
     $service='';
    $servicear='';
    if($services=='')
    {
    $service=$servicesname;
    $servicear=$servicesnamear;
    }
    if($servicesname=='')
    {
    $service=$services;
    $servicear=$servicesnamear;
    }
    // if($request->Image){
    //             $Image = $request->Image;
    //             $fileName = date('dmyhisa').'-'.$Image->getClientOriginalName();
    //             $fileName = str_replace(" ", "-", $fileName);
    //              $Image->move('uploads/', $fileName);
    //             $featuredimage = 'uploads/'.$fileName;
    // 			$Update = DB::table('vendor_services')->where('ServicesID', $request->id)->where('vendor_id', $vendor_id)
    //                                 ->update(['Image'=>$featuredimage]);
    //         }
      if ($request->hasFile('Image')) {
        $Image = $request->file('Image');
        $fileName = date('dmyhisa') . '-' . $Image->getClientOriginalName();
        $fileName = str_replace(" ", "-", $fileName);
        $Image->move('uploads/', $fileName);
        $featuredimage = 'uploads/' . $fileName;
    } else {
        $featuredimage = ''; 
    }
    $Update = DB::table('vendor_services')->where('ServicesID', $request->id)->where('vendor_id', $vendor_id)
        				->update(['Name'=>$service,'Name_ar'=>$servicear,'servicesfor'=>$servicesfor,'Service_Price'=>$serviceprice,'Service_Time'=>$servicetime,'Service_Category'=>$category,'Staff_ID'=>$staff_name,'price'=>$serviceprice, 'Image'=>$featuredimage]);
						
					$dates = $request->input('service_date');
    $fromTimes = $request->input('service_time_from');
    $toTimes = $request->input('service_time_to');
    $maxPersons = $request->input('max_persons');
	$date=date('Y-m-d H:i:s');
foreach ($dates as $index => $date) {	
$insert1 = DB::table('vendor_service_schedule')->where('service_id',$request->id)->update(
    [
    'service_date' =>  $date,
    'service_time_from' => $fromTimes[$index],
    'service_time_to' => '',
    'max_persons' => $maxPersons[$index],
'created_at' => $date]);	
}	
						
    return redirect()->route('vendor-services')->with('message', 'Updated Successfully');
    		}
    		else
    		{
    		return redirect()->route('vendor-login')->withErrors('please login first');	
    		}
    	}
	

public function EnableServices(Request $request)
	{
	 if(Session::has('cityvendor'))
     {   
    $vendor_id = Session::get('cityid');
        $id=$request->id;

        $getfile=DB::table('vendor_services')
                ->where('ServicesID',$request->id)->where('vendor_id',$vendor_id)
                ->first();
	
       if($getfile->enabled==1)
	   {
		   $enable=0;
	   }
	    if($getfile->enabled==0)
	   {
		   $enable=1;
	   }

    	$update = DB::table('vendor_services')->where('ServicesID',$request->id)->where('vendor_id',$vendor_id)
                                  
                                ->update(['enabled'=>$enable]);
        if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Enable' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull Enable'); 
        }

      }
    else
      {
        return redirect()->route('vendor-login')->withErrors($this->login_message);
      }	
		
		
		
		
	}	
	
	
	
	public function DeleteServices(Request $request)
	{
		
		if(Session::has('cityvendor'))
     {   
    
       

         $vendor_id = Session::get('cityid');

       

    	$delete=DB::table('vendor_services')->where('ServicesID',$request->id)->where('vendor_id',$vendor_id)->delete();
        if($delete)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Delete' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }

      }
    else
      {
        return redirect()->route('vendor-login')->withErrors($this->login_message);
      }
		
		
	}



	
}
