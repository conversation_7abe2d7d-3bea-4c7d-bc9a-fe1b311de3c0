<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;
use Illuminate\Support\Str;

class VendorClientController extends Controller
{
    public function vendorBooking(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor_id = Session::get('cityid');
			
			$users1 = DB::table('users')->distinct()
    	                   ->where('orders.vendor_id',$vendor_id)->join('orders', 'users.id', '=','orders.user_id')->orderBy('orders.id', 'desc')
    	                   ->get();
							$user2=DB::table('users')->distinct()
    	                   ->where('vendorid',$vendor_id)->orderBy('id', 'desc')
    	                   ->get();
						  

$collection = collect($users1)->unique('users.id');
    $merged     = $collection->merge($user2);
    $users   = $merged->all();
			
			return view('vendor.clientlist',compact('users'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
	}
	
	public function CustomerProfile(Request $request)
	
	{
		$created_at = Carbon::Now();
    if(Session::has('cityvendor'))
		{
			$vendor_id = Session::get('cityid');
			$vendor = Session::get('cityid');
		 $user1=DB::table('users')
                ->where('users.id',$request->id)->where('orders.vendor_id',$vendor_id)->join('orders', 'users.id', '=','orders.user_id')
                ->first();
				$user2=DB::table('users')
    	                   ->where('vendorid',$vendor_id)->where('users.id',$request->id)
    	                   ->first();
						   //$users = $users1.$user2;

$collection = collect($user1);
    $merged     = $collection->merge($user2);
    $user   = $merged->all();
	
    	    $approved = DB::table('orders')->select('orders.*','vendor.*','areas.*','users.id as userid','users.user_phone','users.name','users.email','users.address','users.aboutme')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')->join('users', 'orders.user_id', '=','users.id')->join('areas', 'vendor.area', '=','areas.Area_id')->where('orders.vendor_id',$vendor)->where('orders.status',6)->where('user_id',$request->id)->get();              
    	     $completed = DB::table('orders')->select('orders.*','vendor.*','areas.*','users.id as userid','users.user_phone','users.name','users.email','users.address','users.aboutme')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')->join('users', 'orders.user_id', '=','users.id')->join('areas', 'vendor.area', '=','areas.Area_id')->where('orders.vendor_id',$vendor)->where('orders.status',2)->where('user_id',$request->id)->get();                 
			$pending=DB::table('orders')->select('orders.*','vendor.*','areas.*','users.id as userid','users.user_phone','users.name','users.email','users.address','users.aboutme')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')->join('users', 'orders.user_id', '=','users.id')->join('areas', 'vendor.area', '=','areas.Area_id')->where('orders.vendor_id',$vendor)->where('orders.status',1)->where('user_id',$request->id)->get();
			$cancelled=DB::table('orders')->select('orders.*','vendor.*','areas.*','users.id as userid','users.user_phone','users.name','users.email','users.address','users.aboutme')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')->join('users', 'orders.user_id', '=','users.id')->join('areas', 'vendor.area', '=','areas.Area_id')->where('orders.vendor_id',$vendor)->where('orders.status',3)->where('user_id',$request->id)->get();
		return view('vendor.customerprofile',compact('user','approved','completed','pending','cancelled'));
		}
	else {
		return redirect()->route('vendor-login')->withErrors('please login first');
	
		}
	}
	
	public function SaveCustomer(Request $request)
	
	{
		$created_at = Carbon::Now();
    if(Session::has('cityvendor'))
		{
		$firstname=$request->firstname;
		$lastname=$request->lastname;
		$email=$request->email;
		$phone=$request->phone;
		$gender=$request->gender;
		$address=$request->address;
		$country=$request->country;
		$password=$request->password;
		$this->validate(
         $request,
         [		'firstname'=>'required',
         		'lastname'=>'required',
				 'email' => 'required|email|unique:users,email',
				 'gender'=>'required',
				 'address'=>'required',
				 'country'=>'required',
				 'password'=>'required|min:6',
				 'phone'=>'required'
		 ]

);

$password=Hash::make($password);
$name=$firstname." ".$lastname;
$vendor_id = Session::get('cityid');
$insert = DB::table('users')
    				->insertGetId(['name'=>$name,'user_phone'=>$phone,'firstname'=>$firstname,'lastname'=>$lastname,'email'=>$email,'gender'=>$gender,'address'=>$address,'country'=>$country,'password'=>$password,'vendorid'=>$vendor_id]);
 if($insert!=''){
	 return redirect()->back()->with('message', 'Added Successfully'); 
 }
 else
 {
return redirect()->back()->withErrors('Nothing to Add');	 
 }
		
		}
	else{
		return redirect()->route('vendor-login')->withErrors('please login first');
	
		}
	}
	
	
	
	
}
