<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;
use Illuminate\Support\Str;

class VendorEmployeeController extends Controller
{
    public function VendorEmployeeList(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor_id = Session::get('cityid');
			$employees=DB::table('staff_profile')->where('staff_profile.vendor_id',$vendor_id)
    	                   ->get();
						  
						    $service=array();
						   foreach($employees as $employee)
						   {
						   $servicenames=DB::table('vendor_services')->join('staff_services', 'staff_services.Service_ID', '=','vendor_services.ServicesID')->where('vendor_services.vendor_id',$vendor_id)->where('staff_services.staff_id',$employee->staff_id)
    	                   ->get();
						   $services='';
						    foreach($servicenames as $servicename)
						   {
						   $services.=$servicename->Name.",";
						   }
						   $services=rtrim($services,",");
						   $service[$employee->staff_id]=$services;
						   }
						 
			return view('vendor.employeelist',compact('employees','service'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');		
		}
		
	}
	
	 public function AddVendorEmployee(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor_id = Session::get('cityid');
			$services=DB::table('vendor_services')
    	                   ->where('vendor_id',$vendor_id)
    	                   ->get();
			return view('vendor.add-employee',compact('services'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');		
		}
		
	}
	
	 public function SaveVendorEmployee(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor_id = Session::get('cityid');
			$Image=$request->Image;
			$name=$request->name;
			$name_ar=$request->name_ar;
			$email=$request->email;
			$phone=$request->phone;
			$designation=$request->designation;
			$password=$request->password;
			$services=$request->services;
			$breaktime=$request->breaktime;
			$giveservices=$request->giveservices;
			$sundaytimestart=$request->sundaytimestart;
			$sundaytimeend=$request->sundaytimeend;
			$mondaystart=$request->mondaystart;
			$mondayend=$request->mondayend;
			$tuesdaystart=$request->tuesdaystart;
			$tuesdayend=$request->tuesdayend;
			$wednesdaystart=$request->wednesdaystart;
			$wednesdayend=$request->wednesdayend;
			$thursdaystart=$request->thursdaystart;
			$thursdayend=$request->thursdayend;
			$fridaystart=$request->fridaystart;
			$fridayend=$request->fridayend;
			$saturdaystart=$request->saturdaystart;
			$saturdayend=$request->saturdayend;
			if($breaktime=='')
			{
			$breaktime=0;	
			}
			$this->validate(
         $request,
         [		 'Image'=>'required|image:jpeg,png,jpg,gif,svg,webp|max:2048',
         		'name'=>'required',
         		'name_ar'=>'required',
				 'email' => 'required|email|unique:staff_profile,email',
				 'password'=>'required|min:6',
				 'phone'=>'required',
				 'services.*'=>'required',
				 'giveservices'=>'required',
				'designation'=>'required'
				 
				 
		 ]

);

$password=Hash::make($password);
$date=date('Y-m-d');
if($request->Image){
            $Image = $request->Image;
            $fileName = date('dmyhisa').'-'.$Image->getClientOriginalName();
            $fileName = str_replace(" ", "-", $fileName);
             $Image->move('uploads/', $fileName);
            $featuredimage = 'uploads/'.$fileName;
        }
			$insert = DB::table('staff_profile')
    				->insertGetId(['staff_name'=>$name,'staff_name_ar'=>$name_ar,'staff_image'=> $featuredimage,'email'=>$email,'phone'=>$phone,'vendor_id'=>$vendor_id,'created_at'=>$date,'staff_pass'=>$password,'giveservices'=>$giveservices,'designation'=>$designation,'breaktime'=>$breaktime]);
					 if($insert){
foreach($request->services as $val){
	
	$insert1 = DB::table('staff_services')
    				->insert(['vendor_id'=>$vendor_id,'staff_id'=> $insert,'Service_ID'=>$val]);
	
}
if($sundaytimestart!='' and $sundaytimeend!='')
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$sundaytimestart,'close_hour'=>$sundaytimeend,'days'=>'Sunday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>1]);
}
else
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$sundaytimestart,'close_hour'=>$sundaytimeend,'days'=>'Sunday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>0]);	
}
if($mondaystart!='' and $mondayend!='')
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$mondaystart,'close_hour'=>$mondayend,'days'=>'Monday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>1]);
}
else
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$mondaystart,'close_hour'=>$mondayend,'days'=>'Monday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>0]);	
}
if($tuesdaystart!='' and $tuesdayend!='')
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$tuesdaystart,'close_hour'=>$tuesdayend,'days'=>'Tuesday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>1]);
}
else
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$tuesdaystart,'close_hour'=>$tuesdayend,'days'=>'Tuesday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>0]);	
}
if($wednesdaystart!='' and $wednesdayend!='')
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$wednesdaystart,'close_hour'=>$wednesdayend,'days'=>'Wednesday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>1]);
}
else
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$wednesdaystart,'close_hour'=>$wednesdayend,'days'=>'Wednesday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>0]);	
}
if($thursdaystart!='' and $thursdayend!='')
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$thursdaystart,'close_hour'=>$thursdayend,'days'=>'Thursday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>1]);
}
else
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$thursdaystart,'close_hour'=>$thursdayend,'days'=>'Thursday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>0]);	
}
if($fridaystart!='' and $fridayend!='')
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$fridaystart,'close_hour'=>$fridayend,'days'=>'Friday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>1]);
}
else
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$fridaystart,'close_hour'=>$fridayend,'days'=>'Friday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>0]);	
}
if($saturdaystart!='' and $saturdayend!='')
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$saturdaystart,'close_hour'=>$saturdayend,'days'=>'Saturday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>1]);
}
else
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$saturdaystart,'close_hour'=>$saturdayend,'days'=>'Saturday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>0]);	
}


         return redirect()->back()->with('message', 'Employee Added Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }
		
		
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');		
		}
		
	}
	
	public function EditVendorEmployee(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor_id = Session::get('cityid');
			$services=DB::table('vendor_services')
    	                   ->where('vendor_id',$vendor_id)
    	                   ->get();
			$employee=DB::table('staff_profile')
    	                   ->where('vendor_id',$vendor_id)->where('staff_id',$request->id)
    	                   ->first();
						   
			$servicesstaff=DB::table('staff_services')
    	                   ->where('vendor_id',$vendor_id)->where('staff_id',$request->id)
    	                   ->get();
						   $servicesID=array();
						    foreach($servicesstaff as $servicestaff)
						   {
							  $servicesID[]=$servicestaff->Service_ID; 
						   }
						   
			$timeslots=DB::table('employee_time_slot')
    	                   ->where('vendor_id',$vendor_id)->where('staff_id',$request->id)
    	                   ->get();			   
						  
						   
			return view('vendor.edit-employee',compact('services','employee','servicesID','timeslots'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');		
		}
		
	}
	
	
	
	 public function UpdateVendorEmployee(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor_id = Session::get('cityid');
			$staff_id =$request->id;
			$Image=$request->Image;
			$name=$request->name;
			$name_ar=$request->name_ar;
			$email=$request->email;
			$phone=$request->phone;
			$designation=$request->designation;
			$password=$request->password;
			$services=$request->services;
			$breaktime=$request->breaktime;
			$giveservices=$request->giveservices;
			$sundaytimestart=$request->sundaytimestart;
			$sundaytimeend=$request->sundaytimeend;
			$mondaystart=$request->mondaystart;
			$mondayend=$request->mondayend;
			$tuesdaystart=$request->tuesdaystart;
			$tuesdayend=$request->tuesdayend;
			$wednesdaystart=$request->wednesdaystart;
			$wednesdayend=$request->wednesdayend;
			$thursdaystart=$request->thursdaystart;
			$thursdayend=$request->thursdayend;
			$fridaystart=$request->fridaystart;
			$fridayend=$request->fridayend;
			$saturdaystart=$request->saturdaystart;
			$saturdayend=$request->saturdayend;
			$breaktime=$request->breaktime;
			if($breaktime=='')
			{
			$breaktime=0;	
			}
			$this->validate(
         $request,
         [		
         		'name'=>'required',
         		'name_ar'=>'required',
				 'email' => 'required|email',
				 'phone'=>'required',
				 'services.*'=>'required',
				 'giveservices'=>'required',
				'designation'=>'required'
				 
				 
		 ]

);
if($password!='')
{
$password=Hash::make($password);
$update = DB::table('staff_profile')->where('staff_id', $staff_id)->where('vendor_id', $vendor_id)
    				->update(['staff_pass'=> $password]);
}
$date=date('Y-m-d');
if($request->Image){
            $Image = $request->Image;
            $fileName = date('dmyhisa').'-'.$Image->getClientOriginalName();
            $fileName = str_replace(" ", "-", $fileName);
             $Image->move('uploads/', $fileName);
            $featuredimage = 'uploads/'.$fileName;
			$update = DB::table('staff_profile')->where('staff_id', $staff_id)->where('vendor_id', $vendor_id)
    				->update(['staff_image'=> $featuredimage]);
        }
			$insert = DB::table('staff_profile')->where('staff_id', $staff_id)->where('vendor_id', $vendor_id)
    				->update(['staff_name'=>$name,'staff_name_ar'=>$name_ar,'email'=>$email,'phone'=>$phone,'vendor_id'=>$vendor_id,'created_at'=>$date,'giveservices'=>$giveservices,'designation'=>$designation,'breaktime'=>$breaktime]);
					 $delete= DB::table('staff_services')->where('staff_id', $staff_id)->where('vendor_id', $vendor_id)->delete();
$insert=$staff_id;					 
foreach($request->services as $val){
	
	$insert1 = DB::table('staff_services')
    				->insert(['vendor_id'=>$vendor_id,'staff_id'=> $insert,'Service_ID'=>$val]);
	
}
DB::table('employee_time_slot')->where('staff_id', $staff_id)->where('vendor_id', $vendor_id)->delete();
if($sundaytimestart!='' and $sundaytimeend!='')
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$sundaytimestart,'close_hour'=>$sundaytimeend,'days'=>'Sunday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>1]);
}
else
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$sundaytimestart,'close_hour'=>$sundaytimeend,'days'=>'Sunday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>0]);	
}
if($mondaystart!='' and $mondayend!='')
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$mondaystart,'close_hour'=>$mondayend,'days'=>'Monday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>1]);
}
else
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$mondaystart,'close_hour'=>$mondayend,'days'=>'Monday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>0]);	
}
if($tuesdaystart!='' and $tuesdayend!='')
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$tuesdaystart,'close_hour'=>$tuesdayend,'days'=>'Tuesday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>1]);
}
else
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$tuesdaystart,'close_hour'=>$tuesdayend,'days'=>'Tuesday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>0]);	
}
if($wednesdaystart!='' and $wednesdayend!='')
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$wednesdaystart,'close_hour'=>$wednesdayend,'days'=>'Wednesday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>1]);
}
else
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$wednesdaystart,'close_hour'=>$wednesdayend,'days'=>'Wednesday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>0]);	
}
if($thursdaystart!='' and $thursdayend!='')
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$thursdaystart,'close_hour'=>$thursdayend,'days'=>'Thursday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>1]);
}
else
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$thursdaystart,'close_hour'=>$thursdayend,'days'=>'Thursday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>0]);	
}
if($fridaystart!='' and $fridayend!='')
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$fridaystart,'close_hour'=>$fridayend,'days'=>'Friday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>1]);
}
else
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$fridaystart,'close_hour'=>$fridayend,'days'=>'Friday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>0]);	
}
if($saturdaystart!='' and $saturdayend!='')
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$saturdaystart,'close_hour'=>$saturdayend,'days'=>'Saturday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>1]);
}
else
{
$insert1 = DB::table('employee_time_slot')
    				->insert(['open_hour'=>$saturdaystart,'close_hour'=>$saturdayend,'days'=>'Saturday','vendor_id'=>$vendor_id,'staff_id'=>$insert,'status'=>0]);	
}


        return redirect()->back()->with('message', 'Employee Update Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }
		
		
		
		
	}
	 public function EnableVendorEmployee(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor_id = Session::get('cityid');
			$id=$request->id;

        $getfile=DB::table('staff_profile')
                ->where('staff_id',$request->id)->where('vendor_id',$vendor_id)
                ->first();
	
       if($getfile->enabled==1)
	   {
		   $enable=0;
	   }
	    if($getfile->enabled==0)
	   {
		   $enable=1;
	   }

    	$update = DB::table('staff_profile')->where('staff_id',$request->id)->where('vendor_id',$vendor_id)
                                  
                                ->update(['enabled'=>$enable]);
        if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Enable' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull Enable'); 
        }
		
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');		
		}
		
	}
	
	
	public function VendorEmployeeProfile(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$vendor_id = Session::get('cityid');
			$id=$request->id;

        $employee=DB::table('staff_profile')
                ->where('staff_id',$request->id)->where('vendor_id',$vendor_id)
                ->first();
				$timeslots=DB::table('employee_time_slot')
    	                   ->where('vendor_id',$vendor_id)->where('staff_id',$request->id)
    	                   ->get();	
				$bookings=DB::table('order_cart')->join('orders', 'orders.id', '=','order_cart.book_ID')
                ->where('staff_id',$request->id)->where('order_cart.vendor_id',$vendor_id)
                ->get();
	
     return view('vendor.view-employeeprofile',compact('employee','timeslots','bookings'));
		
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');		
		}
		
	}
	
	
	
}
