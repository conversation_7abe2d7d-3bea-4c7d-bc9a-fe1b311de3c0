<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;
use Illuminate\Support\Str;
use DateTime;

class PromotionBannerController extends Controller
{
    public function PromotionBannerList(Request $request)
		{
				if(Session::has('cityadmin'))
			{
				$banners=DB::table('promotionbanner')->join('vendor_services', 'promotionbanner.ServiceID', '=','vendor_services.ServicesID','left')->select('PromoID','PromoName','PromoImage','ServiceID','promotionbanner.enabled','Name','title','subtitle')->get();
                return view('admin.promotionbannerlist',compact('banners'));
               
				
			}
			else 
			{
					return redirect()->route('login')->withErrors('please login first');
				
			}	
			
			
		}
		
		    public function PromotionBannerEdit(Request $request)
		{
				if(Session::has('cityadmin'))
			{
				$services=DB::table('vendor_services')->get();
				$servicesall=$services->unique('Name');
				$banners=DB::table('promotionbanner')->join('vendor_services', 'promotionbanner.ServiceID', '=','vendor_services.ServicesID','left')->where('PromoID',$request->id)->first();
				$vendors=DB::table('vendor')->where('enabled',1)->get();
                return view('admin.promotionbanneredit',compact('banners','servicesall','vendors'));
               
				
			}
			else 
			{
					return redirect()->route('login')->withErrors('please login first');
				
			}	
			
			
		}
		
		 public function PromotionBannerUpdate(Request $request)
		{
				if(Session::has('cityadmin'))
			{
				$PromoName=$request->PromoName;
		$photo=rtrim($request->photo,",");
		$ServiceID=($request->ServiceID) ? $request->ServiceID : 0;
		$type=($request->promotion_type) ? $request->promotion_type : "";
		$vendor_id=($request->vendor_id) ? $request->vendor_id : 0;
		$title=$request->title;
		$subtitle=$request->subtitle;
			$this->validate($request,[
         		'PromoName'=>'required',
				
			
				'title'=>'required',
				'subtitle'=>'required'
			
         		
         		
         ]

	);
	$date = date('d-m-Y');
	
		if($photo!='')
		{
		$update = DB::table('promotionbanner')
                                ->where('PromoID', $request->id)
                                ->update(['PromoImage'=>$photo]);	
		}
		
			$update = DB::table('promotionbanner')->where('PromoID', $request->id)
    				->update(['PromoName'=>$PromoName,'ServiceID'=>$ServiceID,'title'=>$title,'subtitle'=>$subtitle,'vendor_id'=>$vendor_id,'type'=>$type]);
				

        return redirect()->back()->with('message', 'Promotion Banner Updated Successfully');
     
               
				
			}
			else 
			{
					return redirect()->route('login')->withErrors('please login first');
				
			}	
			
			
		}
		
		 public function PromotionBannerAdd(Request $request)
		{
				if(Session::has('cityadmin'))
			{	
				$services=DB::table('vendor_services')->get();
				$servicesall=$services->unique('Name');
				//$banners=DB::table('promotionbanner')->join('vendor_services', 'promotionbanner.ServiceID', '=','vendor_services.ServicesID')->where('PromoID',$request->id)->get();
				$vendors=DB::table('vendor')->where('enabled',1)->get();
                return view('admin.promotionbanneradd',compact('servicesall','vendors'));
               
				
			}
			else 
			{
					return redirect()->route('login')->withErrors('please login first');
				
			}	
			
			
		}
		
		public function SavePromotionBanner(Request $request)
		{
				if(Session::has('cityadmin'))
			{	
				$PromoName=$request->PromoName;
		$photo=rtrim($request->photo,",");
		$ServiceID=($request->ServiceID) ? $request->ServiceID : 0;
		$type=($request->promotion_type) ? $request->promotion_type : "";
		$vendor_id=($request->vendor_id) ? $request->vendor_id : 0;
		$title=$request->title;
		$subtitle=$request->subtitle;
			$this->validate($request,[
         		'PromoName'=>'required',
				'photo'=>'required',
			
			'title'=>'required',
				'subtitle'=>'required'
         		
         		
         ]

	);
	$date = date('d-m-Y');
	
		
		
			$insert = DB::table('promotionbanner')
    				->insert(['PromoName'=>$PromoName,'PromoImage'=>$photo,'ServiceID'=>$ServiceID,'title'=>$title,'subtitle'=>$subtitle,'vendor_id'=>$vendor_id,'type'=>$type]);
					 if($insert){

        return redirect()->back()->with('message', 'Promotion Banner Added Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }
               
				
			}
			else 
			{
					return redirect()->route('login')->withErrors('please login first');
				
			}	
			
			
		}
		
		 public function DeletePromoBanner(Request $request)
		{
				if(Session::has('cityadmin'))
			{
				

       

    	$delete=DB::table('promotionbanner')->where('PromoID',$request->id)->delete();
        if($delete)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Delete' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }
               
				
			}
			else 
			{
					return redirect()->route('login')->withErrors('please login first');
				
			}	
			
			
		}
		
		
		
		 public function EnablePromoBanner(Request $request)
		{
				if(Session::has('cityadmin'))
			{
				

       

    	 $id=$request->id;

        $getfile=DB::table('promotionbanner')
                ->where('PromoID',$request->id)
                ->first();
	
       if($getfile->enabled==1)
	   {
		   $enable=0;
	   }
	    if($getfile->enabled==0)
	   {
		   $enable=1;
	   }

    	$update = DB::table('promotionbanner')
                                ->where('PromoID', $request->id)
                                ->update(['enabled'=>$enable]);
        if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Enable' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull Enable'); 
        }
               
				
			}
			else 
			{
					return redirect()->route('login')->withErrors('please login first');
				
			}	
			
			
		}
		
		
}
