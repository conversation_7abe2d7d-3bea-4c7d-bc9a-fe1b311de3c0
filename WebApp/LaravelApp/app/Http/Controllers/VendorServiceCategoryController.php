<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;
use Illuminate\Support\Str;

class VendorServiceCategoryController extends Controller
{
     public function vendorServicesCategories(Request $request)
    {
		if(Session::has('cityadmin'))
		{
			
			$vendor=DB::table('vendor')->select('vendor_id','vendor_name')->get();
    	                  
    	                   
						   
			$servicescategory=DB::table('vendor_servicecategory')->join('vendor','vendor_servicecategory.vendor_id','=','vendor.vendor_id')->select('Cat_ID','Cat_Name','Cat_Name_ar','vendor_servicecategory.vendor_id','vendor_servicecategory.Ordering','vendor_servicecategory.enabled','vendor.vendor_name')
    	                   ->orderBy('Ordering', 'asc')
    	                   ->get();	

							   
						   
			return view('admin.servicescategory',compact('servicescategory','vendor'));			   
			
		}
		else
		{
		return redirect()->route('login')->withErrors('please login first');	
		}
	
	}
	
	
	public function vendorServicesCategorySave(Request $request)
    {
		if(Session::has('cityadmin'))
		{
			$vendor_id = $request->vendor_id;
			$Cat_Name=$request->Cat_Name;
			$Cat_Name_ar=$request->Cat_Name_ar;
			$Ordering=$request->Ordering;
		$servicescategory=DB::table('vendor_servicecategory')
    	                   ->where('vendor_id',$vendor_id)->where('Cat_Name',$Cat_Name)->get();
		if(count($servicescategory)>0)
		{
		return redirect()->back()->with('message', 'Services Category Already Exists');
		exit();		
		}
		  $this->validate(
         $request,
         [	
		'Cat_Name_ar'=>'required',
		'Cat_Name'=>'required',
		'Ordering'=>'required'
				
				
         		
         ]

);

$insert = DB::table('vendor_servicecategory')
    				->insertGetId(['Cat_Name'=>$Cat_Name,'Cat_Name_ar'=>$Cat_Name_ar,'vendor_id'=>$vendor_id,'Ordering'=>$Ordering]);
return redirect()->back()->with('message', 'Added Successfully');
		}
		else
		{
		return redirect()->route('login')->withErrors('please login first');	
		}
	}

	 public function vendorServicesEdit(Request $request)
    {
		if(Session::has('cityadmin'))
		{
			$vendor=DB::table('vendor')->select('vendor_id','vendor_name')->get();
			

	$services=DB::table('vendor_servicecategory')
    	                   ->where('Cat_ID',$request->id)
    	                   ->first();	

							   
						   
			return view('admin.edit-servicescategory',compact('services','vendor'));			   
			
		}
		else
		{
		return redirect()->route('login')->withErrors('please login first');	
		}
	}
	
	public function vendorServicesCategoryUpdate(Request $request)
    {
		if(Session::has('cityadmin'))
		{
			$vendor_id = $request->vendor_id;
			$Cat_Name=$request->Cat_Name;
			$Cat_Name_ar=$request->Cat_Name_ar;
			$Ordering=$request->Ordering;
		$this->validate(
         $request,
         [	
		'Cat_Name_ar'=>'required',
		'Cat_Name'=>'required',
		'Ordering'=>'required'
				
				
         		
         ]

);

$Update = DB::table('vendor_servicecategory')->where('Cat_ID', $request->id)
    				->update(['Cat_Name'=>$Cat_Name,'Cat_Name_ar'=>$Cat_Name_ar,'Ordering'=>$Ordering,'vendor_id'=>$vendor_id]);
return redirect()->back()->with('message', 'Updated Successfully');
		}
		else
		{
		return redirect()->route('login')->withErrors('please login first');	
		}
	}
	

public function EnableServicesCategory(Request $request)
	{
	 if(Session::has('cityadmin'))
     {   
   
        $id=$request->id;

        $getfile=DB::table('vendor_servicecategory')
                ->where('Cat_ID',$request->id)
                ->first();
	
       if($getfile->enabled==1)
	   {
		   $enable=0;
	   }
	    if($getfile->enabled==0)
	   {
		   $enable=1;
	   }

    	$update = DB::table('vendor_servicecategory')->where('Cat_ID',$request->id)
                                  
                                ->update(['enabled'=>$enable]);
        if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Enable' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull Enable'); 
        }

      }
    else
      {
        return redirect()->route('login')->withErrors($this->login_message);
      }	
		
		
		
		
	}	
	
	
	
	public function DeleteServicesCategory(Request $request)
	{
		
		if(Session::has('cityadmin'))
     {   
    
       

         $vendor_id = Session::get('cityid');

       

    	$delete=DB::table('vendor_servicecategory')->where('Cat_ID',$request->id)->delete();
        if($delete)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Delete' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }

      }
    else
      {
        return redirect()->route('login')->withErrors($this->login_message);
      }
		
		
	}
	
}
