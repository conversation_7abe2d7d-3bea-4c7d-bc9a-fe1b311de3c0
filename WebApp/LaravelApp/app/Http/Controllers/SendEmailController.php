<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;
use Mail;
use App\Mail\SendMail;
use Config;
class SendEmailController extends Controller
{
    public function SendEmail()
	{
	if(Session::has('cityadmin'))
		{
		 $users = DB::table('users')->get();
    	               
    	                  
		return view('admin.send-email',compact('users'));
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
		}	
	}
	
 public function SendEmailSend(Request $request)
	{

        if (!Session::has('cityadmin')) {
            return redirect()->route('login')->withErrors('Please login first.');
        }

	 // Validate the request data
        $request->validate([
            'users' => 'required',
            'subject' => 'required',
            'message' => 'required',
        ]);
        $userId = $request->input('users');
        $subject = $request->input('subject');
        $messageBody = $request->input('message');
        $files = $request->file('files');
        if($userId!='ALL')
        {
        $user = DB::table('users')->where('id', $userId)->first();
        $settings = DB::table('smtp_settings')->where('SMTP_ID', 1)->first();


        if (!$user || !$settings) {
            return redirect()->back()->withErrors('User or SMTP settings not found.');
        }


         // Configure mail settings
            Config::set('mail', [
                'driver' => $settings->type,
                'host' => $settings->mail_host,
                'port' => $settings->mail_port,
                'from' => [
                    'address' => $settings->mail_from_address,
                    'name' => $settings->mail_from_name
                ],
                'encryption' => $settings->mail_encryption,
                'username' => $settings->mail_username,
                'password' => $settings->mail_password,
            ]);
                                    //dd($settings->type);
                //                     $config = array(
                //     'driver'     => $settings->type,
                //     'host'       => $settings->mail_host,
                //     'port'       => $settings->mail_port,
                //     'from'       => array('address' =>$settings->mail_from_address, 'name' =>$settings->mail_from_name ),
                //     'encryption' => $settings->mail_encryption,
                //     'username'   => $settings->mail_username,
                //     'password'   => $settings->mail_password
                // );
                // Config::set('mail', $config);
	  /*
                                Config::set('mail.mailers.smtp.name', "BOOK ME");
        Config::set('mail.mailers.smtp.host', DB::table('smtp_settings')->find(3)->val);
            Config::set('mail.mailers.smtp.username', DB::table('settings')->find(5)->val);
            Config::set('mail.mailers.smtp.password', DB::table('settings')->find(6)->val);
            Config::set('mail.mailers.smtp.port', DB::table('settings')->find(4)->val);
            */
            $emails = [$user->email];
            $data = [
                'subject' => $subject,
                'message1' => $messageBody
            ];

            // Send email
            Mail::send('emails.SendEmail', $data, function ($message) use ($emails, $files, $subject) {
                $message->to($emails)->subject($subject);

                if ($files) {
                    foreach ($files as $file) {
                        $message->attach($file->getRealPath(), [
                            'as' => $file->getClientOriginalName(),
                            'mime' => $file->getMimeType()
                        ]);
                    }
                }
            });
            
        }
        else
        {
            
          $users = DB::table('users')->get();
        $settings = DB::table('smtp_settings')->where('SMTP_ID', 1)->first();


        if (!$users || !$settings) {
            return redirect()->back()->withErrors('User or SMTP settings not found.');
        }


         // Configure mail settings
            Config::set('mail', [
                'driver' => $settings->type,
                'host' => $settings->mail_host,
                'port' => $settings->mail_port,
                'from' => [
                    'address' => $settings->mail_from_address,
                    'name' => $settings->mail_from_name
                ],
                'encryption' => $settings->mail_encryption,
                'username' => $settings->mail_username,
                'password' => $settings->mail_password,
            ]);
                                    //dd($settings->type);
                //                     $config = array(
                //     'driver'     => $settings->type,
                //     'host'       => $settings->mail_host,
                //     'port'       => $settings->mail_port,
                //     'from'       => array('address' =>$settings->mail_from_address, 'name' =>$settings->mail_from_name ),
                //     'encryption' => $settings->mail_encryption,
                //     'username'   => $settings->mail_username,
                //     'password'   => $settings->mail_password
                // );
                // Config::set('mail', $config);
	  /*
                                Config::set('mail.mailers.smtp.name', "BOOK ME");
        Config::set('mail.mailers.smtp.host', DB::table('smtp_settings')->find(3)->val);
            Config::set('mail.mailers.smtp.username', DB::table('settings')->find(5)->val);
            Config::set('mail.mailers.smtp.password', DB::table('settings')->find(6)->val);
            Config::set('mail.mailers.smtp.port', DB::table('settings')->find(4)->val);
            */
            foreach($users as $user)
            {
            $emails = [$user->email];
            $data = [
                'subject' => $subject,
                'message1' => $messageBody
            ];

            // Send email
            Mail::send('emails.SendEmail', $data, function ($message) use ($emails, $files, $subject) {
                $message->to($emails)->subject($subject);

                if ($files) {
                    foreach ($files as $file) {
                        $message->attach($file->getRealPath(), [
                            'as' => $file->getClientOriginalName(),
                            'mime' => $file->getMimeType()
                        ]);
                    }
                }
            }); 
            
            
        }
        }

            return redirect()->back()->with('message', 'Mail Sent Successfully');
        }
}
