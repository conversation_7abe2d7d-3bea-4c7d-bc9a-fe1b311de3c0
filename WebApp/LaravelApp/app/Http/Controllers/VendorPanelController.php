<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;

class VendorPanelController extends Controller
{
      public function Login()
	{
		
		
		return view('vendor.login');
	}
	public function checkadminLogin(Request $request)
    {
    	$admin_email=$request->admin_email;
    	$admin_pass=$request->admin_pass;

    	$this->validate(
         $request,
         [
         		'admin_email'=>'required',
         		'admin_pass'=>'required',
         ],
         [

         	'admin_email.required'=>'Enter E-Mail',
         	'admin_pass.required'=>'Enter the password',
         ]

);
    	$adminLogin = DB::table('vendor')
    	                   ->where('vendor_email',$admin_email)
    	                   ->first();

if($adminLogin->enabled==0)
					{
					return redirect()->route('vendor-login')->withErrors('Your account has been disabled by Administrator. Please Contact Admin.');
exit();					
					}
    	if($adminLogin){
					
					
         if(Hash::check($admin_pass,$adminLogin->vendor_pass)){
			   session::put('cityid',$adminLogin->vendor_id);
           session::put('cityvendor',$adminLogin->vendor_email);
		    session::put('admin_name',$adminLogin->vendor_name);
		   session::put('admin_image',$adminLogin->vendor_logo);
		   session::put('admin_phone',$adminLogin->vendor_phone);
		   
		   session::put('dashboard',$adminLogin->dashboard);
		   session::put('calendar',$adminLogin->calendar);
		   session::put('booking',$adminLogin->booking);
		   session::put('addbooking',$adminLogin->addbooking);
		   session::put('clientlist',$adminLogin->clientlist);
		   session::put('addclient',$adminLogin->addclient);
		   session::put('editclient',$adminLogin->editclient);
		   session::put('deleteclient',$adminLogin->deleteclient);
		    session::put('viewclient',$adminLogin->viewclient);
		   session::put('employeelist',$adminLogin->employeelist);
		   session::put('addemployee',$adminLogin->addemployee);
		   session::put('editemployee',$adminLogin->editemployee);
		   session::put('viewemployee',$adminLogin->viewemployee);
		   session::put('serviceslist',$adminLogin->serviceslist);
		   session::put('addservice',$adminLogin->addservice);
		   session::put('serviceedit',$adminLogin->serviceedit);
		   session::put('servicedelete',$adminLogin->servicedelete);
		   session::put('reviewslist',$adminLogin->reviewslist);
		   session::put('viewreviews',$adminLogin->viewreviews);
		    session::put('settings',$adminLogin->settings);
			
		 
           return redirect()->route('vendorDashboard');
         }
         else
         {
         	return redirect()->route('vendor-login')->withErrors('wrong password');
         }
    	}
    	else
    	{
             return redirect()->route('vendor-login')->withErrors('invalid email and password');
    	}

    }
	
	public function vendorDashboard(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$email = Session::get('cityvendor');
			$vendor_id = Session::get('cityid');
			$vendor = Session::get('cityid');
			$client = DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->join('users', 'orders.user_id', '=','users.id')->orderBy('orders.id', 'desc')
    	                   ->get()->unique('*.id');
						  
			$clients=count($client);
			
				$services=DB::table('vendor_services')->where('vendor_id',$vendor_id)->get();
				$service1=count($services);
				$total = DB::table('orders')
                ->select(DB::raw('SUM(total_price) as total'))->where('vendor_id',$vendor_id)
                ->get();
				$staff = DB::table('staff_profile')
               ->where('vendor_id',$vendor_id)
                ->get();
				$staffs=count($staff);
				$vendorenddate=DB::table('vendor')->where('vendor_id',$vendor_id)->first();
				
				$vendor_id = Session::get('staffvendor');
		
		if($request->staff=='')
		{
		
		$staff=Session::get('cityid');
		}
		else
		{
		$staff=$request->staff;
		}
		
		//$staffbooking=DB::table('order_cart')->where('staff_id',$staff)
    	                   //->pluck('book_ID')->toArray();
					//$vendor_id=DB::table('order_cart')->where('staff_id',$staff)
    	                   //->pluck('vendor_id')->first();
				   //$staffbooking=array_unique($staffbooking);
				  
			$employees=DB::table('staff_profile')->where('staff_profile.vendor_id',$vendor_id)
    	                   ->get();
				  
						    $service=array();
						   foreach($employees as $employee)
						   {
						   $servicenames=DB::table('vendor_services')->join('staff_services', 'staff_services.Service_ID', '=','vendor_services.ServicesID')->where('vendor_services.vendor_id',$vendor_id)->where('staff_services.staff_id',$employee->staff_id)
    	                   ->get();
						   $services='';
						    foreach($servicenames as $servicename)
						   {
						   $services.=$servicename->Name.",";
						   }
						   $services=rtrim($services,",");
						   $service[$employee->staff_id]=$services;
						   }
						  
			$vendor = $vendor_id;
			$vendor_id = Session::get('cityid');
		$staff=$request->staff;
		$staffbooking=DB::table('order_cart')->where('vendor_id',$vendor_id)->where('staff_id',$staff)
    	                   ->pluck('book_ID')->toArray();
						   
			$employees=DB::table('staff_profile')->where('staff_profile.vendor_id',$vendor_id)
    	                   ->get();
						  
						    $service=array();
						   foreach($employees as $employee)
						   {
						   $servicenames=DB::table('vendor_services')->join('staff_services', 'staff_services.Service_ID', '=','vendor_services.ServicesID')->where('vendor_services.vendor_id',$vendor_id)->where('staff_services.staff_id',$employee->staff_id)
    	                   ->get();
						   $services='';
						    foreach($servicenames as $servicename)
						   {
						   $services.=$servicename->Name.",";
						   }
						   $services=rtrim($services,",");
						   $service[$employee->staff_id]=$services;
						   }
						   $vendor_id = Session::get('cityid');
			$vendor = Session::get('cityid');
			if($staff=='')
			{
			$booking = DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->join('users', 'orders.user_id', '=','users.id')->orderBy('orders.id', 'desc')
    	                   ->get();
			}
			else
			{
			$booking = DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->join('users', 'orders.user_id', '=','users.id')->orderBy('orders.id', 'desc')
    	                   ->whereIn('orders.id', $staffbooking)->get();	
			}
						   //$staffs=DB::table('staff_profile')->where('vendor_id',$vendor)->get();
			$users1 = DB::table('users')->distinct()
    	                   ->where('orders.vendor_id',$vendor_id)->join('orders', 'users.id', '=','orders.user_id')->orderBy('orders.id', 'desc')
    	                   ->get();
							$user2=DB::table('users')->distinct()
    	                   ->where('vendorid',$vendor_id)->orderBy('id', 'desc')
    	                   ->get();
				 $employees1=DB::table('staff_profile')
               ->where('vendor_id',$vendor_id)
                ->get();		   
			$vendor_services = DB::table('vendor_services')
    	                   ->where('vendor_id',$vendor_id)->orderBy('Name', 'asc')
    	                   ->get();		   
						  

$collection = collect($users1);
    $merged     = $collection->merge($user2);
    $users   = $merged->all();
						   //$staffs=DB::table('staff_profile')->where('vendor_id',$vendor)->get();
			$users1 = DB::table('users')->distinct()
    	                   ->where('orders.vendor_id',$vendor_id)->join('orders', 'users.id', '=','orders.user_id')->orderBy('orders.id', 'desc')
    	                   ->get();
							$user2=DB::table('users')->distinct()
    	                   ->where('vendorid',$vendor_id)->orderBy('id', 'desc')
    	                   ->get();
				 $employees1=DB::table('staff_profile')
               ->where('vendor_id',$vendor_id)
                ->get();		   
			$vendor_services = DB::table('vendor_services')
    	                   ->where('vendor_id',$vendor_id)->orderBy('Name', 'asc')
    	                   ->get();		   
						  

$collection = collect($users1);
    $merged     = $collection->merge($user2);
    $users   = $merged->all();
			
$bookingdates=array();

for($i=1;$i<=31;$i++)
{
	$day=$i;
	$month=date('m');
	$year=date('Y');
	$date=$year."-".$month."-".$day;
	$orders=DB::table('orders')->where('vendor_id',$vendor_id)->where('service_date',$date)->count();
	$bookingdates[]=$orders;
	
}
$bookingdate=implode(",",$bookingdates);

$orders = DB::table('orders')->select(
            DB::raw('sum(total_price) as sums'), 
            DB::raw("DATE_FORMAT(service_date,'%m') as monthKey")
  )
  ->whereYear('service_date', date('Y'))
   ->where('vendor_id',$vendor_id)
  ->groupBy('service_date')
  ->orderBy('service_date', 'ASC')
  ->get();

$ordertotal = [0,0,0,0,0,0,0,0,0,0,0,0];

foreach($orders as $order){
    $ordertotal[$order->monthKey-1] = $order->sums;
}  
$max=max($ordertotal);
$ordertotals=implode(",",$ordertotal);


$bookings = DB::table('orders')->select('orders.*','users.id as userid','users.name as name')
    	                   ->where('vendor_id',$vendor_id)->join('users', 'orders.user_id', '=','users.id')->where('service_date', '=', date('Y-m-d'))->orderBy('orders.id', 'desc')
    	                   ->get();
		return view('vendor.dashboard',compact('bookings','max','ordertotals','bookingdate','employees1','clients','service1','total','staffs','vendorenddate','booking'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	
	public function vendorCancel(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$id = Session::get('cityid');
			$vendordetails = DB::table('vendor')->select('paymentcancelpolicy','cancel','cancelduration','cancelfee','modifydate')
    	                   ->where('vendor_id',$id)
    	                   ->first();
						   
		return view('vendor.cancel',compact('vendordetails'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
		public function vendorRefund(Request $request)
    {
		if(Session::has('cityvendor'))
		{
		$booking = DB::table('refunds')->select('refunds.*','orders.*','vendor.*','users.id as userid','users.user_phone','users.name','users.email','users.address','users.aboutme')->join('orders', 'orders.id', '=','refunds.order_id')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')->join('users', 'orders.user_id', '=','users.id')->get();
		return view('vendor.refund_list',compact('booking'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	public function vendorCancelSave(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$id = Session::get('cityid');
			$cancel=$request->cancel;
			$cancelduration=$request->cancelduration;
			$cancelfee=$request->cancelfee;
			$modifydate=$request->modifydate;
			$paymentcancelpolicy=$request->paymentcancelpolicy;
			
    	$this->validate(
         $request,
         [
         		'paymentcancelpolicy'=>'required',
         		'cancelduration'=>'required_if:cancel,in:1',
				'cancelfee'=>'required_if:cancel,in:1',
				
         ]

);
		$update = DB::table('vendor')->where('vendor_id',$id)->update(['paymentcancelpolicy'=>$paymentcancelpolicy,'cancel'=>$cancel,'cancelduration'=>$cancelduration,'cancelfee'=>$cancelfee,'modifydate'=>$modifydate]);

			return redirect()->back()->with('message', 'Details Updated Successfully');

		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	public function vendorBasic(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$id = Session::get('cityid');
			$vendordetails = DB::table('vendor')
    	                   ->where('vendor_id',$id)
    	                   ->first();
$ameneties = DB::table('ameneties')->get();
$amenetiessaved1 = DB::table('vendor_ameneties')->select('ameneties_id')->where('vendor_id',$id)->get()->toArray();
		$amenetiessaved=array();
		foreach($amenetiessaved1 as $amenetiessaved2)
				{
				$amenetiessaved[]=$amenetiessaved2->ameneties_id;
				}						   
		return view('vendor.basic',compact('vendordetails','ameneties','amenetiessaved'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	public function vendorBasicSave(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$id = Session::get('cityid');
			$vendor_name=$request->vendor_name;
			$vendor_name_ar=$request->name_ar;
			$description=$request->description;
			$calendarschedule=$request->calendarschedule;
			$shop_type=$request->shop_type;
			$servicesat=$request->servicesat;
			$extrachargehome=$request->extrachargehome;
			$vendor_phone=$request->vendor_phone;
			 $instagram=$request->instagram;
		 $facebook=$request->facebook;
		$YouTube=$request->YouTube;
		  $X=$request->X;
		  $LinkedIn=$request->LinkedIn;
		  $Snapchat=$request->Snapchat;
		  $Pininterest=$request->Pininterest;
		  $Wechat=$request->Wechat;
		  $Tiktok=$request->Tiktok;
 $ameneties=$request->ameneties;
    	$this->validate(
         $request,
         [		'calendarschedule' => 'required',
         		'vendor_name'=>'required',
				'name_ar'=>'required',
         		'description'=>'required',
				'shop_type'=>'required',
				'servicesat'=>'required',
				'extrachargehome'=>'required',
				'vendor_phone'=>'required',
				'ameneties.*'=>'required',
         ]

);
if($ameneties)
		{
			$delete=DB::table('vendor_ameneties')->where('vendor_id',$id)->delete();
		 foreach($ameneties as $key => $amenety)
            {
				DB::table('vendor_ameneties')
    				->insert(['ameneties_id'=>$amenety,'vendor_id'=>$id]);
			}	
		}
$update = DB::table('vendor')->where('vendor_id',$id)->update(['vendor_name'=>$vendor_name,'vendor_name_ar'=>$vendor_name_ar,'description'=>$description,'shop_type'=>$shop_type,'servicesat'=>$servicesat,'extrachargehome'=>$extrachargehome,'vendor_phone'=>$vendor_phone,'instagram'=>$instagram,'facebook'=>$facebook,'YouTube'=>$YouTube,'X'=>$X,'LinkedIn'=>$LinkedIn,'Snapchat'=>$Snapchat,'Pininterest'=>$Pininterest,'Wechat'=>$Wechat,'Tiktok'=>$Tiktok,'calendarschedule'=>$calendarschedule]);

			return redirect()->back()->with('message', 'Details Updated Successfully');
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
		public function vendorTimings(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$id = Session::get('cityid');
			$vendortime = DB::table('time_slot')
    	                   ->where('vendor_id',$id)
    	                   ->get();
					   
		return view('vendor.vendortimings',compact('vendortime'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	public function vendorAddressUpdate(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$id = Session::get('cityid');
			$address=$request->address;
			$address_ar=$request->address_ar;
			$zipcode=$request->zipcode;
			$area=$request->area;
			$block=$request->block;
			$street=$request->street;
			$street_ar=$request->street_ar;
			$avenue=$request->avenue;
			$avenue_ar=$request->avenue_ar;
			$lat=$request->lat;
			$lng=$request->lng;
			
			$this->validate(
         $request,
         [		'address'=>'required',
				'address_ar'=>'required',
         		'zipcode'=>'required',
         		'area'=>'required',
				'block'=>'required',
				'street'=>'required',
				'avenue'=>'required',
				'street_ar'=>'required',
				'avenue_ar'=>'required',
				'lat'=>'required',
				'lng'=>'required'
         ]

);
			DB::table('vendor')
    				->where('vendor_id', $id)
                                ->update(['vendor_loc'=>$address,'vendor_loc_ar'=>$address_ar,'area'=>$area,'block'=>$block,'street'=>$street,'avenue'=>$avenue,'street_ar'=>$street_ar,'avenue_ar'=>$avenue_ar,'zipcode'=>$zipcode,'lat'=>$lat,'lng'=>$lng]);
								
				
			
					   
		 return redirect()->back()->with('message', 'Address Updated Successfully');
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	public function vendorTimeUpdate(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$id = Session::get('cityid');
			$mondayopen=$request->mondayopen;
			$mondayclose=$request->mondayclose;
			$tuesdayopen=$request->tuesdayopen;
			$tuesdayclose=$request->tuesdayclose;
			$wednesdayopen=$request->wednesdayopen;
			$wednesdayclose=$request->wednesdayclose;
			$thursdayopen=$request->thursdayopen;
			$thursdayclose=$request->thursdayclose;
			$fridayopen=$request->fridayopen;
			$fridayclose=$request->fridayclose;
			$saturdayopen=$request->saturdayopen;
			$saturdayclose=$request->saturdayclose;
			$sundayopen=$request->sundayopen;
			$sundayclose=$request->sundayclose;
			
			DB::table('time_slot')
    				->where('vendor_id', $id)->where('days', 'Monday')
                                ->update(['open_hour'=>$mondayopen,'close_hour'=>$mondayclose]);
								
								DB::table('time_slot')
    				->where('vendor_id', $id)->where('days', 'Tuesday')
                                ->update(['open_hour'=>$tuesdayopen,'close_hour'=>$tuesdayclose]);
								
								DB::table('time_slot')
    				->where('vendor_id', $id)->where('days', 'Wednesday')
                                ->update(['open_hour'=>$wednesdayopen,'close_hour'=>$wednesdayclose]);
								
								DB::table('time_slot')
    				->where('vendor_id', $id)->where('days', 'Thursday')
                                ->update(['open_hour'=>$thursdayopen,'close_hour'=>$thursdayclose]);
								
								DB::table('time_slot')
    				->where('vendor_id', $id)->where('days', 'Friday')
                                ->update(['open_hour'=>$fridayopen,'close_hour'=>$fridayclose]);
								
								DB::table('time_slot')
    				->where('vendor_id', $id)->where('days', 'Saturday')
                                ->update(['open_hour'=>$saturdayopen,'close_hour'=>$saturdayclose]);
								
								DB::table('time_slot')
    				->where('vendor_id', $id)->where('days', 'Sunday')
                                ->update(['open_hour'=>$sundayopen,'close_hour'=>$sundayclose]);
			
					   
		 return redirect()->back()->with('message', 'Timings Updated Successfully');
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	
	public function vendorAddress(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$id = Session::get('cityid');
			$vendordetails = DB::table('vendor')
    	                   ->where('vendor_id',$id)
    	                   ->first();
						    $governarates=DB::table('governarate')
                ->where('enabled',1)
                ->get();
				$html='<option value="">Choose Location</option>';
				foreach($governarates as $governarate)
				{
					$html.='<optgroup label="'.$governarate->Gov_Title.'">';
					$areas=DB::table('areas')
                ->where('enable',1)->where('Gove_ID', '=',$governarate->Gov_ID )
                ->get();
				$selected="";
				foreach($areas as $area)
				{
					if($area->Area_id==$vendordetails->area) {$selected="selected";}
					$html.='<option value="'.$area->Area_id.'" '.$selected.'>'.$area->Area_Title.'</option>';
					
				$selected='';
				}
				$html.='</optgroup>';	
				}
					   
		return view('vendor.vendoraddress',compact('vendordetails','html'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	public function vendorLogo(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$id = Session::get('cityid');
			$vendordetails = DB::table('vendor')
    	                   ->where('vendor_id',$id)
    	                   ->first();
$vendor_photos = DB::table('vendor_photos')->where('vendor_id',$id)->get();						   
		return view('vendor.logo',compact('vendordetails','vendor_photos'));
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	public function vendorLogoUpdate(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$id = Session::get('cityid');
			$featuredimage= $request->vendor_logo;
			$this->validate($request,[
		 
			'vendor_logo'=>'image:jpeg,png,jpg,gif,svg|max:2048',

	   ]);
	if($request->vendor_logo){
            $Icon = $request->vendor_logo;
            $fileName = date('dmyhisa').'-'.$featuredimage->getClientOriginalName();
            $fileName = str_replace(" ", "-", $fileName);
            $featuredimage->move('uploads/', $fileName);
            $featuredimage = 'uploads/'.$fileName;
			
			DB::table(' vendor')
    				->where('vendor_id', $id)
                                ->update(['vendor_logo'=>$featuredimage]);
        }
		$this->validate($request,[
		 
			'photos.*'=>'required|image:jpeg,png,jpg,gif,svg|max:2048',

	   ]);
	  
	   $images='';
	   if ($request->file('photos')){
            foreach($request->file('photos') as $key => $file)
            {
                $fileName = time().rand(1,99).'.'.$file->extension();  
                $file->move('uploads/', $fileName);
                $photos='uploads/'.$fileName;
				
				DB::table('vendor_photos')
    				->insert(['vendor_id'=>$id,'photo'=>$photos]);
								


				
            }
        }
		

		
		  return redirect()->back()->with('message', 'Updated Successfully');
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	public function vendorDeletephoto(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$id = Session::get('cityid');
			$delete=DB::table('vendor_photos')->where('Vendor_photoID',$request->id)->where('vendor_id',$id)->delete();
        if($delete)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Delete' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }
		}
		
	}
	
	 public function VendorLogout(Request $request)
     {
      $request->session()->forget('cityid');
	  $request->session()->forget('cityvendor');
	  $request->session()->forget('admin_name');
	  $request->session()->forget('admin_image');
	  $request->session()->forget('admin_phone');
	  $request->session()->forget('dashboard');
	  $request->session()->forget('calendar');
	  $request->session()->forget('booking');
	  $request->session()->forget('addbooking');
	  $request->session()->forget('clientlist');
	  $request->session()->forget('addclient');
	  $request->session()->forget('editclient');
	  $request->session()->forget('deleteclient');
	  $request->session()->forget('viewclient');
	  $request->session()->forget('employeelist');
	  $request->session()->forget('addemployee');
	  $request->session()->forget('editemployee');
	  $request->session()->forget('viewemployee');
	  $request->session()->forget('serviceslist');
	  $request->session()->forget('addservice');
	  $request->session()->forget('serviceedit');
	  $request->session()->forget('servicedelete');
	  $request->session()->forget('reviewslist');
	  $request->session()->forget('viewreviews');
	  $request->session()->forget('settings');
	 
		
           return redirect()->route('vendor-login')->withErrors("Logged Out Successfully");

     }
	 
	 
	 
	 
	 public function vendorEditProfile(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			$cityid = Session::get('cityid');
				$vendor = DB::table('vendor')
    	                   ->where('vendor_id',$cityid)
    	                   ->first();
		return view('vendor.editprofile',$vendor);
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	 public function vendorChangeaddress(Request $request)
    {
		if(Session::has('cityvendor'))
		{
			
		return view('vendor.change-password');
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	 public function vendorchangepassword(Request $request)
    {
		if(Session::has('cityvendor'))
		{
		$id = Session::get('cityid');
			$password=$request->password;
			$repassword=$request->repassword;
$this->validate(
         $request,
         [
         		'password' => 'required|repassword|min:6',
				
         ]

);
$password=Hash::make($password);

$update=DB::table('vendor')->where('vendor_id ', $id)
                                ->update
    				(['vendor_pass'=>$password]);
			
		return redirect()->back()->with('message', 'Updated Successfully');
		}
		else
		{
		return redirect()->route('vendor-login')->withErrors('please login first');	
		}
		
	}
	
	
}
