<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Session;
use Hash;
use Carbon\Carbon;
use File;
use Config;
use App\Mail\ExpiryMail;
use Mail;
class AdminController extends Controller
{
     public function Login()
	{
		
		
		return view('login');
	}
	 public function checkadminLogin(Request $request)
    {
    	$admin_email=$request->admin_email;
    	$admin_pass=$request->admin_pass;

    	$this->validate(
         $request,
         [
         		'admin_email'=>'required',
         		'admin_pass'=>'required',
         ],
         [

         	'admin_email.required'=>'Enter E-Mail',
         	'admin_pass.required'=>'Enter the password',
         ]

);
    	$adminLogin = DB::table('admin')
    	                   ->where('admin_email',$admin_email)
    	                   ->first();


    	if($adminLogin){

         if(Hash::check($admin_pass,$adminLogin->admin_pass)){
           session::put('cityadmin',$adminLogin->admin_email);
		    session::put('admin_name1',$adminLogin->admin_name);
		   session::put('admin_image1',$adminLogin->admin_image);
		   session::put('admin_phone1',$adminLogin->admin_phone);
		   session::put('DashBoardView',$adminLogin->DashBoardView);
		   session::put('CustomerView',$adminLogin->CustomerView);
		   session::put('AllVendorView',$adminLogin->AllVendorView);
		   session::put('AddVendor',$adminLogin->AddVendor);
		   session::put('EditVendor',$adminLogin->EditVendor);
		   session::put('DeleteVendor',$adminLogin->DeleteVendor);
		   session::put('ServiceCategoryList',$adminLogin->ServiceCategoryList);
		   session::put('AddServiceCategory',$adminLogin->AddServiceCategory);
		   session::put('EditServiceCategory',$adminLogin->EditServiceCategory);
		   session::put('DeleteServiceCategory',$adminLogin->DeleteServiceCategory);
		   session::put('ServicesList',$adminLogin->ServicesList);
		   session::put('AddServices',$adminLogin->AddServices);
		   session::put('EditServices',$adminLogin->EditServices);
		   session::put('DeleteServices',$adminLogin->DeleteServices);
		   session::put('ViewSaleReport',$adminLogin->ViewSaleReport);
		   session::put('ViewVendorReport',$adminLogin->ViewVendorReport);
		   session::put('ViewUserReport',$adminLogin->ViewUserReport);
		   session::put('ViewAllBooking',$adminLogin->ViewAllBooking);
		   session::put('ViewAllCancelBooking',$adminLogin->ViewAllCancelBooking);
		   session::put('ViewNotification',$adminLogin->ViewNotification);
		   session::put('SendEmails',$adminLogin->SendEmails);
		   session::put('SendMessages',$adminLogin->SendMessages);
		    session::put('ViewUploadedFiles',$adminLogin->ViewUploadedFiles);
		   session::put('AddUploadedFIles',$adminLogin->AddUploadedFIles);
			session::put('DeleteUploadedFIles',$adminLogin->DeleteUploadedFIles);
			session::put('ViewPromotionsList',$adminLogin->ViewPromotionsList);
			session::put('AddPromotion',$adminLogin->AddPromotion);
			 session::put('EditPromotion',$adminLogin->EditPromotion);
			
			 session::put('DeletePromotion',$adminLogin->DeletePromotion);
			  session::put('SettingsGeneral',$adminLogin->SettingsGeneral);
			   session::put('SMTP',$adminLogin->SMTP);
			    session::put('Governarate',$adminLogin->Governarate);
				 session::put('Areas',$adminLogin->Areas);
				  session::put('PrivacyPolicy',$adminLogin->PrivacyPolicy);
				   session::put('TermsandConditions',$adminLogin->TermsandConditions);
				    session::put('ViewStaffs',$adminLogin->ViewStaffs);
					session::put('AddStaffs',$adminLogin->AddStaffs);
					session::put('EditStaff',$adminLogin->EditStaff);
					session::put('DeleteStaff',$adminLogin->DeleteStaff);
					session::put('StaffPermission',$adminLogin->StaffPermission);
           return redirect()->route('adminDashboard');
         }
         else
         {
         	return redirect()->route('login')->withErrors('wrong password');
         }
    	}
    	else
    	{
             return redirect()->route('login')->withErrors('invalid email and password');
    	}

    }
	
	 public function adminLogout(Request $request)
     {
      $request->session()->forget('cityadmin');
	  $request->session()->forget('admin_email');
	  $request->session()->forget('admin_name');
	  $request->session()->forget('admin_image');
	  $request->session()->forget('admin_phone');
	  $request->session()->forget('DashBoardView');
	  $request->session()->forget('CustomerView');
	  $request->session()->forget('AllVendorView');
	  $request->session()->forget('AddVendor');
	  $request->session()->forget('EditVendor');
	  $request->session()->forget('DeleteVendor');
	  $request->session()->forget('ServiceCategoryList');
	  $request->session()->forget('AddServiceCategory');
	  $request->session()->forget('EditServiceCategory');
	  $request->session()->forget('DeleteServiceCategory');
	  $request->session()->forget('ServicesList');
	  $request->session()->forget('AddServices');
	  $request->session()->forget('EditServices');
	  $request->session()->forget('DeleteServices');
	  $request->session()->forget('ViewSaleReport');
	  $request->session()->forget('ViewVendorReport');
	  $request->session()->forget('ViewUserReport');
	  $request->session()->forget('ViewAllBooking');
	  $request->session()->forget('ViewAllCancelBooking');
	  $request->session()->forget('ViewNotification');
	  $request->session()->forget('SendEmails');
	   $request->session()->forget('SendMessages');
	   $request->session()->forget('ViewUploadedFiles');
	   $request->session()->forget('AddUploadedFIles');
	   $request->session()->forget('DeleteUploadedFIles');
	   $request->session()->forget('ViewPromotionsList');
	   $request->session()->forget('AddPromotion');
	    $request->session()->forget('EditPromotion');
		$request->session()->forget('DeletePromotion');
		$request->session()->forget('SettingsGeneral');
		$request->session()->forget('Governarate');
		$request->session()->forget('Areas');
		$request->session()->forget('PrivacyPolicy');
		$request->session()->forget('TermsandConditions');
		$request->session()->forget('ViewStaffs');
		$request->session()->forget('AddStaffs');
		$request->session()->forget('EditStaff');
		$request->session()->forget('DeleteStaff');
		$request->session()->forget('StaffPermission');
		
           return redirect()->route('login')->withErrors("Logged Out Successfully");

     }
	 public function EditProfile(Request $request)
	 {
		if(Session::has('cityadmin'))
		{
			$email = Session::get('cityadmin');
				$user = DB::table('admin')
    	                   ->where('admin_email',$email)
    	                   ->first();
		return view('admin.editprofile',compact('user'));
		}
		else
		{
		return redirect()->route('login')->withErrors('please login first');	
		}
		 
		 
		 
		 
		 
	 }
	 
	  public function SaveEditProfile(Request $request)
	 {
		if(Session::has('cityadmin'))
		{
			$email = Session::get('cityadmin');
			$this->validate($request,[
		   'name' => 'required',
		  
		   'phone' => 'required',
		   'email' => 'required'

	   ]);
			$name=$request->name;
			
			$phone=$request->phone;
			$email1=$request->email;
			$password=$request->password;
			$photo=$request->photo;
			$date = date('d-m-Y');
        $created_at=date('d-m-Y h:i a');
        if($photo){
           
            $fileName = date('dmyhisa').'-'.$photo->getClientOriginalName();
            $fileName = str_replace(" ", "-", $fileName);
            $photo->move('settings/images/'.$date.'/', $fileName);
            $photo = 'settings/images/'.$date.'/'.$fileName;
			$updates = DB::table('admin')->where('admin_email', $email)
    				->update([ 'admin_image'=>$photo]);
        }
		if($password!='')
		{
			
		$new_pass=Hash::make($password);
		$updated = DB::table('admin')->where('admin_email', $email)
    				->update(['admin_pass'=>$new_pass]);
		}
		$updates = DB::table('admin')->where('admin_email', $email)
    				->update(['admin_name'=>$name,'admin_phone'=>$phone]);
					
					if($email!=$email1)
					{
						echo "Hi3";
					$updates1 = DB::table('admin')->where('admin_email', $email)
    				->update(['admin_email'=>$email1]);
						if($updates1)
						{
							  $request->session()->forget('cityadmin');
	  $request->session()->forget('admin_email');
	  $request->session()->forget('admin_name');
	  $request->session()->forget('admin_image');
	  $request->session()->forget('admin_phone');
	  $request->session()->forget('DashBoardView');
	  $request->session()->forget('CustomerView');
	  $request->session()->forget('AllVendorView');
	  $request->session()->forget('AddVendor');
	  $request->session()->forget('EditVendor');
	  $request->session()->forget('DeleteVendor');
	  $request->session()->forget('ServiceCategoryList');
	  $request->session()->forget('AddServiceCategory');
	  $request->session()->forget('EditServiceCategory');
	  $request->session()->forget('DeleteServiceCategory');
	  $request->session()->forget('ServicesList');
	  $request->session()->forget('AddServices');
	  $request->session()->forget('EditServices');
	  $request->session()->forget('DeleteServices');
	  $request->session()->forget('ViewSaleReport');
	  $request->session()->forget('ViewVendorReport');
	  $request->session()->forget('ViewUserReport');
	  $request->session()->forget('ViewAllBooking');
	  $request->session()->forget('ViewAllCancelBooking');
	  $request->session()->forget('ViewNotification');
	  $request->session()->forget('SendEmails');
	   $request->session()->forget('SendMessages');
	   $request->session()->forget('ViewUploadedFiles');
	   $request->session()->forget('AddUploadedFIles');
	   $request->session()->forget('DeleteUploadedFIles');
	   $request->session()->forget('ViewPromotionsList');
	   $request->session()->forget('AddPromotion');
	    $request->session()->forget('EditPromotion');
		$request->session()->forget('DeletePromotion');
		$request->session()->forget('SettingsGeneral');
		$request->session()->forget('Governarate');
		$request->session()->forget('Areas');
		$request->session()->forget('PrivacyPolicy');
		$request->session()->forget('TermsandConditions');
		$request->session()->forget('ViewStaffs');
		$request->session()->forget('AddStaffs');
		$request->session()->forget('EditStaff');
		$request->session()->forget('DeleteStaff');
		$request->session()->forget('StaffPermission');
		
           return redirect()->route('login')->withErrors("Logged Out Successfully");
						}
					}
					
					
        return redirect()->back()->with('message', 'Profile Updated Successfully');
		
		}
		else
		{
		return redirect()->route('login')->withErrors('please login first');	
		}
		 
		 
		 
		 
		 
	 }
	 
	public function adminDashboard(Request $request)
	
	{
		$created_at = Carbon::Now();
    if(Session::has('cityadmin'))
		{
		 $users = DB::table('users')->where('block',0)->where('created_at',Carbon::today())->count();
		  $orders = DB::table('orders')->where('payment_status','Paid')->where('created_at',Carbon::today())->count();
		  $cancelledorders = DB::table('orders')->where('status',3)->where('updated_at',Carbon::today())->count();
		  $totalamount =DB::table('orders')
->selectRaw('sum(total_price) as total')
->where('created_at',Carbon::today())->get();
$bookingpending = DB::table('orders')->select('orders.*','vendor.*','areas.*','users.id as userid','users.user_phone','users.name','users.email','users.address','users.aboutme','orders.Bookid')->join('vendor', 'orders.vendor_id', '=','vendor.vendor_id')->join('users', 'orders.user_id', '=','users.id')->join('areas', 'vendor.area', '=','areas.Area_id')->where('orders.status',1)->orderBy('service_date', 'desc')->take(5)->get();

$vendors = DB::table('vendor')->join('areas', 'vendor.area', '=','areas.Area_id')->join('vendortypes', 'vendor.shop_type', '=','vendortypes.Vendor_Type')->take(5)->get();
 $totalusers = DB::table('users')->count();
 $totalvendors = DB::table('vendor')->count();
 $totalbooking = DB::table('orders')->where('orders.payment_status','Paid')->count();
 $totalordersum = DB::table('orders')->select(
            DB::raw('sum(total_price) as sums'))->where('orders.payment_status','Paid')->get();
 $orders1 = DB::table('orders')->select(
            DB::raw('sum(total_price) as sums'), 
            DB::raw("DATE_FORMAT(service_date,'%m') as monthKey")
  )
  ->whereYear('service_date', date('Y'))
  ->groupBy('service_date')
  ->orderBy('service_date', 'desc')
  ->get();
$ordertotal = [0,0,0,0,0,0,0,0,0,0,0,0];

foreach($orders1 as $order){
    $ordertotal[$order->monthKey-1] = $order->sums;
}  
$max=max($ordertotal);
$ordertotals=implode(",",$ordertotal);


		return view('admin.dashboard',compact('ordertotals','max','users','orders','cancelledorders','totalamount','bookingpending','vendors','totalusers','totalvendors','totalbooking','totalordersum'));
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
	
		}
	}
	
	public function smtpSettings()
	{
	if(Session::has('cityadmin'))
		{
		 $smtp = DB::table('smtp_settings')
    	                   ->where('SMTP_ID',1)
    	                   ->first();
		return view('admin.smtp',compact('smtp'));
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
		}	
	}
	
	public function amenetise()
	{
	if(Session::has('cityadmin'))
		{
		
		return view('admin.add-amenetis');
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
		}	
	}
	
	
	public function listamenetise()
	{
	if(Session::has('cityadmin'))
		{
		$ameneties = DB::table('ameneties')->get();
    	                  
    	                   
		return view('admin.list-amenetis',compact('ameneties'));
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
		}	
	}
	
	
	 public function Saveamenetise(Request $request)
	
	{
		$created_at = Carbon::Now();
   if(Session::has('cityadmin'))
		{
		$AmentiesName=$request->AmentiesName;
		$AmentiesName_ar=$request->AmentiesName_ar;
		$AmentiesImage=$request->AmentiesImage;
			$this->validate($request,[
         		'AmentiesName'=>'required',
				'AmentiesName_ar'=>'required',
				'AmentiesImage'=>'required'
				
         		
         		
         ]

	);
	$date = date('d-m-Y');
	if($request->AmentiesImage){
           $AmentiesImage=rtrim($AmentiesImage,",");
        }
		
		
			$insert = DB::table('ameneties')
    				->insert(['AmentiesImage'=>$AmentiesImage,'AmentiesName'=>$AmentiesName,'AmentiesName_ar'=>$AmentiesName_ar]);
					 if($insert){

        return redirect()->back()->with('message', 'Ameneties Added Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }
	}
	}
	public function editamenetise(Request $request)
	{
	if(Session::has('cityadmin'))
		{
		$ameneties = DB::table('ameneties')
    	                   ->where('Am_ID',$request->id)
    	                   ->first();
		return view('admin.edit-amenetis',compact('ameneties'));
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
		}	
	}
	
	
	
	public function editsaveamenties(Request $request)
	{
	if(Session::has('cityadmin'))
		{
		$AmentiesName=$request->AmentiesName;
		$AmentiesName_ar=$request->AmentiesName_ar;
		$AmentiesImage=$request->AmentiesImage;
			$this->validate($request,[
         		'AmentiesName'=>'required',
				'AmentiesName_ar'=>'required',
				'AmentiesImage'=>'required'
				
         		
         		
         ]

	);
	if($request->AmentiesImage){
           $AmentiesImage=rtrim($AmentiesImage,",");
        }
		$update = DB::table('ameneties')->where('Am_ID',$request->id)
    				->update(['AmentiesImage'=>$AmentiesImage,'AmentiesName'=>$AmentiesName,'AmentiesName_ar'=>$AmentiesName_ar]);
					 if($update){

        return redirect()->back()->with('message', 'Ameneties Updated Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
		}	
	}
	
	public function DeleteAmeneties(Request $request)
	{
		
		 if(Session::has('cityadmin'))
     {   
    
       

        

       

    	$delete=DB::table('ameneties')->where('Am_ID',$request->id)->delete();
        if($delete)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Delete' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }
		
		
	}
	
	public function smtpSettings_save(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$type=$request->type;
			$mail_host=$request->mail_host;
			$mail_port=$request->mail_port;
			$mail_username=$request->mail_username;
			$mail_password=$request->mail_password;
			$mail_encryption=$request->mail_encryption;
			$mail_from_address=$request->mail_from_address;
			$mail_from_name=$request->mail_from_name;

    	$this->validate(
         $request,
         [
         		'type'=>'required',
         		'mail_host'=>'required',
				'mail_port'=>'required',
         		'mail_username'=>'required',
				'mail_password'=>'required',
         		'mail_host'=>'required',
				'mail_encryption'=>'required',
         		'mail_from_address'=>'required',
				'mail_from_name'=>'required',
         		
         ],
         [

         	'type.required'=>'Enter Mail Type',
         	'mail_host.required'=>'Enter Host Name',
			'mail_port.required'=>'Enter Port No.',
         	'mail_username.required'=>'Enter User Name',
			'mail_password.required'=>'Enter Mail Password',
         	'mail_encryption.required'=>'Enter Encryption',
			'mail_from_address.required'=>'Enter Mail From',
         	'mail_from_name.required'=>'Enter From Name',
         ]

);
$smtp= DB::table('smtp_settings')->update(['type'=>$type,'mail_host'=>$mail_host,'mail_port'=>$mail_port,'mail_username'=>$mail_username,'mail_password'=>$mail_password,'mail_encryption'=>$mail_encryption,'mail_from_address'=>$mail_from_address,'mail_from_name'=>$mail_from_name]);
		  if($smtp){

        return redirect()->back()->with('message', 'Updated Successfully');
     }
     else{
         return redirect()->back()->withErrors('Nothing to Update');
     }
		}
	else {
		return redirect()->route('login')->withErrors('please login first');
		}	
	}
	
	public function governorate(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$governorates = DB::table('governarate')->get();
    	                   
    	                   
		return view('admin.governarate',compact('governorates'));	
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	
	public function governorate_save(Request $request)
	{
	if(Session::has('cityadmin'))
		{
		$Gov_Title=$request->Gov_Title;
			$this->validate(
         $request,
         [
         		'Gov_Title'=>'required',
         		
         		
         ],
         [

         	'Gov_Title.required'=>'Enter Name',
         
         ]

	);
			$insert = DB::table('governarate')
    				->insert(['Gov_Title'=>$Gov_Title, 'enabled'=>1]);
					 if($insert){

        return redirect()->back()->with('message', 'Added Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }	
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	public function Editgovernorate(Request $request)
	{
	if(Session::has('cityadmin'))
		{
		$governorates = DB::table('governarate')->get();
		$governarates1 = DB::table('governarate')
    	                   ->where('Gov_ID',$request->id)
    	                   ->first();
return view('admin.editgovernarate',compact('governorates','governarates1'));						   
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}

public function Updategovernorate(Request $request)
	{
	if(Session::has('cityadmin'))
		{
		$Gov_Title=$request->Gov_Title;
			$this->validate(
         $request,
         [
         		'Gov_Title'=>'required',
         		
         		
         ],
         [

         	'Gov_Title.required'=>'Enter Name',
         
         ]

	);	
$update = DB::table('governarate')
                                ->where('Gov_ID', $request->id)
                                ->update(['Gov_Title'=>$Gov_Title]);

		if($update)
			{
			return redirect()->back()->with('message', 'Updated Successfully');
			}	
		else
			{
			return redirect()->back()->withErrors('Nothing to Update');
			}
		}
		else {
			return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	public function Deletegovernorate(Request $request)
	{
		
		 if(Session::has('cityadmin'))
     {   
    
        $Gov_ID=$request->id;

        $getfile=DB::table('governarate')
                ->where('Gov_ID',$request->id)
                ->first();

       

    	$delete=DB::table('governarate')->where('Gov_ID',$request->id)->delete();
        if($delete)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Delete' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }
		
		
	}
	
	
	public function Enablegovernorate(Request $request)
	{
		
		 if(Session::has('cityadmin'))
     {   
    
        $Gov_ID=$request->id;

        $getfile=DB::table('governarate')
                ->where('Gov_ID',$request->id)
                ->first();
	
       if($getfile->enabled==1)
	   {
		   $enable=0;
	   }
	    if($getfile->enabled==0)
	   {
		   $enable=1;
	   }

    	$update = DB::table('governarate')
                                ->where('Gov_ID', $request->id)
                                ->update(['enabled'=>$enable]);
        if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Enable' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull Enable'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }
		
		
	}
	
	
	
	public function areas(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$areas = DB::table('areas')->join('governarate', 'governarate.Gov_ID', '=','areas.Gove_ID')->get();
    	    $governorates = DB::table('governarate')->get();             
    	                   
		return view('admin.areas',compact('governorates','areas'));	
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	
	public function area_save(Request $request)
	{
	if(Session::has('cityadmin'))
		{
		$Area_Title=$request->Area_Title;
		$Area_Title_ar=$request->Area_Title_ar;
		$governarate=$request->governarate;
			$this->validate(
         $request,
         [
         		'Area_Title'=>'required',
				'Area_Title_ar'=>'required',
         		'governarate'=>'required',
         		
         ],
         [

         	'Area_Title.required'=>'Enter Area',
			'Area_Title_ar.required'=>'Enter Area (Arabic)',
			'governarate.required'=>'Enter Governarate',
         
         ]

	);
			$insert = DB::table('areas')
    				->insert(['Area_Title'=>$Area_Title,'Area_Title_ar'=>$Area_Title_ar, 'Gove_ID'=>$governarate,'enable'=>1]);
					 if($insert){

        return redirect()->back()->with('message', 'Added Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }	
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	
	public function Editarea(Request $request)
	{
	if(Session::has('cityadmin'))
		{
		$areas = DB::table('areas')->join('governarate', 'governarate.Gov_ID', '=','areas.Gove_ID')->get();
    	    $governorates = DB::table('governarate')->get();             
    	    $areas1 = DB::table('areas')
    	                   ->where('Area_id',$request->id)
    	                   ->first();               
		return view('admin.editarea',compact('governorates','areas','areas1'));	
						   
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	
	public function Updatearea(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$Area_Title_ar		=$request->Area_Title_ar;
		$Area_Title=$request->Area_Title;
		$governarate=$request->governarate;
			$this->validate(
         $request,
         [		'Area_Title_ar'=>'required',
         		'Area_Title'=>'required',
         		'governarate'=>'required',
         		
         ],
         [
			'Area_Title_ar.required'=>'Enter Area (Arabic)',
         	'Area_Title.required'=>'Enter Area',
			'governarate.required'=>'Enter Governarate',
         
         ]

	);
$update = DB::table('areas')
                                ->where('Area_id', $request->id)
                                ->update(['Area_Title'=>$Area_Title,'Area_Title_ar'=>$Area_Title_ar,'Gove_ID'=>$governarate]);

		if($update)
			{
			return redirect()->back()->with('message', 'Updated Successfully');
			}	
		else
			{
			return redirect()->back()->withErrors('Nothing to Update');
			}
		}
		else {
			return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	
	public function Deletearea(Request $request)
	{
		
		 if(Session::has('cityadmin'))
     {   
    
       

        $getfile=DB::table('areas')
                ->where('Area_id',$request->id)
                ->first();

       

    	$delete=DB::table('areas')->where('Area_id',$request->id)->delete();
        if($delete)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Delete' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }
		
		
	}
	
	public function Enablearea(Request $request)
	{
		
		 if(Session::has('cityadmin'))
     {   
    
        $Gov_ID=$request->id;

        $getfile=DB::table('areas')
                ->where('Area_id',$request->id)
                ->first();
	
       if($getfile->enable==1)
	   {
		   $enable=0;
	   }
	    if($getfile->enable==0)
	   {
		   $enable=1;
	   }

    	$update = DB::table('areas')
                                ->where('Area_id', $request->id)
                                ->update(['enable'=>$enable]);
        if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Enable' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull Enable'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }
		
		
	}
	public function Faqs(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$faqs  = DB::table('faqs')
                ->get();
    	                   
    	                   
		return view('admin.faqs',compact('faqs'));	
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	public function FaqEdit(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$faqs  = DB::table('faqs')
                ->get();
			$faq1 = DB::table('faqs')
    	                   ->where('Faq_ID',$request->id)
    	                   ->first();
		return view('admin.faqs-edit',compact('faqs','faq1'));	
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	public function FaqEditSave(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			 $Question=$request->Question;
				  $Answer=$request->Answer;
				  $Question_ar=$request->Question_ar;
				  $Answer_ar=$request->Answer_ar;
    	    $this->validate(
         $request,
         [
         		'Question'=>'required',
				'Answer'=>'required',
				'Question_ar'=>'required',
				'Answer_ar'=>'required'
				
         		
         		
         ]
         

	);     

	
			 $update = DB::table('faqs')->where('Faq_ID', $request->id)
    				->update(['Question'=>$Question, 'Answer'=>$Answer,'Question_ar'=>$Question_ar, 'Answer_ar'=>$Answer_ar]);
    				
					 if($update){

        return redirect()->back()->with('message', 'Faq Updated Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }	                 	
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	public function faqDelete(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			
    	                   
    	   $delete=DB::table('faqs')->where('Faq_ID',$request->id)->delete();
        if($delete)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Delete' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull delete'); 
        }                
		
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	public function faqSave(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$faqs  = DB::table('faqs')
                ->get();
				 $Question=$request->Question;
				  $Answer=$request->Answer;
				  $Question_ar=$request->Question_ar;
				  $Answer_ar=$request->Answer_ar;
    	    $this->validate(
         $request,
         [
         		'Question'=>'required',
				'Answer'=>'required',
				'Question_ar'=>'required',
				'Answer_ar'=>'required'
				
         		
         		
         ]
         

	);               
    	 $insert = DB::table('faqs')
    				->insert(['Question'=>$Question, 'Answer'=>$Answer,'Question_ar'=>$Question_ar, 'Answer_ar'=>$Answer_ar]);
					 if($insert){

        return redirect()->back()->with('message', 'New Faq Added Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }	                  
		
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	public function contactus(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$contactus  = DB::table('contactus')->where('Contact_ID',1)
                ->first();
    	                   
    	                   
		return view('admin.contactus',compact('contactus'));	
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	public function contactusSave(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$customer_service=$request->customer_service;
			$whatsapp=$request->whatsapp;
			$website=$request->website;
			$facebook=$request->facebook;
			$instagram=$request->instagram;
			$this->validate(
         $request,
         [
         		'customer_service'=>'required',
				'whatsapp'=>'required',
				'website'=>'required',
				'facebook'=>'required',
				'instagram'=>'required',
         		
         		
         ]
         

	);
    	    $update = DB::table('contactus')
                                ->where('Contact_ID', 1)
                                ->update(['customer_service'=>$customer_service,'whatsapp'=>$whatsapp,'website'=>$website,'facebook'=>$facebook,'instagram'=>$instagram]);               
    	                   
		if($update)
			{
			return redirect()->back()->with('message', 'Updated Successfully');
			}	
		else
			{
			return redirect()->back()->withErrors('Nothing to Update');
			}
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	public function privacypolicy(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$privacy = DB::table('privacypolicy')->where('Privacy_ID',1)
                ->first();
    	                   
    	                   
		return view('admin.privacy-policy',compact('privacy'));	
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	public function privacypolicySave(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$Description=$request->Description;
			$Description_ar=$request->Description_ar;
			$this->validate(
         $request,
         [
         		'Description'=>'required',
				'Description_ar'=>'required',
         		
         		
         ],
         [

         	'Description.required'=>'Enter Content',
         
         ]

	);
    	    $update = DB::table('privacypolicy')
                                ->where('Privacy_ID', 1)
                                ->update(['Description'=>$Description,'Description_ar'=>$Description_ar]);               
    	                   
		if($update)
			{
			return redirect()->back()->with('message', 'Updated Successfully');
			}	
		else
			{
			return redirect()->back()->withErrors('Nothing to Update');
			}
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	
	public function terms(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$terms = DB::table('terms')->where('Terms_ID',1)
                ->first();
    	                   
    	                   
		return view('admin.terms',compact('terms'));	
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	public function termsSave(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$Description=$request->Description;
			$Description_ar=$request->Description_ar;
			$this->validate(
         $request,
         [
         		'Description'=>'required',
         		'Description_ar'=>'required'
         		
         ],
         [

         	'Description.required'=>'Enter Content',
         
         ]

	);
    	    $update = DB::table('terms')
                                ->where('Terms_ID', 1)
                                ->update(['Description'=>$Description,'Description_ar'=>$Description_ar]);               
    	                   
		if($update)
			{
			return redirect()->back()->with('message', 'Updated Successfully');
			}	
		else
			{
			return redirect()->back()->withErrors('Nothing to Update');
			}
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	
	public function generalSettings(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$settings = DB::table('generalsettings')->where('Settings_ID',1)->first();
               
    	                   
    	                   
		return view('admin.general-settings',compact('settings'));	
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	
	public function generalSettingsSave(Request $request)
	{
	if(Session::has('cityadmin'))
		{
			$this->validate($request,[
		   'Name' => 'required',
		   'LogoWhite' => 'required|image:jpeg,png,jpg,gif,svg|max:2048',
		   'LogoBlack' => 'required|image:jpeg,png,jpg,gif,svg|max:2048',
		   'TimeZone' => 'required',
		   'Page_Background' => 'required|image:jpeg,png,jpg,gif,svg|max:2048',
		   'Refer' => 'required',
		   'Minimum_Refer' => 'required',
		   'Earn_Bonus' => 'required',
		   'Refer_EarnMethod' => 'required',
		   'MaximumRefer_Earn' => 'required',
		  'SetOrder_Amount' => 'required',
		  'SetOrder_Currency' => 'required',
		    'SetTime_Cancel' => 'required',
			  'Settime_Intervel' => 'required'

	   ]);
	   $settings = DB::table('generalsettings')->where('Settings_ID',1)
                ->first();
	    $Name=$request->Name;
		 $TimeZone=$request->TimeZone;
		  $Refer=$request->Refer;
		   $Minimum_Refer=$request->Minimum_Refer;
		   $Earn_Bonus=$request->Earn_Bonus;
		    $Refer_EarnMethod=$request->Refer_EarnMethod;
			 $MaximumRefer_Earn=$request->MaximumRefer_Earn;
			 $SetOrder_Amount=$request->SetOrder_Amount;
			 $SetOrder_Currency=$request->SetOrder_Currency;
			  $SetTime_Cancel=$request->SetTime_Cancel;
			  $Settime_Intervel=$request->Settime_Intervel;
			  
			   $date = date('d-m-Y');
        $created_at=date('d-m-Y h:i a');
        if($request->LogoWhite){
            $LogoWhite = $request->LogoWhite;
            $fileName = date('dmyhisa').'-'.$LogoWhite->getClientOriginalName();
            $fileName = str_replace(" ", "-", $fileName);
            $LogoWhite->move('settings/images/'.$date.'/', $fileName);
            $LogoWhite = 'settings/images/'.$date.'/'.$fileName;
        }
		else
		{
		$LogoWhite=	$settings->LogoWhite;
		}
		if($request->LogoBlack){
		
		    $LogoBlack = $request->LogoBlack;
            $fileName = date('dmyhisa').'-'.$LogoBlack->getClientOriginalName();
            $fileName = str_replace(" ", "-", $fileName);
            $LogoBlack->move('settings/images/'.$date.'/', $fileName);
            $LogoBlack = 'settings/images/'.$date.'/'.$fileName;
        }
		else
		{
		$LogoBlack=	$settings->LogoBlack;
		}
		
		
		if($request->Page_Background){
		
		    $Page_Background = $request->Page_Background;
            $fileName = date('dmyhisa').'-'.$Page_Background->getClientOriginalName();
            $fileName = str_replace(" ", "-", $fileName);
            $Page_Background->move('settings/images/'.$date.'/', $fileName);
            $Page_Background = 'settings/images/'.$date.'/'.$fileName;
        }
		else
		{
		$Page_Background=$settings->Page_Background;
		}
		
		$update = DB::table('generalsettings')
                                ->where('Settings_ID', 1)
                                ->update(['Name'=>$Name,'LogoWhite'=>$LogoWhite,'LogoBlack'=>$LogoBlack,'TimeZone'=>$TimeZone,'Page_Background'=>$Page_Background,'Refer'=>$Refer,'Minimum_Refer'=>$Minimum_Refer,'Earn_Bonus'=>$Earn_Bonus,'Refer_EarnMethod'=>$Refer_EarnMethod,'MaximumRefer_Earn'=>$MaximumRefer_Earn,'SetOrder_Amount'=>$SetOrder_Amount,'SetOrder_Currency'=>$SetOrder_Currency,'SetTime_Cancel'=>$SetTime_Cancel,'Settime_Intervel'=>$Settime_Intervel]); 
			if($update)
			{
			return redirect()->back()->with('message', 'Updated Successfully');
			}	
		else
			{
			return redirect()->back()->withErrors('Nothing to Update');
			}
		
		}
		else {
		return redirect()->route('login')->withErrors('please login first');
		}
		
	}
	
	public function AddAdminUser(Request $request)
	{
	if(Session::has('cityadmin'))
	{	
	
	return view('admin.addadminuser');	
	
	
	}

	else
	{
	return redirect()->route('login')->withErrors('please login first');
	}
	}
	
	
	public function ListAdminUsers(Request $request)
	{
	if(Session::has('cityadmin'))
	{	
	$users = DB::table('admin')->get();
	return view('admin.listadminusers',compact('users'));	
	
	
	}

	else
	{
	return redirect()->route('login')->withErrors('please login first');
	}
	}
	
	
	public function EditAdminUser(Request $request)
	{
	if(Session::has('cityadmin'))
	{	
	$user = DB::table('admin')->where('id', $request->id)->first();
	return view('admin.editadminuser',compact('user'));	
	
	
	}

	else
	{
	return redirect()->route('login')->withErrors('please login first');
	}
	}
	
	public function PermissionAdminUser(Request $request)
	{
	if(Session::has('cityadmin'))
	{	
	$user = DB::table('admin')->where('id', $request->id)->first();
	return view('admin.permissionadminuser',compact('user'));	
	
	
	}

	else
	{
	return redirect()->route('login')->withErrors('please login first');
	}
	}
	
	public function SaveAddAdminUser(Request $request)
	{
	if(Session::has('cityadmin'))
	{	
	
		$this->validate($request,[
		   'name' => 'required',
		   'email' => 'required|email|unique:admin,admin_email',
		   'phone' => 'required',
		   'password' => 'required|min:6',
			'photo'=>'required',

	   ]);
			$name=$request->name;
			$email=$request->email;
			$phone=$request->phone;
			$password=$request->password;
			$photo=$request->photo;
			$date = date('d-m-Y');
        $created_at=date('d-m-Y h:i a');
        if($photo){
            
            $photo = rtrim($photo,",");
        }
		$new_pass=Hash::make($password);
		$insert = DB::table('admin')
    				->insert(['admin_name'=>$name, 'admin_image'=>$photo,'admin_phone'=>$phone,'admin_email'=>$email,'admin_pass'=>$new_pass]);
					 if($insert){

        return redirect()->back()->with('message', 'New Staff Added Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Add');
     }	
	
	}

	else
	{
	return redirect()->route('login')->withErrors('please login first');
	}
	}
	
	
	
	
	public function UpdateAdminUser(Request $request)
	{
	if(Session::has('cityadmin'))
	{	
	
		$this->validate($request,[
		   'name' => 'required',
		   'email' => 'required|email',
		   'phone' => 'required'

	   ]);
			$name=$request->name;
			$email=$request->email;
			$phone=$request->phone;
			$password=$request->password;
			$photo=$request->photo;
			$date = date('d-m-Y');
        $created_at=date('d-m-Y h:i a');
        if($photo){
            
            
            
            $photo = rtrim($photo,",");
     
			$updates = DB::table('admin')->where('id', $request->id)
    				->update([ 'admin_image'=>$photo]);
        }
		if($password!='')
		{
			
		$new_pass=Hash::make($password);
		$updated = DB::table('admin')->where('id', $request->id)
    				->update(['admin_pass'=>$new_pass]);
		}
		$updates = DB::table('admin')->where('id', $request->id)
    				->update(['admin_name'=>$name,'admin_phone'=>$phone,'admin_email'=>$email]);
					 if($updates){

        return redirect()->back()->with('message', 'Staff Updated Successfully');
     }
		else{
         return redirect()->back()->withErrors('Nothing to Update');
     }	
	
	}

	else
	{
	return redirect()->route('login')->withErrors('please login first');
	}
	}
	
	public function PermissionUpdateAdminUser(Request $request)
	{
	 if(Session::has('cityadmin'))
     {   
    
        $id=$request->id;
		$field=$request->field;
        $getfile=DB::table('admin')
                ->where('id',$request->id)
                ->first();
	
       if($getfile->$field==1)
	   {
		   $enable=0;
	   }
	    if($getfile->$field==0)
	   {
		   $enable=1;
	   }

    	$update = DB::table('admin')
                                ->where('id', $request->id)
                                ->update([$field=>$enable]);
        if($update)
        {
        
                 
        //return redirect()->back()->with('message', 'Deleted Successfully');
		return response()->json(['Enable' => 'success']);
        }
        else
        {
           return redirect()->back()->withErrors('unsuccessfull Enable'); 
        }

      }
    else
      {
        return redirect()->route('cityadminlogin')->withErrors($this->login_message);
      }	
		
		
		
		
	}
	
	public function Uploadfiles(Request $request)
	{
	 if(Session::has('cityadmin'))
     {   
			$this->validate($request,[
		 
			'files.*'=>'required|image:jpeg,png,jpg,gif,svg|max:2048',

	   ]);
	  
	   $images='';
	   if ($request->file('files')){
            foreach($request->file('files') as $key => $file)
            {
                $fileName = time().rand(1,99).'.'.$file->extension();  
                $file->move('uploads/', $fileName);
                $photos='uploads/'.$fileName;
				
				
								


				
            }
        }
	   $i=1;
	   $html="";
									if ($handle = opendir(public_path('uploads'))) {

								while (false !== ($entry = readdir($handle))) {
								if ($entry != "." && $entry != "..") { 
										$html.='<div class="col-xl-3 col-xxl-3 col-md-4 col-sm-6">
											<div class="card">
												<div class="card-body product-grid-card">
													<div class="new-arrival-product">
														<div class="new-arrivals-img-contnent">
															<div class="form-check custom-checkbox mb-3 product-check">
																<input type="checkbox" name="images[]" class="form-check-input" value="uploads/'.$entry.'" id="customCheckBox'.$i.'" >';
																$allowedMimeTypes = ['image/jpeg','image/gif','image/png','image/bmp','image/svg+xml','image/webp'];
																
																$contentType = mime_content_type('uploads/'.$entry);
																
																
															$html.='</div>';
															 if(in_array($contentType, $allowedMimeTypes) ){ 
															$html.='<img class="img-fluid rounded" src="'.asset('/').'uploads/'.$entry.'" alt="">';
															} else { 
															$html.='<a href="'.asset('/').'uploads/'.$entry.'">File</a>';
															 }
														$html.='</div>
														<div class="new-arrival-content text-center mt-3">
															<h4>'.$file=str_replace("-", " ", $entry);preg_replace('/\.\w+$/', '', $file).'</h4>';
															$size = filesize('uploads/'.$entry);
															//$html.='<span class="price">'.$units = array('B', 'KB', 'MB', 'GB', 'TB');
    //$formattedSize = $size; for ($i = 0; $size >= 1024 && $i < count($units) - 1; $i++) {
       // $size /= 1024;
        //$formattedSize = round($size, 2);
    //} 
	
	//$formattedSize . ' ' . $units[$i];
	
	//$html.='</span>
														$html.='</div>
													</div>
												</div>
											</div>
										</div>';
										
										$i++;
										   }
    }
    closedir($handle);
}
	   return response()->json(['html' => $html,'type'=> 'done']);
	
	 }
	 else
	 {
		return redirect()->route('cityadminlogin')->withErrors($this->login_message); 
	 }
	}
	
	
	public function ViewUploadfiles(Request $request)
	{
	 if(Session::has('cityadmin'))
     {   
 return view('admin.viewuploadedfiles');
	 }
	else
	 {
		return redirect()->route('cityadminlogin')->withErrors($this->login_message); 
	 }
	}
	
		public function ViewfileDelete(Request $request)
	{
	 if(Session::has('cityadmin'))
     {   
		
		$files=$request->files1;
		$file=explode(",",$files);
		foreach($file as $fil)
		{
			File::delete(public_path($fil));
			
		}
		return response()->json(['Delete' => 'success']);
	 }
	else
	 {
		return redirect()->route('cityadminlogin')->withErrors($this->login_message); 
	 }
	}
	
	public function expirynotification()
	{
	$monthAgo = date("Y-m-d",strtotime('+1 month'));	
	$vendors   = DB::table('vendor')
                                ->where('contractenddate', $monthAgo)->get();
	$adminemail   = DB::table('admin')
                                ->where('id', 1)->first();
								$adminsendemail=$adminemail->admin_email;
		foreach($vendors as $vendor)
		{
			$name=$vendor->vendor_name;
			$phone=$vendor->vendor_phone;
			$email=$vendor->vendor_email;
			$date=$vendor->contractenddate;
			$datenew=date('d-m-Y',strtotime($date));
			$body = [
            'name'=>$name,
            'phone'=>$phone,
           'email'=>$email,
		   'date'=>$datenew
		   
		   
        ];
 $app_name = "BookMe";
        $settings=DB::table('smtp_settings')
    	                   ->where('SMTP_ID',1)
    	                   ->first();
						   
						   $config = array(
        'driver'     => $settings->type,
        'host'       => $settings->mail_host,
        'port'       => $settings->mail_port,
        'from'       => array('address' =>$settings->mail_from_address, 'name' =>$settings->mail_from_name ),
        'encryption' => $settings->mail_encryption,
        'username'   => $settings->mail_username,
        'password'   => $settings->mail_password
      );
      Config::set('mail', $config);
        Mail::to('<EMAIL>')->send(new ExpiryMail($body));
		}
	}
}
