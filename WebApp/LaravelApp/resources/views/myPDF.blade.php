<!DOCTYPE html>
<html>
<head>
    <title>Receipt Bookme</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
</head>
<body>
    <div class="content-body default-height">
            <div class="container-fluid">
				
                <div class="row">
                    <div class="col-lg-12">

                        <div class="card mt-3">
                            <div class="card-header"> Invoice <strong>{{$bookings->Bookid}}</strong> <span class="float-end">
                                    <strong>Status:</strong> @if($bookings->status==1) Pending @endif @if($bookings->status==2) Completed @endif @if($bookings->status==3) Cancelled @endif @if($bookings->status==6) Failed @endif</span> </div>
                            <div class="card-body">
                                <div class="row mb-5">
                                    <div class="mt-4 col-xl-3 col-lg-3 col-md-6 col-sm-12">
                                        <h6>From:</h6>
                                        <div> <strong>{{$bookings->vendor_name}}</strong> </div>
                                        <div>{{$bookings->Area_Title}}</div>
                                        <div>{{$bookings->block}} {{$bookings->street}} {{$bookings->avenue}} {{$bookings->zipcode}}</div>
                                        <div>Email: {{$bookings->vendor_email}}</div>
                                        <div>Phone: {{$bookings->vendor_phone}}</div>
                                    </div>
                                    <div class="mt-4 col-xl-3 col-lg-3 col-md-6 col-sm-12">
                                        <h6>To:</h6>
                                        <div> <strong>{{$bookings->name}}</strong> </div>
                                     
                                        <div>{{$bookings->address}}</div>
                                        <div>Email: {{$bookings->email}}</div>
                                        <div>Phone: {{$bookings->user_phone}}</div>
                                    </div>
                                    <div class="mt-4 col-xl-6 col-lg-6 col-md-12 col-sm-12 d-flex justify-content-lg-end justify-content-md-center justify-content-xs-start">
                                        <div class="row align-items-center">
											<div class="col-sm-9"> 
												<div class="brand-logo mb-3">
													<img class="logo-abbr me-2" width="50" src="images/logo.png" alt="">
													<img class="logo-compact" width="110" src="images/logo-text.png" alt="">
												</div>
                                                
                                            </div>
                                            
                                        </div>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th class="center">#</th>
                                                <th>Services Name</th>
                                             
                                                <th class="right">Price</th>
                                                <th class="center">Qty</th>
                                                <th class="right">Total</th>
												<th class="right">Re Assign</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										@foreach($bookingdetails  as $key => $book)
                                            <tr>
                                                <td class="center">{{$key+1}}</td>
                                                <td class="left strong">{{$book->service_name}}</td>
                                               
                                                <td class="right">KD {{$book->price}}</td>
                                                <td class="center">1</td>
                                                <td class="right">KD {{$book->price}}</td>
												<td class="right">
									@foreach($staffs as $staff)
									@if($staff->staff_id==$book->staff_id) {{$staff->staff_name}} @endif
									@endforeach
								</select>		</td>
                                            </tr>
											@endforeach
										
                                        </tbody>
                                    </table>
                                </div>
                               
                                        <table class="table table-clear">
                                            <tbody>
                                                <tr>
                                                    <td class="left"><strong class="text-black">Subtotal</strong></td>
                                                    <td class="right">KD {{$bookings->total_price}}</td>
                                                </tr>
                                               
                                               
                                                <tr>
                                                    <td class="left"><strong class="text-black">Total</strong></td>
                                                    <td class="right"><strong class="text-black">KD {{$bookings->total_price}}</strong>
                                                      </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                 
                        </div>
                    </div>
                </div>
            </div>
        </div>
  
</body>
</html>
 



