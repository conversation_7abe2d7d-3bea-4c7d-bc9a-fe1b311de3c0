@extends('staff.layout.master')

@section('content')

  <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title"> Employee</h4>
                            </div>
                            <div class="card-body">
                                <!-- Nav tabs -->
                                <div class="custom-tab-1">
                                    <ul class="nav nav-tabs mb-3">
                                        <li class="nav-item">
                                            <a class="nav-link active" data-bs-toggle="tab" href="#home1"><i class="la la-user me-2"></i>Basic Info</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" data-bs-toggle="tab" href="#profile1"><i class="la la-clock me-2"></i> Info</a>
                                        </li>
                                      
                                    </ul>
                                    <div class="tab-content">
                                        <div class="tab-pane fade show active" id="home1" role="tabpanel">
                                            <div class="pt-4">
                                               
                                                <p>
                                                   <strong> Phone Number </strong><br>
                                                   {{$staffdetail->phone}}
                                                </p>

                                                <p>
                                                    <strong> Service From </strong><br>
                                                  {{$staffdetail->giveservices}}
                                                 </p>
                                                    
                                                <div class="profile-personal-info">
                                                    <h4 class="text-primary mb-4">Working Days</h4>
													
                                                    <div class="row mb-2">
                                                        <div class="col-sm-3 col-5">
                                                            <h5 class="f-w-500">Sunday <span class="pull-right">:</span>
                                                            </h5>
                                                        </div>
                                                        <div class="col-sm-9 col-7"><span>@if($timeslots[0]->status==1) @if($timeslots[0]->days=='Sunday') {{$timeslots[0]->open_hour}} To {{$timeslots[0]->close_hour}} @endif @endif</span>
                                                        </div>
                                                    </div>
													
                                                    <div class="row mb-2">
                                                        <div class="col-sm-3 col-5">
                                                            <h5 class="f-w-500">Monday <span class="pull-right">:</span>
                                                            </h5>
                                                        </div>
                                                        <div class="col-sm-9 col-7"><span>@if($timeslots[1]->status==1) @if($timeslots[1]->days=='Monday') {{$timeslots[1]->open_hour}} To {{$timeslots[1]->close_hour}} @endif @endif</span>
                                                        </div>
                                                    </div>
                                                    <div class="row mb-2">
                                                        <div class="col-sm-3 col-5">
                                                            <h5 class="f-w-500">Tuesday <span class="pull-right">:</span></h5>
                                                        </div>
                                                        <div class="col-sm-9 col-7"><span>@if($timeslots[2]->status==1) @if($timeslots[2]->days=='Tuesday') {{$timeslots[2]->open_hour}} To {{$timeslots[2]->close_hour}} @endif @endif</span>
                                                        </div>
                                                    </div>
                                                    <div class="row mb-2">
                                                        <div class="col-sm-3 col-5">
                                                            <h5 class="f-w-500">Wednesday <span class="pull-right">:</span>
                                                            </h5>
                                                        </div>
                                                        <div class="col-sm-9 col-7"><span>@if($timeslots[3]->status==1) @if($timeslots[3]->days=='Wednesday') {{$timeslots[3]->open_hour}} To {{$timeslots[3]->close_hour}} @endif @endif</span>
                                                        </div>
                                                    </div>
                                                    <div class="row mb-2">
                                                        <div class="col-sm-3 col-5">
                                                            <h5 class="f-w-500">Thursday <span class="pull-right">:</span></h5>
                                                        </div>
                                                        <div class="col-sm-9 col-7"><span>@if($timeslots[4]->status==1) @if($timeslots[4]->days=='Thursday') {{$timeslots[4]->open_hour}} To {{$timeslots[4]->close_hour}} @endif @endif</span>
                                                        </div>
                                                    </div>
                                                    <div class="row mb-2">
                                                        <div class="col-sm-3 col-5">
                                                            <h5 class="f-w-500">Friday <span class="pull-right">:</span></h5>
                                                        </div>
                                                        <div class="col-sm-9 col-7"><span>@if($timeslots[5]->status==1) @if($timeslots[5]->days=='Friday') {{$timeslots[5]->open_hour}} To {{$timeslots[5]->close_hour}} @endif @endif</span>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-2">
                                                        <div class="col-sm-3 col-5">
                                                            <h5 class="f-w-500">Saturday <span class="pull-right">:</span></h5>
                                                        </div>
                                                        <div class="col-sm-9 col-7"><span>@if($timeslots[6]->status==1) @if($timeslots[6]->days=='Saturday') {{$timeslots[6]->open_hour}} To {{$timeslots[6]->close_hour}} @endif @endif</span>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="profile1">
                                            <div class="row">
                                                
                                                


                                                <div class="profile-personal-info">
                                                    <h4 class="text-primary mb-4">Assign Service</h4>
                                                   
                                                   
                                                    <ul class="list-icons">
													@foreach($staffservices as $staff)
                                                        <li> <span class="align-middle me-2"><i class="fa fa-check text-info"></i></span>
														{{$staff->Name}}
                                                        </li>
													@endforeach
                                                      

                                                        
                                                    </ul>
                                              
                                                 
                                               

                                                 
                                                </div>
                                           
                                            </div>
                                        </div>
                                        
                                    </div>
                                </div>
                            </div>

                          
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		
@endsection