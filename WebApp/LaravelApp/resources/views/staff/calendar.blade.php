@extends('staff.layout.master')

@section('content')

 <div class="content-body default-height">
            <div class="container-fluid">
                <div class="page-titles">
				<h2> Calendar</h2>
                </div>
                <!-- row -->


                 <div class="row">
                    <div class="col-xl-3 col-xxl-4">
                        <div class="card">
                            <div class="card-body">
                                <h4 class="card-intro-title">Calendar</h4>

                                <div class="">
                                    <div id="external-events" class="my-3">
                                        <p>Drag and drop your event or click in the calendar</p>
                                         <div class="external-event bg-green light" data-class="bg-primary"><i class="fa fa-move"></i><span>Completed</span></div>
                                        <div class="external-event bg-yellow light" data-class="bg-warning"><i class="fa fa-move"></i>Scheduled
                                        </div>
                                       
                                        <div class="external-event bg-red light" data-class="bg-danger"><i class="fa fa-move"></i>Cancelled</div>
                                        
                                      
                                    </div>
                                    <!-- checkbox -->
									
                                    <a href="javascript:void()" data-bs-toggle="modal" data-bs-target="#add-category" class="btn btn-primary btn-event w-100 mb-3">
                                        <span class="align-middle"><i class="ti-plus me-1"></i></span> Add Appointment
                                    </a>

                                    <a href="javascript:void()" data-bs-toggle="modal" data-bs-target="#event-modal" class="btn btn-primary btn-event w-100">
                                        <span class="align-middle"><i class="ti-plus me-1"></i></span> Add New Client
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-9 col-xxl-8">
					
                        <div class="card">
						<form id="calendar1" name="calendar1" method="get" action="{{route('staff-calendar')}}">
							<div class="card-header d-sm-flex d-block pb-0 border-0">
								<div class="me-auto pe-3">
									<h4 class="text-black fs-20 font-w600">Calendar</h4>
								
								</div>
								
									<div class="col-xl-4 col-lg-4  col-xxl-4">

									<select class="form-control default-select" id="sel4" name="staff">
										<option value="" selected="selected">Filter by Employes</option>
										@foreach($employees1 as $employee)
									<option value="{{$employee->staff_id}}">{{$employee->staff_name}}</option>
										@endforeach
										
										
									</select>
									
									
								</div>
								
							</div>
							</form>

                            <div class="card-body">
                                <div id="calendar" class="app-fullcalendar"></div>
                            </div>
                        </div>
                    </div>
                    <!-- BEGIN MODAL -->
                  <div class="modal fade none-border" id="event-modal">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title"><strong>Create Client</strong></h4>
				</div>
				<div class="modal-body">
					<form method="post" action="{{route('save-customer')}}">
					 {{csrf_field()}}
						<div class="row">
							

							<div class="col-md-6 mb-2">
								<label class="control-label form-label">First Name <span> *</span></label>
								<input class="form-control form-white" placeholder="First Name" name="firstname" type="text" required>
							</div>
							<div class="col-md-6 mb-2">
								<label class="control-label form-label">Last Name <span> *</span></label>
								<input class="form-control form-white" placeholder="Last Name" name="lastname" type="text" required>
							</div>
						  
							<div class="col-md-6 mb-2">
								<label class="control-label form-label">Email<span> *</span></label>
								<input class="form-control form-white" placeholder="Email" type="email" name="email" required>
							</div>
								<div class="col-md-6 mb-2">
								<label class="control-label form-label">Country Code<span> *</span></label>
								<input class="form-control form-white" placeholder="Country Code" readonly type="text" name="code" value="+965">
							</div>


							<div class="col-md-6 mb-2">
								<label class="control-label form-label">Phone no<span> *</span></label>
								<input class="form-control form-white" placeholder="Phone no." type="text" name="phone" required>
							</div>
							<div class="col-md-6 mb-2">
								<label class="control-label form-label">Gender<span> *</span></label>
								<select class="form-control form-white" name="gender" required>
								<option value="">Select</option>
								<option value="Male">Male</option>
								<option value="Female">Female</option>
								</select>
							</div>
							<div class="col-md-6 mb-2">
								<label class="control-label form-label">Address<span> *</span></label>
								<textarea class="form-control form-white" name="address" id="address" required></textarea>
							</div>
							<div class="col-md-6 mb-2">
								<label class="control-label form-label">Country<span> *</span></label>
								<input class="form-control form-white" placeholder="Country" readonly type="text" name="country" value="Kuwait">
							</div>

							<div class="col-md-6 mb-2">
								<label class="control-label form-label">Password<span> *</span></label>
								<input class="form-control form-white" placeholder="Password" type="password" name="password" required>
							</div>

						

						 

							
						</div>
					

				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default waves-effect" data-bs-dismiss="modal">Close</button>
					<button type="submit" class="btn btn-success save-event waves-effect waves-light">Create
						</button>

				  
				</div>
				</form>
			</div>
		</div>
	</div>
					
					
					<div class="modal fade none-border" id="add-category">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title"><strong>Add Appointment</strong></h4>
                     <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
              
                    <form method="post" enctype='multipart/form-data' action="{{route('staff-booking-save')}}" >
													{{csrf_field()}}
													  <div class="modal-body">
													
					 <div class="col-md-6 mb-2">
                                <label class="control-label form-label">Date <span> *</span></label>
                                <input id="date" required  name="date" required class="datepicker-default form-control" >
                            </div>
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <label class="control-label form-label">Employee <span> *</span></label>
                                  <select  required class="form-control default-select form-white" data-placeholder="Service At..." id="staff" name="staff">
                                     <option value="">Select</option>
									 @foreach($employees as $employee)
									<option value="{{$employee->staff_id}}">{{$employee->staff_name}}</option>
										@endforeach
                                    
                                </select>
                            </div>
                            <div class="col-md-6 mb-2">
                                <label class="control-label form-label">Service At <span> *</span></label>
                                <select name="serviceat" required class="form-control default-select form-white" data-placeholder="Service At..." name="category-color">
                                    <option value="">Select</option>
									<option>Salon</option>
                                    <option>Home</option>
                                    
                                </select>
                            </div>

                            <div class="col-md-6 mb-2">
                                <label class="control-label form-label">Clients <span> *</span></label>
                                <select required class="form-control default-select form-white" data-placeholder="Service At..." name="clients">
                                     <option value="">Select</option>
									 @foreach($users as $user)
									<option value="{{$user->id}}">{{$user->name}}</option>
										@endforeach
                                    
                                </select>
                            </div>

                          
                            <div class="col-md-6 mb-2">
                                <label class="control-label form-label">Services <span> *</span></label>
                                <select required multiple required name="services[]" class="form-control default-select" id="sel2">
                                     @foreach($vendor_services as $services)
									<option value="{{$services->ServicesID}}">{{$services->Name}}</option>
                                   @endforeach
                                   
                                </select>
                            </div>

                           

                            <div class="col-md-6 mb-2">
                                <label class="control-label form-label">Time <span> *</span></label>
                                <div class="input-group" id="booking">
                                  
                                </div>
                            </div>


                            <div class="col-md-6 mb-2">
                                <label class="control-label form-label">Payment Status <span> *</span></label>
                               <select  required name="status" class="form-control default-select" id="sel3">
                                    
									<option value="Paid">Paid</option>
                                 <option value="Not Paid">Not Paid</option>
                                   
                                </select>
                            </div>
							
							<div class="col-md-6 mb-2">
                                <label class="control-label form-label">Payment Method <span> *</span></label>
                               <select  required name="paymentmethod" class="form-control default-select" id="sel3">
                                    
									<option>Knet</option>
                                 <option>COD</option>
                                   
                                </select>
                            </div>

                           

                            
                        </div>
                    
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default btn-danger light waves-effect" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary  save-category">Book Appointment</button>
                </div>
				</form>
            </div>
        </div>
    </div>
					
					
					
					
                 
                </div>
            </div>
        </div>
		
@endsection
