@extends('staff.layout.master')

@section('content')

  <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-12">
                        <div class="filter cm-content-box box-primary">
                            <div class="content-title">
                                <div class="cpa">
                                    Change Password
                                </div>
                                <div class="tools">
                                    <a href="javascript:void(0);" class="expand SlideToolHeader"><i class="fal fa-angle-down"></i></a>
                                </div>
                            </div>
                            <div class="cm-content-body  form excerpt">
                                <div class="card-body">
                                <form id="changepassword" name="changepassword" method="post" action="{{route('staff-update-changepassword')}}">
								{{csrf_field()}}
													@if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif
                                    <div class="row">
                                        <div class="col-xl-12 col-sm-6">
                                           
                                              <div class="mb-3">
                                                <label  class="form-label">New Password <span> *</span></label>
                                                <input type="password" class="form-control" placeholder="Password" name="password" value="">
                                              </div>
                                            
                                        </div>
                                        <div class="col-xl-12 col-sm-12 mb-3">
                                            <label class="form-label">Retype New Password <span> *</span></label>
                                             <input type="password" class="form-control" placeholder="Retype Password" name="repassword" value="">
                                        </div>

                                      

                                      
                                    </div>	

                                    <div class="filter">
                                        <div class="text-center">
                                            
                                            <button type="submit" class="btn btn-primary my-2">Submit</button>
                                        </div>
                                    </div>
									</form>
                                
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		
@endsection