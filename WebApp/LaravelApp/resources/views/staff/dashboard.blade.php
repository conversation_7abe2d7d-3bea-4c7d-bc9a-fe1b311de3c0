@extends('staff.layout.master')

@section('content')

<div class="content-body default-height">
            <!-- row -->
			<div class="container-fluid">
			
			<div class="row mb-5">
						<div class="col-xl-3 col-xxl-4" style="display: none;">
							<div class="card">
								<div class="card-body">
									<h4 class="card-intro-title">Calendar</h4>
	
									<div class="">
										<div id="external-events" class="my-3">
										
										  
										</div>
								
										
									
									</div>
								</div>
							</div>
						</div>
						<div class="col-xl-12 col-xxl-12">
							<div class="card">
							<form id="calendar1" name="calendar1" method="get" action="{{route('staffDashboard')}}">
							<div class="card-header d-sm-flex d-block pb-0 border-0">
								<div class="me-auto pe-3">
									<h4 class="text-black fs-20 font-w600">Calendar</h4>
								
								</div>
								
								<div class="col-xl-4 col-lg-4  col-xxl-4">

									<select class="form-control default-select" id="sel4" name="staff">
										<option value="" selected="selected">Filter by Employes</option>
										@foreach($employees1 as $employee)
									<option value="{{$employee->staff_id}}">{{$employee->staff_name}}</option>
										@endforeach
										
										
									</select>
									
									
								</div>
								
							</div>
							</form>
								
								<div class="card-body">
									<div id="calendar" class="app-fullcalendar"></div>
								</div>
							</div>
						</div>
						
					
					</div>
			
				<div class="row mb-5">

				
					<div class="col-xl-12 col-xxl-12">

					
						<div class="row">
							<div class="col-sm-3">
								<div class="card avtivity-card bg-grad-2 overflow-hidden">
									<div class="card-body">
										<div class="media align-items-center">
											<span class="activity-icon bgl-success">
												<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" x="0" y="0" viewBox="0 0 24 24" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><g fill="#000" fill-rule="evenodd" clip-rule="evenodd"><path d="M12 11.667A3.333 3.333 0 1 0 12 5a3.333 3.333 0 0 0 0 6.667zm0-1A2.333 2.333 0 1 0 12 6a2.333 2.333 0 0 0 0 4.667zM7.5 10.5a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zm-1 0a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"  fill="#ffffff" opacity="1" data-original="#000000"></path><path d="M1.5 19v-2a3.5 3.5 0 0 1 6.428-1.918 4.5 4.5 0 0 1 8.144 0A3.5 3.5 0 0 1 22.5 17v2a.5.5 0 0 1-.5.5H2a.5.5 0 0 1-.5-.5zm1-2a2.5 2.5 0 0 1 5 0v1.5h-5zm19 1.5V17a2.5 2.5 0 0 0-5 0v1.5zm-6 0V17a3.5 3.5 0 1 0-7 0v1.5z"  fill="#ffffff" opacity="1" data-original="#000000"></path><path d="M21.5 10.5a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zm-1 0a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z" fill="#ffffff" opacity="1" data-original="#000000"></path></g></g></svg>
											</span>
											<div class="media-body">
												<p class="fs-16 font-w600 mb-2">CLIENTS</p>
												<span class="title font-w600">{{$clients}}</span>
											</div>
										</div>
										
									</div>
									<div class="effect bg-success"></div>
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
										<path fill="rgba(255,255,255,0.3)" fill-opacity="1" d="M0,128L34.3,112C68.6,96,137,64,206,96C274.3,128,343,224,411,250.7C480,277,549,235,617,213.3C685.7,192,754,192,823,181.3C891.4,171,960,149,1029,117.3C1097.1,85,1166,43,1234,58.7C1302.9,75,1371,149,1406,186.7L1440,224L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z"></path>
									</svg>
								</div>
							</div>
							<div class="col-sm-3">
								<div class="card avtivity-card  bg-grad-3 overflow-hidden">
									<div class="card-body">
										<div class="media align-items-center">
											<span class="activity-icon bgl-success">
												<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M59.686 481.055A6.821 6.821 0 0 0 66.634 487h90a6.713 6.713 0 0 0 6.7-8.73l-11.514-37.714 35.871-22.656a20.77 20.77 0 0 1 15.1-1.926c.144.037.29.075.437.1l74.909 13.8a98.358 98.358 0 0 0 60.775-8.579 6.774 6.774 0 0 0 .856-.5l129.557-88.955a7 7 0 0 0 2.035-9.38 36.972 36.972 0 0 0-49.3-13.484l-1.206.651c-9.43-10.647-25.1-14.624-37.122-8.529l-.156.082-4.376 2.357a41 41 0 0 0-46.19-3.63L305.272 315h-37a52.531 52.531 0 0 1-22.743-5.22l-5.137-2.489a89.566 89.566 0 0 0-93.215 9.1c-.154.116-.3.236-.446.363L134.325 327.8a41.637 41.637 0 0 1-14.36 8.339l-6.636-21.973a7.189 7.189 0 0 0-6.695-5.166h-60a7.21 7.21 0 0 0-5.241 2.567 6.992 6.992 0 0 0-1.707 5.488Zm330.448-167.5c5.679-2.833 12.7-.927 17.744 3.078l-47.324 25.531-1.465.773a43.993 43.993 0 0 0-5.364-9.764Zm-50.354-1.4a6.51 6.51 0 0 0 .166-.092 26.983 26.983 0 0 1 25.213-.957l-21.9 11.8a43.891 43.891 0 0 0-12.241-5.986Zm-196.146 26.1 12.186-10.849a75.621 75.621 0 0 1 78.48-7.516l5.138 2.493A66.619 66.619 0 0 0 268.272 329H318.1a30.094 30.094 0 0 1 29.134 22.675l-34.274-1.555a217.243 217.243 0 0 0-39.563 1.825 7 7 0 0 0 1.92 13.866 203.555 203.555 0 0 1 37.01-1.71l42.5 1.923c.1 0 .211.007.316.007a7 7 0 0 0 7-7c0-.624-.021-1.244-.046-1.862l5.039-2.657 61.57-33.216a22.988 22.988 0 0 1 26.481 3.267l-122.926 84.4a84.254 84.254 0 0 1-51.612 7.14l-74.68-13.752a34.727 34.727 0 0 0-25.271 3.44 7.44 7.44 0 0 0-.323.193l-32.8 20.735-23.53-77.179a55.652 55.652 0 0 0 19.589-11.284ZM101.45 323l45.732 150h-74.35L54.539 323Z" fill="#ffffff" opacity="1" data-original="#000000" class=""></path><path d="M106.918 470.721a19 19 0 1 0-19-19 19.022 19.022 0 0 0 19 19Zm0-24a5 5 0 1 1-5 5 5.006 5.006 0 0 1 5-5ZM145.328 164.843l18.994 1.831a94.052 94.052 0 0 0 9.4 22.341l-12.063 14.566A7 7 0 0 0 162.1 213l22.778 22.778a7 7 0 0 0 9.415.442l14.6-12.095a95.782 95.782 0 0 0 22.3 9.426l1.831 19.061A7.057 7.057 0 0 0 240 259h32.294a7.057 7.057 0 0 0 6.968-6.392l1.831-19.025a94.108 94.108 0 0 0 22.34-9.414L318 236.224a7 7 0 0 0 9.414-.446L350.189 213a7 7 0 0 0 .443-9.416l-12.1-14.606a95.77 95.77 0 0 0 9.428-22.307l18.716-1.827a7 7 0 0 0 6.32-6.967v-32.295a7 7 0 0 0-6.328-6.968l-18.994-1.83a94.052 94.052 0 0 0-9.4-22.341l12.063-14.567a7 7 0 0 0-.41-9.382l-22.485-22.779a7 7 0 0 0-9.442-.473l-14.6 12.1a95.782 95.782 0 0 0-22.3-9.426l-1.831-18.79A6.824 6.824 0 0 0 272.291 25H240a6.824 6.824 0 0 0-6.968 6.121L231.2 50.01a93.917 93.917 0 0 0-22.34 9.347l-14.568-12.089a7 7 0 0 0-9.415.429L162.1 70.468a7 7 0 0 0-.442 9.411l12.1 14.6a95.775 95.775 0 0 0-9.426 22.3l-19 1.831a7 7 0 0 0-6.332 6.972v32.293a7 7 0 0 0 6.328 6.968ZM153 131.94l17.6-1.7a7 7 0 0 0 6.169-5.488 81.95 81.95 0 0 1 11.443-27.092 7 7 0 0 0-.49-8.259l-11.217-13.546 13.766-13.766 13.544 11.217a7 7 0 0 0 8.348.434 79.385 79.385 0 0 1 27.007-11.382 6.767 6.767 0 0 0 5.488-5.962l1.7-17.4h19.58l1.7 17.4a6.882 6.882 0 0 0 5.488 6.066 81.812 81.812 0 0 1 27.1 11.387 7 7 0 0 0 8.26-.517l13.513-11.2 13.535 13.7L324.278 89.4a7 7 0 0 0-.432 8.346 79.4 79.4 0 0 1 11.381 27.006 7 7 0 0 0 6.169 5.488l17.6 1.7v19.585l-17.323 1.691a7 7 0 0 0-6.162 5.487 81.929 81.929 0 0 1-11.439 27.1 7 7 0 0 0 .49 8.26l11.222 13.537-13.766 13.766-13.545-11.217a7 7 0 0 0-8.348-.434 79.385 79.385 0 0 1-27.007 11.385 7.073 7.073 0 0 0-5.488 6.233L265.934 245h-19.58l-1.7-17.667a7.038 7.038 0 0 0-5.488-6.2 81.99 81.99 0 0 1-27.095-11.454 7 7 0 0 0-8.26.483l-13.544 11.213-13.762-13.775 11.217-13.545a7 7 0 0 0 .432-8.349 79.405 79.405 0 0 1-11.381-27.006 7 7 0 0 0-6.169-5.488l-17.6-1.7Z"  fill="#ffffff" opacity="1" data-original="#000000" class=""></path><path d="M256.145 211.6a69.875 69.875 0 1 0-69.875-69.874 69.953 69.953 0 0 0 69.875 69.874Zm0-125.749a55.875 55.875 0 1 1-55.875 55.875 55.939 55.939 0 0 1 55.875-55.872Z"  fill="#ffffff" opacity="1" data-original="#000000" class=""></path><path d="M243.656 163.094a7 7 0 0 0 9.78 0l33.506-32.713a7 7 0 1 0-9.779-10.018L248.546 148.3l-13.709-13.384a7 7 0 1 0-9.779 10.018Z"  fill="#ffffff" opacity="1" data-original="#000000" class=""></path></g></svg>
											</span>
											<div class="media-body">
												<p class="fs-16 font-w600 mb-2">SERVICES</p>
												<span class="title font-w600">{{$service1}}</span>
											</div>
										</div>
										
									</div>
									<div class="effect bg-success"></div>
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
										<path fill="rgba(255,255,255,0.3)" fill-opacity="1" d="M0,128L34.3,112C68.6,96,137,64,206,96C274.3,128,343,224,411,250.7C480,277,549,235,617,213.3C685.7,192,754,192,823,181.3C891.4,171,960,149,1029,117.3C1097.1,85,1166,43,1234,58.7C1302.9,75,1371,149,1406,186.7L1440,224L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z"></path>
									</svg>
								</div>
							</div>


							<div class="col-sm-3">
								<div class="card avtivity-card  bg-grad-1 overflow-hidden">
									<div class="card-body">
										<div class="media align-items-center">
											<span class="activity-icon bgl-success">
												<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" x="0" y="0" viewBox="0 0 511 511.999" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M216.5 366c5.52 0 10-4.48 10-10s-4.48-10-10-10-10 4.48-10 10 4.48 10 10 10zm0 0" fill="#ffffff" opacity="1" data-original="#000000"></path><path d="m3.43 389.07 120 120a9.996 9.996 0 0 0 14.14 0l66-66a9.996 9.996 0 0 0 0-14.14l-2.93-2.93h144.15c12.66 0 24.741-4.746 34.058-13.395l121.316-113.699c11.613-10.785 15.461-27.918 9.57-42.629a38.492 38.492 0 0 0-23.851-22.355 38.492 38.492 0 0 0-32.453 3.957c-.063.039-26.934 17.672-26.934 17.672-.183-54.207-32.945-112.012-78.043-138.035a30.088 30.088 0 0 0 5.887-10.375c5.16-15.7-3.418-32.692-19.14-37.883-.493-.164-.985-.309-1.477-.461l25.742-46.242a9.989 9.989 0 0 0 .512-8.664 9.984 9.984 0 0 0-6.457-5.801C335.063 2.723 315.879 0 296.5 0c-19.375 0-38.562 2.723-57.02 8.086a9.997 9.997 0 0 0-5.945 14.469l25.742 46.242c-.5.156-1 .305-1.5.469-15.699 5.183-24.273 22.171-19.113 37.882a30.052 30.052 0 0 0 5.91 10.36C198.804 143.918 166.5 202.316 166.5 256c0 1.465.035 2.91.082 4.344-17.91 4.886-34.562 13.789-48.566 26.05l-30.391 26.59-4.055-4.054a9.996 9.996 0 0 0-14.14 0l-66 66a9.996 9.996 0 0 0 0 14.14zM257.332 24.184C270.145 21.402 283.27 20 296.5 20s26.355 1.402 39.172 4.184L313.387 64.21a121.925 121.925 0 0 0-33.77 0zm6.688 64.082c20.52-6.715 42.89-7.223 64.933-.008 5.246 1.73 8.11 7.402 6.387 12.637a9.94 9.94 0 0 1-8.887 6.855c-19.719-6.27-40.2-6.281-59.91-.02h-.004c-4.11-.257-7.598-2.945-8.879-6.832-1.723-5.242 1.14-10.914 6.36-12.632zm5.53 39.55c17.34-6.246 35.305-6.394 52.688-.421C368.7 143.37 406.5 201.063 406.5 256c0 4.656-.293 9.094-.871 13.242l-48.067 31.535C350.123 291.56 338.844 286 326.5 286h-20v-11.719c11.64-4.129 20-15.246 20-28.281 0-16.543-13.457-30-30-30-5.512 0-10-4.484-10-10s4.488-10 10-10c3.543 0 7.281 1.809 10.816 5.227 3.97 3.84 10.301 3.734 14.141-.23 3.84-3.97 3.734-10.302-.234-14.142-5.075-4.914-10.153-7.69-14.723-9.207V166c0-5.523-4.477-10-10-10s-10 4.477-10 10v11.719c-11.637 4.129-20 15.246-20 28.281 0 16.543 13.457 30 30 30 5.516 0 10 4.484 10 10s-4.484 10-10 10c-4.273 0-8.883-2.688-12.984-7.566-3.555-4.227-9.864-4.774-14.09-1.22-4.227 3.556-4.774 9.864-1.219 14.09 5.344 6.36 11.633 10.79 18.293 13.024V286h-3.328c-4.914 0-7.121-3.203-10.582-5.441-21.13-15.836-47.3-24.559-73.7-24.559-4.128 0-8.265.215-12.382.633 0-.211-.008-.418-.008-.633 0-54.168 37.258-111.668 83.05-128.184zM131.189 301.441C149.977 284.988 174.12 276 198.89 276c22.101 0 44.011 7.3 61.691 20.555 2.55 1.492 9.652 9.445 22.586 9.445H326.5c11.383 0 20 9.254 20 20 0 11.027-8.973 20-20 20h-70c-5.523 0-10 4.477-10 10s4.477 10 10 10h70c22.055 0 40-17.945 40-40 0-2.293-.203-4.555-.586-6.781l98.2-64.43a18.573 18.573 0 0 1 15.558-1.86 18.562 18.562 0 0 1 11.492 10.778c2.887 7.203 1.074 15.27-4.644 20.578L365.207 397.98a29.917 29.917 0 0 1-20.418 8.02H180.641l-78.84-78.844zm-54.688 28.7L182.36 436l-51.86 51.86L24.64 382zm0 0" fill="#ffffff" opacity="1" data-original="#000000"></path></g></svg>
											</span>
											<div class="media-body">
												<p class="fs-16 font-w600 mb-2">INCOME</p>
												<span class="title font-w600">{{$total[0]->total}} KWD</span>
											</div>
										</div>
										
									</div>
									<div class="effect bg-success"></div>
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
										<path fill="rgba(255,255,255,0.3)" fill-opacity="1" d="M0,128L34.3,112C68.6,96,137,64,206,96C274.3,128,343,224,411,250.7C480,277,549,235,617,213.3C685.7,192,754,192,823,181.3C891.4,171,960,149,1029,117.3C1097.1,85,1166,43,1234,58.7C1302.9,75,1371,149,1406,186.7L1440,224L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z"></path>
									</svg>
								</div>
							</div>

							<div class="col-sm-3">
								<div class="card avtivity-card  bg-grad-4 overflow-hidden">
									<div class="card-body">
										<div class="media align-items-center">
											<span class="activity-icon bgl-success">
											<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M423.459 366.897c-9.606-4.431-20.33-4.866-30.196-1.226l-112.547 41.506c1.084-17.102-8.208-33.941-24.653-41.604l-110.291-51.385c-14.814-6.902-32.312-5.478-50.604 4.12L60.256 336.63l-.454-4.95v.001a14.835 14.835 0 0 0-5.327-10.113 14.836 14.836 0 0 0-10.914-3.399l-28.798 2.644c-8.202.753-14.263 8.039-13.51 16.242l14.302 155.82a14.836 14.836 0 0 0 5.328 10.113 14.83 14.83 0 0 0 10.913 3.399l28.799-2.643c8.202-.753 14.263-8.039 13.51-16.243l-.546-5.946c18.497-9.459 34.154-10.318 51.69-2.994l63.662 26.589c9.287 3.879 18.48 5.819 27.509 5.819 7.459 0 14.806-1.325 21.995-3.977l182.188-67.189c20.434-7.536 30.928-30.295 23.394-50.733-3.639-9.869-10.933-17.743-20.538-22.173zM31.39 490.355 17.281 336.647l26.684-2.449 14.108 153.709zm383.676-65.565-182.189 67.189c-12.143 4.478-24.508 3.956-37.8-1.595l-63.663-26.589c-20.196-8.436-38.889-8.26-59.413.788L61.838 353.869l40.764-21.393c13.899-7.293 26.15-8.568 36.412-3.785l110.291 51.385c13.421 6.254 19.252 22.261 12.999 35.682s-22.259 19.25-35.679 12.998l-58.914-27.449a8 8 0 0 0-6.757 14.504l58.914 27.449a42.575 42.575 0 0 0 18.031 4.006c14.731-.002 28.898-7.649 36.786-20.81L398.8 380.684c5.858-2.16 12.235-1.896 17.959.743 5.724 2.64 10.065 7.319 12.225 13.177 4.482 12.161-1.761 25.703-13.918 30.186zm95.745-228.431c0-9.562-7.779-17.34-17.34-17.34h-46.393v-9.891c0-11.313-9.205-20.519-20.52-20.519h-57.657c-11.314 0-20.519 9.205-20.519 20.519v9.891h-46.393a17.28 17.28 0 0 0-3.02.281c-15.247-12.752-32.814-22.072-51.567-27.524 24.507-13.835 41.1-40.11 41.1-70.203C288.505 37.146 252.359 1 207.931 1s-80.574 36.146-80.574 80.573c0 30.099 16.6 56.379 41.115 70.211-55.608 16.226-97.905 65.779-102.421 125.976-.361 4.817 1.306 9.601 4.574 13.123 3.268 3.521 7.914 5.542 12.745 5.542h201.282v27.988c0 13.186 10.728 23.913 23.914 23.913h178.333c13.186 0 23.914-10.728 23.914-23.913zm-16 0v47.33c-.005 7.769-6.327 14.087-14.096 14.087H314.748c-7.773 0-14.097-6.323-14.097-14.097v-47.32c0-.714.626-1.34 1.34-1.34h191.48c.714.001 1.34.627 1.34 1.34zm-111.226 77.417h28.292v9.007h-28.292zm-19.202-104.647c0-2.449 2.069-4.519 4.519-4.519h57.657c2.45 0 4.52 2.069 4.52 4.519v9.891h-66.695v-9.891zM143.357 81.573c0-35.605 28.968-64.573 64.574-64.573s64.574 28.968 64.574 64.573c0 35.598-28.953 64.559-64.547 64.573h-.052c-35.595-.013-64.549-28.975-64.549-64.573zM83.37 280.425c-.413 0-.755-.143-1.017-.425-.261-.282-.378-.634-.348-1.044 4.914-65.492 60.213-116.795 125.899-116.809l.026.001.027-.001c28.408.006 55.87 9.654 78.14 27.301a17.22 17.22 0 0 0-1.446 6.912l.001 84.065zm403.528 51.901H308.565c-4.364 0-7.914-3.55-7.914-7.913l.001-54.152a29.9 29.9 0 0 0 14.096 3.516h52.837v12.65c0 6.813 5.543 12.356 12.357 12.356h35.577c6.813 0 12.357-5.543 12.357-12.356v-12.65h52.838a29.9 29.9 0 0 0 14.096-3.516l.001 54.153c.001 4.362-3.549 7.912-7.913 7.912zm-138.472-83.473h98.609a8 8 0 0 0 8-8v-28.91a8 8 0 0 0-8-8h-98.609a8 8 0 0 0-8 8v28.91a8 8 0 0 0 8 8zm8-28.911h82.609v12.91h-82.609z" fill="#ffffff" opacity="1" data-original="#000000" class=""></path></g></svg>
											</span>
											<div class="media-body">
												<p class="fs-16 font-w600 mb-2">EMPLOYEES</p>
												<span class="title font-w600">{{$staffs}}</span>
											</div>
										</div>
										
									</div>
									<div class="effect bg-success"></div>
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
										<path fill="rgba(255,255,255,0.3)" fill-opacity="1" d="M0,128L34.3,112C68.6,96,137,64,206,96C274.3,128,343,224,411,250.7C480,277,549,235,617,213.3C685.7,192,754,192,823,181.3C891.4,171,960,149,1029,117.3C1097.1,85,1166,43,1234,58.7C1302.9,75,1371,149,1406,186.7L1440,224L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z"></path>
									</svg>
								</div>
							</div>


							<div class="col-sm-3">
								<div class="card avtivity-card  bg-grad-5 overflow-hidden">
									<div class="card-body">
										<div class="media align-items-center">
											<span class="activity-icon bgl-success">
											<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M456.832 32.133H415.6V15c0-8.284-6.716-15-15-15s-15 6.716-15 15v17.133h-66.4V15c0-8.284-6.716-15-15-15s-15 6.716-15 15v17.133h-66.4V15c0-8.284-6.716-15-15-15s-15 6.716-15 15v17.133h-66.4V15c0-8.284-6.716-15-15-15s-15 6.716-15 15v17.133H55.166C24.748 32.133 0 56.88 0 87.3v369.533C0 487.252 24.748 512 55.166 512h401.666C487.251 512 512 487.252 512 456.833V87.3c0-30.42-24.749-55.167-55.168-55.167zm-401.666 30H96.4v17.134c0 8.284 6.716 15 15 15s15-6.716 15-15V62.133h66.398v17.134c0 8.284 6.716 15 15 15s15-6.716 15-15V62.133h66.4v17.134c0 8.284 6.716 15 15 15s15-6.716 15-15V62.133h66.4v17.134c0 8.284 6.716 15 15 15s15-6.716 15-15V62.133h41.232c13.879 0 25.17 11.29 25.17 25.167v41.233H30V87.3c0-13.877 11.29-25.167 25.166-25.167zM456.832 482H55.166C41.29 482 30 470.71 30 456.833v-298.3h452v298.3C482 470.71 470.709 482 456.832 482z" fill="#ffffff" opacity="1" data-original="#000000"></path><path d="M151.566 208.867H87.299c-8.284 0-15 6.716-15 15v64.266c0 8.284 6.716 15 15 15h64.268c8.284 0 15-6.716 15-15v-64.266c-.001-8.284-6.716-15-15.001-15zm-15 64.266h-34.268v-34.266h34.268zM424.699 208.867h-64.266c-8.284 0-15 6.716-15 15v64.266c0 8.284 6.716 15 15 15h64.266c8.284 0 15-6.716 15-15v-64.266c0-8.284-6.716-15-15-15zm-15 64.266h-34.266v-34.266h34.266zM151.566 337.4H87.299c-8.284 0-15 6.716-15 15v64.266c0 8.284 6.716 15 15 15h64.268c8.284 0 15-6.716 15-15V352.4c-.001-8.284-6.716-15-15.001-15zm-15 64.266h-34.268V367.4h34.268zM424.699 337.4h-64.266c-8.284 0-15 6.716-15 15v64.266c0 8.284 6.716 15 15 15h64.266c8.284 0 15-6.716 15-15V352.4c0-8.284-6.716-15-15-15zm-15 64.266h-34.266V367.4h34.266zM288.133 337.4h-64.268c-8.284 0-15 6.716-15 15v64.266c0 8.284 6.716 15 15 15h64.268c8.284 0 15-6.716 15-15V352.4c0-8.284-6.716-15-15-15zm-15 64.266h-34.268V367.4h34.268zM279.475 222.673l-34.834 34.836-12.116-12.116c-5.857-5.858-15.355-5.858-21.213 0s-5.858 15.355 0 21.213l22.723 22.723a15 15 0 0 0 21.213 0l45.441-45.443c5.857-5.858 5.857-15.355 0-21.213-5.859-5.858-15.356-5.858-21.214 0z" fill="#ffffff" opacity="1" data-original="#000000"></path></g></svg>
											</span>
											<div class="media-body">
												<p class="fs-16 font-w600 mb-2">Contract Expiry Date</p>
												<span class="title font-w600">{{$vendorenddate->contractenddate}}</span>
											</div>
										</div>
										
									</div>
									<div class="effect bg-success"></div>
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
										<path fill="rgba(255,255,255,0.3)" fill-opacity="1" d="M0,128L34.3,112C68.6,96,137,64,206,96C274.3,128,343,224,411,250.7C480,277,549,235,617,213.3C685.7,192,754,192,823,181.3C891.4,171,960,149,1029,117.3C1097.1,85,1166,43,1234,58.7C1302.9,75,1371,149,1406,186.7L1440,224L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z"></path>
									</svg>
								</div>
							</div>
							
							
						</div>
					</div>

					<div class="row">
						<div class="col-xl-3 col-xxl-4" style="display: none;">
							<div class="card">
								<div class="card-body">
									<h4 class="card-intro-title">Calendar</h4>
	
									<div class="">
										<div id="external-events" class="my-3">
										
										  
										</div>
								
										
									
									</div>
								</div>
							</div>
						</div>
						<div class="col-xl-12 col-xxl-12">
							<div class="card">
							<form id="calendar1" name="calendar1" method="get" action="{{route('staffDashboard')}}">
							<div class="card-header d-sm-flex d-block pb-0 border-0">
								<div class="me-auto pe-3">
									<h4 class="text-black fs-20 font-w600">Calendar</h4>
								
								</div>
								
								<div class="col-xl-4 col-lg-4  col-xxl-4">

									<select class="form-control default-select" id="sel4" name="staff">
										<option value="" selected="selected">Filter by Employes</option>
										@foreach($employees1 as $employee)
									<option value="{{$employee->staff_id}}">{{$employee->staff_name}}</option>
										@endforeach
										
										
									</select>
									
									
								</div>
								
							</div>
							</form>
								
								<div class="card-body">
									<div id="calendar" class="app-fullcalendar"></div>
								</div>
							</div>
						</div>
						
					
					</div>
<?php /*
					<div class="col-xl-12 col-xxl-12">
						<div class="col-12">
							<div class="card">
								<div class="card-header d-sm-flex d-block pb-0 border-0">
									<div class="me-auto pe-3">
										<h4 class="text-black fs-20 font-w600">Booking</h4>
										<p class="fs-13 mb-0">PERFORMANCE</p>
									</div>
									<select class="default-select w-auto" aria-label="Default select example">
									  <option selected>Weekly</option>
									  <option value="1">Monthly</option>
									  <option value="2">Daily</option>
									  <option value="3">Yearly</option>
									</select>
								</div>
								<div class="card-body px-3 pb-0">
									<div id="chartTimeline"></div>
								</div>
							</div>
						</div>

						</div>
*/ ?>
                            <div class="col-xl-12 col-xxl-12">
							<div class="card">
                                <div class="card-header">
                                    <h4>Revenue</h4>
                                </div>
                                <div class="card-body">
                                    <div id="chartBarStaff"></div>
                                </div>
                            </div>
							</div>
					
					
					
				</div>




				<div class="row">
				
				

					<div class="col-xl-12 col-xxl-12">


						<div class="row">
						  

							


								<div class="col-xl-12 col-xxl-12">

									<div class="card">
									<div class="card-header">
									<h4 class="card-title">Scheduled Booking Today</h4>
		
									<a href="{{route('staff-booking')}}" class="btn btn-primary rounded d-none d-md-block">See All</a>
									</div>
									<div class="card-body p-0">
										<div id="DZ_W_TimeLine" class="widget-timeline  my-4 px-4 dz-scroll height370">
											<div class="table-responsive">
											
											 <table id="example4" class="table table-responsive-md">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th>BOOKING ID</th>
                                                <th>USER NAME</th>
                                               
                                                <th>BOOKING AT</th>
                                                <th>DATE</th>
                                               
												<th>DURATION</th>
                                                <th>PAYMENT</th>
                                                <th>TYPE</th>
                                                <th>PAID</th>
												<th>STATUS</th>
												<th>Option</th>
												<th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										
										@foreach($bookings as $key => $book)
                                            <tr>
                                                <td>{{$key+1}}</td>
                                                <td>{{$book->Bookid}}</td>
                                                <td>{{$book->name}}</td>
                                                
                                                <td>{{$book->Bookingat}}</td>
                                                <td>{{$book->service_date}}</td>
                                             
                                               
												  <td>
												  
                                                   {{$book->service_time}}
												</td>


                                                <td>
												  
                                                    {{$book->total_price}}
												</td>


                                                <td>
												  
                                                    <span class="badge light badge-success mb-2">{{$book->payment_method}}</span>
												</td>

                                                <td>
												  
                                                    <span class="badge light badge-success mb-2">{{$book->payment_status}}</span>
												</td>
  <td>
@if($book->status==1) Pending @endif
@if($book->status==3) Cancelled @endif
@if($book->status==6) Approved @endif
@if($book->status==2) Completed @endif
   </td>                                             
   
   <td>
                                                    <select class="form-control default-select statuschange" data-id="{{$book->id}}" id="sel1">
													
                                                        <option value="1" @if($book->status==1) selected @endif>Pending</option>
													
                                                        <option value="3" @if($book->status==3) selected @endif>Cancel</option>
                                                        <option value="6" @if($book->status==6) selected @endif>Approved</option>
                                                        <option value="2" @if($book->status==2) selected @endif>Completed</option>
                                                    </select>									
												</td>

												<td> <a href="{{route('staff-bookingdetail',$book->id)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="View"  ><i class="fa fa-eye"></i></a></td>
												
											
                                            </tr>
											@endforeach
                                            
                                            
                                          
                                        </tbody>
                                    </table>
												
										</div>
										</div>
									</div>
								</div>
										</div>




						</div>

						</div>
					
					
					
				</div>

            </div>
        </div>
		
@endsection








<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

<script>
    function chartBarStaff(elementId, data, categories) {
        console.log("Rendering chart with data:", data); // Debugging log for data
        console.log("Rendering chart with categories:", categories); // Debugging log for categories

        var optionsArea = {
            series: [{
                name: "Revenue",
                data: data
            }],
            chart: {
                height: 400,
                type: 'area',
                toolbar: { show: false },
                zoom: { enabled: false }
            },
            dataLabels: { enabled: false },
            stroke: {
                width: [4],
                colors: ['#007BFF'],
                curve: 'smooth'
            },
            xaxis: {
                categories: categories,
                labels: {
                    style: {
                        colors: '#787878',
                        fontSize: '14px',
                        fontFamily: 'Poppins',
                        fontWeight: 100,
                    },
                },
            },
            yaxis: {
                labels: {
                    offsetX: -16,
                    style: {
                        colors: '#787878',
                        fontSize: '14px',
                        fontFamily: 'Poppins',
                        fontWeight: 100,
                    },
                },
            },
            fill: {
                colors: ['rgba(0, 143, 150, 1)'],
                type: 'solid',
                opacity: 0.7
            },
            colors: ['#FF3282'],
            grid: {
                borderColor: '#f1f1f1',
                xaxis: { lines: { show: true } }
            },
            responsive: [{
                breakpoint: 575,
                options: { chart: { height: 250 } }
            }]
        };

        var chartArea = new ApexCharts(document.querySelector(`#${elementId}`), optionsArea);
        chartArea.render();
    }

    @if(isset($ordertotals))
        document.addEventListener("DOMContentLoaded", function() {
            // Convert the PHP string into an array in JavaScript
            var ordertotals = {!! json_encode($ordertotals) !!}.split(',').map(Number); // Converts to an array of numbers
            var categories = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

            // Debugging: Check if ordertotals array is correctly formed
            console.log("ordertotals array:", ordertotals);

            // Call the render function only if ordertotals has values
            if (Array.isArray(ordertotals) && ordertotals.length === 12) {
                chartBarStaff('chartBarStaff', ordertotals, categories);
            } else {
                console.error("Error: ordertotals data is invalid or incomplete.");
            }
        });
    @endif
</script>
