@extends('staff.layout.master')

@section('content')

 <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-12">
                        <div class="card">
                            <div class="card-header justify-content-between">
                                <h4 class="card-title">Booking table</h4>

                                <a  href="javascript:void()" data-bs-toggle="modal" data-bs-target="#add-category" class="btn btn-primary btn-rounded">Add Appointment / Breaktime</a>
                            </div>


@if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif


                            <div class="card-body">
                                <div class="row mb-5 g-3">
                                     <div class="col-xl-3 col-lg-3 col-sm-6 col-12 ">
                                        <select class="form-control default-select" id="sort1" class="sort">
                                                <option value="" selected="selected">Sorting</option>
                                                <option value="1">Sort by Latest</option>
                                                <option value="2">Sort by A-Z</option>
												
                                            </select>
                                    </div>

									<div class="col-xl-4 col-lg-4 col-sm-6 col-12 ">
                                        <select class="form-control default-select payment" id="payment">
                                                <option value="">Filter by Payment status</option>
                                                <option value="Card">Card</option>
                                                <option value="Knet">Knet</option>
												
                                            </select>
                                    </div>

									<div class="col-xl-4 col-lg-2 col-sm-6 col-12 ">
										<select class="form-control default-select" id="status1">
											<option value="" selected="selected">Filter by status</option>
											<option value="Scheduled">Scheduled</option>
										
											<option value="Cancelled">Cancelled </option>
											<option value="Completed">Completed</option>
										</select>
                                    </div>

                                    <div class="col-xl-4 col-lg-6 col-12">


                                        <div class="input-group">
                                            <input type="text" id="searchuser" class="form-control" placeholder="search users..." aria-label="Username" aria-describedby="basic-addon1">
                                            <span class="input-group-text" id="basic-addon1"><a href="javascript:void(0);" class="vendorsearch1">Search</a></span>
                                      </div>
                                    </div>
                                </div>

                               
                        

                                <div class="table-responsive">
                                    <table id="example4" class="display min-w850">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th>BOOKING ID</th>
                                                <th>USER NAME</th>
                                               
                                                <th>BOOKING AT</th>
                                                <th>DATE</th>
                                               
												<th>DURATION</th>
                                                <th>AMOUNT</th>
                                                <th>TYPE</th>
                                                <th>PAID</th>
												<th>STATUS</th>
												<th>Option</th>
												<th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										
										@foreach($bookings as $key => $book)
                                            <tr>
                                                <td>{{$key+1}}</td>
                                                <td>{{$book->Bookid}}</td>
                                                <td>{{$book->name}}</td>
                                                
                                                <td>{{$book->Bookingat}}</td>
                                                <td>{{date('Y-m-d',strtotime($book->service_date))}}</td>
                                             
                                               
												  <td>
												  
                                                   {{$book->service_time}}
												</td>


                                                <td>
												  
                                                    {{$book->total_price}}
												</td>


                                                <td>
												  
                                                    <span class="badge light badge-success mb-2">{{$book->payment_method}}</span>
												</td>

                                                <td>
												  
                                                    <span class="badge light badge-success mb-2">{{$book->payment_status}}</span>
												</td>
  <td>
@if($book->status==1) <span class="badge light bg-yellow mb-2">Scheduled</span> @endif
@if($book->status==3) <span class="badge light bg-red mb-2">Cancelled</span> @endif
@if($book->status==6) Approved @endif
@if($book->status==2) <span class="badge light bg-green mb-2">Completed</span> @endif
   </td>                                             
   
   <td>
                                                    <select class="form-control default-select statuschange" data-id="{{$book->id}}" id="sel1">
													
                                                        <option value="1" @if($book->status==1) selected @endif>Scheduled</option>
													
                                                        <option value="3" @if($book->status==3) selected @endif>Cancel</option>
                                                       
                                                        <option value="2" @if($book->status==2) selected @endif>Completed</option>
                                                    </select>									
												</td>

												<td> <a href="{{route('staff-bookingdetail',$book->id)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="View"  ><i class="fa fa-eye"></i></a></td>
												
											
                                            </tr>
											@endforeach
                                            
                                            
                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>

<div class="modal fade none-border" id="add-category">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title"><strong>Add Appointment / Break Time</strong></h4>
                     <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
              
                    <form method="post" enctype='multipart/form-data' action="{{route('staff-booking-save')}}" >
													{{csrf_field()}}
													  <div class="modal-body">
											<div class="row">		
					 <div class="col-md-6 mb-2">
                                <label class="control-label form-label">Date <span> *</span></label>
                                <input id="date" required  name="date" required class="datepicker-default form-control" >
                            </div>
							 <div class="col-md-6 mb-2">
                                <label class="control-label form-label">Clients <span> *</span></label>
                                <select required class="form-control default-select form-white" data-placeholder="Service At..." name="clients" id="clients">
                                     <option value="">Select</option>
                                     	<option value="180">Staff Breaktime</option>
									 @foreach($users as $user)
									<option value="{{$user->id}}">{{$user->name}}</option>
										@endforeach
                                    
                                </select>
                            </div>
							 <div class="col-md-6 mb-2" id='serv'>
                                <label class="control-label form-label">Services <span> *</span></label>
                                <select required multiple required name="services[]" class="form-control default-select" id="sel2">
                                     @foreach($vendor_services as $services)
									<option value="{{$services->ServicesID}}">{{$services->Name}}</option>
                                   @endforeach
                                   
                                </select>
                            </div>
							
							  <div class="col-md-6 mb-2">
                                <label class="control-label form-label">From Time <span> *</span></label>
                                <div class="input-group" id="booking">
                                  
                                </div>
                            </div>
							
							 <div class="col-md-6 mb-2" id='totime' style="display:none;">
                                <label class="control-label form-label">To Time <span> *</span></label>
                                <div class="input-group" id="booking1">
                                  
                                </div>
                            </div>
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <label class="control-label form-label">Employee <span> *</span></label>
                                  <select  required class="form-control form-white" data-placeholder="Service At..." id="staff" name="staff" disabled>
                                     <option value="">Select</option>
									 @foreach($employees as $employee)
									<option value="{{$employee->staff_id}}" @if(session('citystaffid')==$employee->staff_id) selected @endif >{{$employee->staff_name}}</option>
										@endforeach
                                    
                                </select>
                            </div>
							
                            <div class="col-md-6 mb-2" id="loc">
                                <label class="control-label form-label">Service At <span> *</span></label>
                                <select name="serviceat" required class="form-control default-select form-white" data-placeholder="Service At..." id="serviceat">
                                    <option value="">Select</option>
									<option>Salon</option>
                                    <option>Home</option>
                                    <option>Both</option>
                                </select>
                            </div>

                           

                          
                           

                           

                           


                            <div class="col-md-6 mb-2" id="pay">
                                <label class="control-label form-label">Payment Status <span> *</span></label>
                               <select  required name="status" class="form-control default-select" id="sel3">
                                    
									<option value="Paid">Paid</option>
                                 <option value="Not Paid">Not Paid</option>
                                   
                                </select>
                            </div>
							
							<div class="col-md-6 mb-2" id="method">
                                <label class="control-label form-label">Payment Method <span> *</span></label>
                               <select  required name="paymentmethod" class="form-control default-select" id="sel4">
                                    
									<option>Knet</option>
                                 <option>COD</option>
                                   
                                </select>
                            </div>

                           

                            
                        </div>
                    
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default btn-danger light waves-effect" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary  save-category" id="Book">Add Appointment / Breaktime</button>
                </div>
				</form>
            </div>
        </div>
    </div>




		 <!-- Modal -->
		 <div class="modal fade" id="exampleModalpopover">
			<div class="modal-dialog modal-dialog-centered" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title">Reasssign</h5>
					   <button type="button" class="btn-close" data-bs-dismiss="modal">
						</button>
					</div>
					 <div class="modal-body">
						<div class="row">
													
							<div class="col-xl-12 col-sm-12 mb-3">
								<select class="form-control default-select" id="sel1">
									<option value="" selected="selected">Select Employee</option>
									<option value="1">Jithun</option>
									<option value="2"> Subin</option>
									<option value="2"> Jineesh </option>
									<option value="2"> Amras </option>
									<option value="2"> Shinoj </option>
									<option value="2"> Hashim </option>
								</select>
							</div>
	
	
					
	
							
						</div>
					</div>
					<div class="modal-footer">
						
						<button type="button" class="btn btn-primary">Submit</button>
					</div>
				</div>
			</div>
		</div>
		
@endsection
