<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Title -->
	<title>{{ config('app.name') }}</title>

	<!-- Meta -->
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="author" content="">
	<meta name="robots" content="">


	<meta property="og:image" content="social-image.png">
	<meta name="format-detection" content="telephone=no">

	<!-- Mobile Specific -->
	<meta name="viewport" content="width=device-width, initial-scale=1">

	<!-- Favicon icon -->
	<link rel="shortcut icon" type="image/x-icon" href="#">
	<link rel="stylesheet" href="{{ asset('staffpanel/vendor/chartist/css/chartist.min.css') }}">
	<link rel="stylesheet" href="{{ asset('staffpanel/vendor/select2/css/select2.min.css') }}">
	 <link rel="stylesheet" href="{{ asset('vendorpanel/vendor/pickadate/themes/default.css') }}">
	<link rel="stylesheet"  href="{{ asset('vendorpanel/vendor/pickadate/themes/default.date.css') }}">
	<link href="{{ asset('staffpanel/vendor/bootstrap-select/css/bootstrap-select.min.css') }}" rel="stylesheet">
	<link href="{{ asset('staffpanel/vendor/datatables/css/jquery.dataTables.min.css') }}" rel="stylesheet">
	<link href="{{ asset('staffpanel/vendor/owl-carousel/owl.carousel.css') }}" rel="stylesheet">
	<link href="{{ asset('staffpanel/css/style.css') }}" rel="stylesheet">
	<!-- Datatable -->
    <link href="{{ asset('staffpanel/vendor/datatables/css/jquery.dataTables.min.css') }}" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&amp;family=Roboto:wght@100;300;400;500;700;900&amp;display=swap" rel="stylesheet">
</head>
<body>

    <!--*******************
        Preloader start
    ********************-->
    <div id="preloader">
        <div class="sk-three-bounce">
            <div class="sk-child sk-bounce1"></div>
            <div class="sk-child sk-bounce2"></div>
            <div class="sk-child sk-bounce3"></div>
        </div>
    </div>
    <!--*******************
        Preloader end
    ********************-->

    <!--**********************************
        Main wrapper start
    ***********************************-->
    <div id="main-wrapper">

        <!--**********************************
            Nav header start
        ***********************************-->
         <div class="nav-header">
            <a href="{{route('staffDashboard')}}" class="brand-logo" aria-label="Gymove">
                <img class="logo-abbr" src="{{ asset('staffpanel/images/logo.png') }}" alt="">
                <img class="logo-compact" src="{{ asset('staffpanel/images/logo-text.png') }}" alt="">
                <img class="brand-title" src="{{ asset('staffpanel/images/logo-text.png') }}" alt="">
            </a>

            <div class="nav-control">
                <div class="hamburger">
                    <span class="line"></span><span class="line"></span><span class="line"></span>
                </div>
            </div>
        </div>
        <!--**********************************
            Nav header end
        ***********************************-->
		
		<!--**********************************
            Chat box start
        ***********************************-->
		<div class="chatbox">
			<div class="chatbox-close"></div>
			
		</div>
		<!--**********************************
            Chat box End
        ***********************************-->
		
		<!--**********************************
            Header start
        ***********************************-->
        <header class="header">
            <div class="header-content">
                <nav class="navbar navbar-expand">
                    <div class="collapse navbar-collapse justify-content-between">
                        <div class="header-left">
                            <div class="dashboard_bar">
								Dashboard
                            </div>
                        </div>
                        <ul class="navbar-nav header-right">
							<!--<li class="nav-item">
								<form>
									<div class="input-group search-area d-lg-inline-flex d-none me-3">
									  <span class="input-group-text" id="header-search">
											<button class="bg-transparent border-0" type="button" aria-label="header-search">
												<i class="flaticon-381-search-2"></i>
											</button>
									  </span>
									  <input type="text" class="form-control" placeholder="Search here" aria-label="Username" aria-describedby="header-search">
									</div>
								</form>
							</li>-->
							
							
							
                            <li class="nav-item dropdown header-profile">
                                <a class="nav-link" href="javascript:void(0)" role="button" data-bs-toggle="dropdown">
                                    <img src="{{url(session('admin_image'))}}" width="20" alt="">
									<div class="header-info">
										<span><strong>{{session('admin_name')}}</strong></span>
										
									</div>
                                </a>
                                <div class="dropdown-menu dropdown-menu-end">
                                    <a href="{{route('staff-changeaddress')}}" class="dropdown-item ai-icon">
                                        <svg id="icon-user1" xmlns="http://www.w3.org/2000/svg" class="text-primary" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                                        <span class="ms-2">Change Password</span>
                                    </a>
                                   
                                    <a href="{{route('staff-logout')}}" class="dropdown-item ai-icon">
                                        <svg id="icon-logout" xmlns="http://www.w3.org/2000/svg" class="text-danger" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path><polyline points="16 17 21 12 16 7"></polyline><line x1="21" y1="12" x2="9" y2="12"></line></svg>
                                        <span class="ms-2">Logout </span>
                                    </a>
                                </div>
                            </li>
                        </ul>
                    </div>
                </nav>
            </div>
        </header>
        <!--**********************************
            Header end ti-comment-alt
        ***********************************-->

        <!--**********************************
            Sidebar start
        ***********************************-->
          <div class="deznav">
            <div class="deznav-scroll">
				<ul class="metismenu" id="menu">
                    <li><a class="ai-icon" href="{{route('staffDashboard')}}" aria-expanded="false">
							<i class="flaticon-381-networking"></i>
							<span class="nav-text">Dashboard</span>
						</a>
                        
                    </li>
					<li><a class="ai-icon" href="{{route('staff-calendar')}}" aria-expanded="false">
						<i><svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="512" height="512" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M456.832 32.133H415.6V15c0-8.284-6.716-15-15-15s-15 6.716-15 15v17.133h-66.4V15c0-8.284-6.716-15-15-15s-15 6.716-15 15v17.133h-66.4V15c0-8.284-6.716-15-15-15s-15 6.716-15 15v17.133h-66.4V15c0-8.284-6.716-15-15-15s-15 6.716-15 15v17.133H55.166C24.748 32.133 0 56.88 0 87.3v369.533C0 487.252 24.748 512 55.166 512h401.666C487.251 512 512 487.252 512 456.833V87.3c0-30.42-24.749-55.167-55.168-55.167zm-401.666 30H96.4v17.134c0 8.284 6.716 15 15 15s15-6.716 15-15V62.133h66.398v17.134c0 8.284 6.716 15 15 15s15-6.716 15-15V62.133h66.4v17.134c0 8.284 6.716 15 15 15s15-6.716 15-15V62.133h66.4v17.134c0 8.284 6.716 15 15 15s15-6.716 15-15V62.133h41.232c13.879 0 25.17 11.29 25.17 25.167v41.233H30V87.3c0-13.877 11.29-25.167 25.166-25.167zM456.832 482H55.166C41.29 482 30 470.71 30 456.833v-298.3h452v298.3C482 470.71 470.709 482 456.832 482z" fill="#000000" opacity="1" data-original="#000000"></path><path d="M151.566 208.867H87.299c-8.284 0-15 6.716-15 15v64.266c0 8.284 6.716 15 15 15h64.268c8.284 0 15-6.716 15-15v-64.266c-.001-8.284-6.716-15-15.001-15zm-15 64.266h-34.268v-34.266h34.268zM424.699 208.867h-64.266c-8.284 0-15 6.716-15 15v64.266c0 8.284 6.716 15 15 15h64.266c8.284 0 15-6.716 15-15v-64.266c0-8.284-6.716-15-15-15zm-15 64.266h-34.266v-34.266h34.266zM151.566 337.4H87.299c-8.284 0-15 6.716-15 15v64.266c0 8.284 6.716 15 15 15h64.268c8.284 0 15-6.716 15-15V352.4c-.001-8.284-6.716-15-15.001-15zm-15 64.266h-34.268V367.4h34.268zM424.699 337.4h-64.266c-8.284 0-15 6.716-15 15v64.266c0 8.284 6.716 15 15 15h64.266c8.284 0 15-6.716 15-15V352.4c0-8.284-6.716-15-15-15zm-15 64.266h-34.266V367.4h34.266zM288.133 337.4h-64.268c-8.284 0-15 6.716-15 15v64.266c0 8.284 6.716 15 15 15h64.268c8.284 0 15-6.716 15-15V352.4c0-8.284-6.716-15-15-15zm-15 64.266h-34.268V367.4h34.268zM279.475 222.673l-34.834 34.836-12.116-12.116c-5.857-5.858-15.355-5.858-21.213 0s-5.858 15.355 0 21.213l22.723 22.723a15 15 0 0 0 21.213 0l45.441-45.443c5.857-5.858 5.857-15.355 0-21.213-5.859-5.858-15.356-5.858-21.214 0z" fill="#000000" opacity="1" data-original="#000000"></path></g></svg> </i>
						<span class="nav-text">Calendar</span>
					</a>
				   
				</li>

					
                    <li><a class="ai-icon" href="{{route('staff-booking')}}" aria-expanded="false">
							<i> <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="24" x="0" y="0" viewBox="0 0 32 32" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><g data-name="Layer 2"><path d="M27 4h-4V3a1 1 0 0 0-2 0v1H11V3a1 1 0 0 0-2 0v1H5a3 3 0 0 0-3 3v20a3 3 0 0 0 3 3h22a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3zM5 6h22a1 1 0 0 1 1 1v3H4V7a1 1 0 0 1 1-1zm22 22H5a1 1 0 0 1-1-1V12h24v15a1 1 0 0 1-1 1z" fill="#000000" opacity="1" data-original="#000000"></path><path d="m20.35 15.241-6.3 5.4-2.346-2.345a1 1 0 0 0-1.414 1.414l3 3a1 1 0 0 0 1.357.052l7-6a1 1 0 1 0-1.3-1.518z" fill="#000000" opacity="1" data-original="#000000"></path></g></g></svg>
							</i>
							<span class="nav-text">Booking</span>
						</a>
                     
                    </li>


				
				
					
				<li><a class="ai-icon" href="{{route('staff-profile')}}" aria-expanded="false">
					<i> <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="24" x="0" y="0" viewBox="0 0 24 24" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><g fill="#000" fill-rule="evenodd" clip-rule="evenodd"><path d="M12 11.667A3.333 3.333 0 1 0 12 5a3.333 3.333 0 0 0 0 6.667zm0-1A2.333 2.333 0 1 0 12 6a2.333 2.333 0 0 0 0 4.667zM7.5 10.5a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zm-1 0a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z" fill="#000000" opacity="1" data-original="#000000"></path><path d="M1.5 19v-2a3.5 3.5 0 0 1 6.428-1.918 4.5 4.5 0 0 1 8.144 0A3.5 3.5 0 0 1 22.5 17v2a.5.5 0 0 1-.5.5H2a.5.5 0 0 1-.5-.5zm1-2a2.5 2.5 0 0 1 5 0v1.5h-5zm19 1.5V17a2.5 2.5 0 0 0-5 0v1.5zm-6 0V17a3.5 3.5 0 1 0-7 0v1.5z" fill="#000000" opacity="1" data-original="#000000"></path><path d="M21.5 10.5a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zm-1 0a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z" fill="#000000" opacity="1" data-original="#000000"></path></g></g></svg></i>
					<span class="nav-text">Profile</span>
				</a>
				
			</li>

				
                    
                   
                  
                  
					

				


				
                    
                   
                </ul>
				
				<div class="copyright">
					<p><strong>Book Me</strong> © 2023 All Rights Reserved</p>
					<p>Powered By Chrisans Web Solutions</p>
				</div>
			</div>
        </div>
        <!--**********************************
            Sidebar end
        ***********************************-->
		
		<!--**********************************
            Content body start
        ***********************************-->
			@yield('content')
        <!--**********************************
            Content body end
        ***********************************-->

        <!--**********************************
            Footer start
        ***********************************-->
        <footer class="footer">
            <div class="copyright">
                  <p>Copyright © {{ date('Y') }} Designed & Developed by <a href="https://chrisansgroup.com/" target="_blank">Chrisans Web Solutions</a></p>
            </div>
        </footer>
        <!--**********************************
            Footer end
        ***********************************-->
    </div>
    <!--**********************************
        Main wrapper end
    ***********************************-->

    <!--**********************************
        Scripts
    ***********************************-->
    <!-- Required vendors -->
    <script src="{{ asset('staffpanel/vendor/global/global.min.js') }}"></script>
	<script src="{{ asset('staffpanel/vendor/bootstrap-select/js/bootstrap-select.min.js') }}"></script>
	<script src="{{ asset('staffpanel/vendor/chart-js/chart.bundle.min.js') }}"></script>
	<script src="{{ asset('staffpanel/vendor/owl-carousel/owl.carousel.js') }}"></script>
	
	<script src="{{ asset('staffpanel/vendor/jqueryui/js/jquery-ui.min.js') }}"></script>
    <script src="{{ asset('staffpanel/vendor/moment/moment.min.js') }}"></script>
    <script src="{{ asset('staffpanel/vendor/fullcalendar/js/fullcalendar.min.js') }}"></script>
    <script src="{{ asset('staffpanel/js/plugins-init/fullcalendar-init.js') }}"></script>


	
	<!-- Chart piety plugin files -->
    <script src="{{ asset('staffpanel/vendor/peity/jquery.peity.min.js') }}"></script>


    <script src="{{ asset('staffpanel/js/plugins-init/chartjs-init.js') }}"></script>
	<!-- Apex Chart -->
	<script src="{{ asset('staffpanel/vendor/apexchart/apexchart.js') }}"></script>
	
	<script src="{{ asset('js/sweetalert.min.js') }}"></script>
<script src="{{ asset('vendorpanel/vendor/pickadate/picker.js') }}"></script>
    <script src="{{ asset('vendorpanel/vendor/pickadate/picker.time.js')}}"></script>
    <script src="{{ asset('vendorpanel/vendor/pickadate/picker.date.js')}}"></script>
	<!-- Dashboard 1 -->
	 <script src="{{ asset('vendorpanel/js/plugins-init/pickadate-init.js') }}"></script>
	<script src="{{ asset('staffpanel/js/dashboard/dashboard-1.js') }}"></script>
	<script src="{{ asset('staffpanel/vendor/datatables/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('staffpanel/js/plugins-init/datatables.init.js') }}"></script>
    <script src="{{ asset('staffpanel/js/custom.min.js') }}"></script>
	<script src="{{ asset('staffpanel/js/deznav-init.js') }}"></script>

<script type="text/javascript">
      var base_url = '{{URL::to("/")}}';
</script>
	
	<script>
	var datatable1 = $('#example4').DataTable();
   $("#sort1").change(function(){
    
  
		
        var value = $(this).val();
       if(value==1)
	   {
		   $('#example4').DataTable().order([0, 'desc']).draw();
		   
	   }
	   if(value==2)
	   {
		  $('#example4').DataTable().order([4, 'desc']).draw();
		   
	   }
       
    });
	  $("#payment").change(function(){
    
  
		
        var value = $(this).val();
     
        datatable1.columns(7).search(value).draw();
    });
	
	$("#status1").change(function(){
    
  
		
        var value = $(this).val();
     
        datatable1.columns(9).search(value).draw();
    });
$(".vendorsearch1").click(function(){
    
  
		
        var value = $('#searchuser').val();
       
        datatable1.columns(2).search(value).draw();
    });
	
	
	$(".statuschange").change(function(){
			id=$(this).attr('data-id');
			val1= $('#sel1').find(":selected").val();
			
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/staff/bookstatus-update/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}",status:val1},
                success: function(msg){
                   
                    if($.trim(msg.Update)=="success")
                    {
                    swal("Success!", "Booking Status Updated Successfully", "success");
                    window.location.href="";
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
	
	
	
	
		
		$(".changestaff").change(function(){
			id=$(this).attr('data-id');
			val1= $('#sel1').find(":selected").val();
			
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/staff/staff-update/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}",staffid:val1},
                success: function(msg){
                   
                    if($.trim(msg.Update)=="success")
                    {
                    swal("Success!", "Staff Re Assign Done Successfully", "success");
                    //$('.alert').show();	
                     //$("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
			
			$("#date").change(function(){
			id=$(this).val();
			date1=$('#date').val();
			
			  $.ajax({
                url: base_url+"/stafftime/slots/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}",date1:date1},
                success: function(msg){
                   
                    if($.trim(msg.view)=="success")
                    {
                   
                   $('#booking').html(msg.slots);
				   
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			
			
			
			$.ajax({
                url: base_url+"/stafftime/slots1/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}",date1:date1},
                success: function(msg){
                   
                    if($.trim(msg.view)=="success")
                    {
                   
                   $('#booking1').html(msg.slots);
				   
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			
			
			

			
			});
			
		function carouselReview(){
			/*  testimonial one function by = owl.carousel.js */
			jQuery('.testimonial-one').owlCarousel({
				nav:true,
				loop:true,
				autoplay:true,
				margin:30,
				dots: false,
				left:true,
				navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
				responsive:{
					0:{
						items:1
					},
					484:{
						items:2
					},
					882:{
						items:3
					},	
					1200:{
						items:2
					},			
					
					1540:{
						items:3
					},
					1740:{
						items:4
					}
				}
			})			
		}
		jQuery(window).on('load',function(){
			setTimeout(function(){
				carouselReview();
			}, 1000); 
		});
		 $('#sel4').change(function(){
	  $('#calendar1').submit();
	  });
	    $( function() {
    $( "#datepicker" ).datepicker({ minDate: 1});
  } );
  @if(isset($ordertotals))
  var areaChart3 = function(){	
		//gradient area chart
		if(jQuery('#areaChart_3').length > 0 ){
			const areaChart_3 = document.getElementById("areaChart_3").getContext('2d');
			
			areaChart_3.height = 100;

			new Chart(areaChart_3, {
				type: 'line',
				data: {
					defaultFontFamily: 'Poppins',
					labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul","Aug","Sep","Oct","Nov","Dec"],
					datasets: [
						{
							label: "My First dataset",
							data: [{{$ordertotals}}],
							borderColor: 'rgb(11, 42, 151)',
							borderWidth: "1",
							tension:0.5,
							fill:true,
							backgroundColor: 'rgba(11, 42, 151, .5)'
						}
					]
				},
				options: {
					plugins:{
						legend:false,
					},
					scales: {
						y:{
							max: {{$max}}, 
							min: 0, 
							ticks: {
								beginAtZero: true, 
								stepSize: 20, 
								padding: 10
							}
						},
						x:{ 
							ticks: {
								padding: 5
							}
						}
					}
				}
			});
		}
	}
	@endif
	@if(isset($bookingdate))
var chartTimeline = function(){
		
		var optionsTimeline = {
			chart: {
				type: "bar",
				height: 320,
				stacked: true,
				toolbar: {
					show: false
				},
				sparkline: {
					
				},
				backgroundBarRadius: 5,
				offsetX: -10,
			},
			series: [
				 {
					name: "New Booking",
				 data: [{{$bookingdate}}]
				}
			],
			
			plotOptions: {
				bar: {
					columnWidth: "20%",
					endingShape: "rounded",
					colors: {
						backgroundBarColors: ['rgba(0,0,0,0)', 'rgba(0,0,0,0)', 'rgba(0,0,0,0)', 'rgba(0,0,0,0)', 'rgba(0,0,0,0)', 'rgba(0,0,0,0)', 'rgba(0,0,0,0)', 'rgba(0,0,0,0)', 'rgba(0,0,0,0)', 'rgba(0,0,0,0)'],
						backgroundBarOpacity: 1,
						backgroundBarRadius: 5,
						opacity:0
					},

				},
				distributed: true
			},
			colors:['#0B2A97', '#FF9432'],
			
			grid: {
				show: true,
			},
			legend: {
				show: false
			},
			fill: {
				opacity: 1
			},
			dataLabels: {
				enabled: false,
				colors:['#0B2A97', '#FF9432'],
				dropShadow: {
					enabled: true,
					top: 1,
					left: 1,
					blur: 1,
					opacity: 1
				}
			},
			xaxis: {
				categories: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20','21','22','23','24','25','26','27','28','29','30','31'],
				labels: {
					style: {
						colors: '#787878',
						fontSize: '13px',
						fontFamily: 'Poppins',
						fontWeight: 400
						
					},
				},
				axisTicks:{
					show:false,
					
				},
				crosshairs: {
					show: false,
				},
				axisBorder: {
					show: false,
				},
			},
			
			yaxis: {
				labels: {
					style: {
						colors: '#787878',
						fontSize: '13px',
						fontFamily: 'Poppins',
						fontWeight: 400
						
					},
				},
			},
			responsive: [{
				breakpoint: 575,
				
				options: {
					xaxis: {
						categories: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11']
					},
					
				},
			
			}],

			
			tooltip: {
				x: {
					show: true
				}
			}
    };
		var chartTimelineRender =  new ApexCharts(document.querySelector("#chartTimeline"), optionsTimeline);
		 chartTimelineRender.render();
	}
	@endif
	</script>
	@if(isset($booking))
 
	<script>
 function fullCalender(){
	
	
	
	/* initialize the external events
		-----------------------------------------------------------------*/

		var containerEl = document.getElementById('external-events');
		new FullCalendar.Draggable(containerEl, {
		  itemSelector: '.external-event',
		  eventData: function(eventEl) {
			return {
			  title: eventEl.innerText.trim()
			}
		  }
		 
		});
		/* initialize the calendar
		-----------------------------------------------------------------*/

		var calendarEl = document.getElementById('calendar');
		var calendar = new FullCalendar.Calendar(calendarEl, {
			initialView: 'dayGridMonth',
		  headerToolbar: {
			left: 'prev,next today',
			center: 'title',
			right: 'dayGridMonth,timeGridWeek,timeGridDay'
		  },
		  eventPositioned: function(info)
		 {
		      if (info.view.type === 'dayGridMonth' && info.el.classList.contains('fc-event')) {
		        var eventCount = info.event._def.extendedProps.eventCount;
		        var moreIndicator = '<div class="event-more">+' + (eventCount - 1) + ' more</div>';
		        
		        if (eventCount > 1) {
		          info.el.querySelector('.fc-event-title').insertAdjacentHTML('beforeend', moreIndicator);
		        }
		      }
		    },

		  selectable: true,
		  selectMirror: true,
		  select: function(arg) {
			var title = prompt('Event Title:');
			if (title) {
			  calendar.addEvent({
				title: title,
				start: arg.start,
				end: arg.end,
				allDay: arg.allDay
			  })
			}
			calendar.unselect()
		  },
		  
    		longPressDelay: 0,
		  editable: true,
		  droppable: true, // this allows things to be dropped onto the calendar
		  drop: function(arg) {
			// is the "remove after drop" checkbox checked?
			if (document.getElementById('drop-remove').checked) {
			  // if so, remove the element from the "Draggable Events" list
			  arg.draggedEl.parentNode.removeChild(arg.draggedEl);
			}
		  },
		  initialDate: '<?php echo date('Y-m-d'); ?>',
			  weekNumbers: true,
			  navLinks: true, // can click day/week names to navigate views
			  editable: true,
			  selectable: true,
			  nowIndicator: true,
		   events: [
		   
		    @foreach($booking as $book)
			<?php
			$status='';
			$time=explode('-',$book->service_time);
			$times1=date('H:i:s',strtotime($time[0]));
			$times2=date('H:i:s',strtotime($time[1]));
		if($book->status==1)
			{
				$status='bg-yellow';
			}
			if($book->status==1)
			{
				$status='bg-yellow';
			}
			if($book->status==2)
			{
				$status='bg-green';
			}
			if($book->status==3)
			{
				$status='bg-red';
			}
			if($book->status==4)
			{
				$status='bg-red';
			}
			?>
				{
				  title: '{{$book->name}}',
				  start: '{{date('Y-m-d',strtotime($book->service_date))}}T{{$times1}}',
				  end: '{{date('Y-m-d',strtotime($book->service_date))}}T{{$times2}}',
				  url: '{{route('staff-bookingdetail',$book->id)}}',
				  className: "{{$status}}"
				},
				@endforeach
				
			  ]
		});
		calendar.render();
		
		
	
}	
	
	
	
jQuery(window).on('load',function(){
	setTimeout(function(){
		fullCalender();	
	}, 1000);
	
	
});	

</script>
@endif
<script>
  function parseTime(t) {
    return new Date("1970/01/01 " + t);
  }

  $(document).on('change', '#sel7', function () {
    const selectedTime = parseTime($(this).val());

    // Clear existing options in #time2
    $('#sel8').empty();

    // Loop through options of #time1
    $('#sel7 option').each(function () {
      const timeVal = $(this).val();
      if (parseTime(timeVal) > selectedTime) {
        $('#sel8').append(`<option value="${timeVal}">${timeVal}</option>`);
      }
    });

    // Optional: handle no available times
    if ($('#sel8 option').length === 0) {
      $('#sel8').append('<option value="">No available time</option>');
    }
  });

  // Optional: trigger once to initialize #time2
 
</script>
<script>
 $('#clients').on('change', function () {

	 
    var clientId = $(this).val(); // Get selected client ID
    if(clientId==180)
	{
		$('#serv').hide();
		$('#loc').hide();
		$('#pay').hide();
		$('#method').hide();
		$('#totime').show();
		$('#sel2').prop('required', false);
		$('#serviceat').prop('required', false);
		$('#sel3').prop('required', false);
		$('#sel4').prop('required', false);
		$('#sel2 option').prop('selected', true).trigger('change');
	}
	else
	{
		$('#serv').show();
		$('#loc').show();
		$('#pay').show();
		$('#method').show();
		$('#totime').hide();
		$('#sel2').prop('required', true);
		$('#serviceat').prop('required', true);
		$('#sel3').prop('required', true);
		$('#sel4').prop('required', true);
		
		$('#sel2 option:selected').remove().trigger('change');
	}
    
    // You can also perform an AJAX call or other logic here
  });
</script>

</body>

</html>