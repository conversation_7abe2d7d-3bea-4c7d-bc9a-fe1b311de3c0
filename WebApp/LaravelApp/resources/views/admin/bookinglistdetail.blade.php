 @extends('admin.layout.master')

@section('content')
<div class="content-body default-height">
            <div class="container-fluid">
				<div class="row page-titles">
					<ol class="breadcrumb">
						<li class="breadcrumb-item"><a href="javascript:void(0)">Shop</a></li>
						<li class="breadcrumb-item active"><a href="javascript:void(0)">Invoice</a></li>
					</ol>
                </div>
                <div class="row">
                <div class="col-lg-6">
                <button class="btn btn-info" id="downloadPDF" data-booking-id="{{$booking->id ?? ''}}">Download PDF</button>
</div>
<div class="col-lg-6">
</div>
</div>
                <div class="row" id="content2">
                
                    <div class="col-lg-12">

                        <div class="card mt-3">
                            <div class="card-header" style="display:block;"><strong> Invoice:</strong> {{$booking->Bookid}} <span class="float-end">
                                    <strong>Status:</strong> @if($booking->status==1) Scheduled @endif @if($booking->status==2) Completed @endif @if($booking->status==3) Cancelled @endif @if($booking->status==6) Failed @endif</span> </div>
                            <div class="card-body">
                                <div class="row mb-5">
                                    <div class="mt-4 col-xl-3 col-lg-3 col-md-6 col-sm-12">
                                        <h6><strong>From:</strong></h6>
                                        <div> <strong>{{$booking->vendor_name}}</strong> </div>
                                        <div>{{$booking->Area_Title}}</div>
                                        <div>{{$booking->block}} {{$booking->street}} {{$booking->avenue}} {{$booking->zipcode}}</div>
                                        <div>Email: {{$booking->vendor_email}}</div>
                                        <div>Phone: {{$booking->vendor_phone}}</div>
                                    </div>
                                    <div class="mt-4 col-xl-3 col-lg-3 col-md-6 col-sm-12">
                                        <h6><strong>To:</strong></h6>
                                        <div> <strong>{{$booking->name}}</strong> </div>
                                     
                                        <div>{{$booking->address}}</div>
                                        <div>Email: {{$booking->email}}</div>
                                        <div>Phone: {{$booking->user_phone}}</div>
                                    </div>
                                    <div class="mt-4 col-xl-3 col-lg-3 col-md-6 col-sm-12">
                                    <strong>Payment Method:</strong> Knet <br/> Payment ID: <br/>Date: {{date('d-m-Y',strtotime($booking->bookdate))}}
                                    </div>
                                    <div class="mt-4 col-xl-3 col-lg-3 col-md-12 col-sm-12 d-flex justify-content-lg-end justify-content-md-center justify-content-xs-start">
                                        <div class="row align-items-center">
											<div class="col-sm-9"> 
												<div class="brand-logo mb-3">
													<img class="logo-abbr me-2" width="150" src="{{ asset('images/logo-full.png') }}"" alt="">
													
												</div>
                                                
                                            </div>
                                            <div class="col-sm-3 mt-3">  </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th class="center">#</th>
                                                <th>Services Name</th>
                                             
                                                <th class="right">Price</th>
                                                <th class="center">Qty</th>
                                                <th class="right">Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										@foreach($bookingdetails  as $key => $book)
                                            <tr>
                                                <td class="center">{{$key+1}}</td>
                                                <td class="left strong">{{$book->service_name}}</td>
                                               
                                                <td class="right">KWD {{$book->price}}</td>
                                                <td class="center">1</td>
                                                <td class="right">KWD {{$book->price}}</td>
                                            </tr>
											@endforeach
										
                                        </tbody>
                                    </table>
                                </div>
                                <div class="row">
                                    <div class="col-lg-4 col-sm-5"> </div>
                                    <div class="col-lg-4 col-sm-5 ms-auto">
                                        <table class="table table-clear">
                                            <tbody>
                                                <tr>
                                                    <td class="left"><strong class="text-black">Subtotal</strong></td>
                                                    <td class="right">KWD {{$booking->total_price}}</td>
                                                </tr>
                                               
                                               
                                                <tr>
                                                    <td class="left"><strong class="text-black">Total</strong></td>
                                                    <td class="right"><strong class="text-black">KWD {{$booking->total_price}}</strong>
                                                      </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
@endsection
