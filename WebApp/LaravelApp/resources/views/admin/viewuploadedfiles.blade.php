 @extends('admin.layout.master')

@section('content')
 @if(session('ViewUploadedFiles')==1)
     <div class="content-body default-height">
            <div class="container-fluid">
                <div class="page-titles justify-content-between">
					<h2> All uploaded files</h2>

				@if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif
                </div>

                <div class="row mb-5 justify-content-between">
                    
                    <div class="col-xl-2 col-lg-3 col-sm-6 col-12 ">
                        <select class="form-control default-select" id="sel1" name="delimage">
                                <option value="" selected="selected">Select</option>
                                <option value="1">Delete Image</option>
                               
                            </select>
                    </div>


                  
                    <div class="col-xl-3 col-lg-6 col-12">


                        
                    </div>

					<div class="col-md-2">
						<a href="javascript:void(0);" class="btn btn-primary btn-rounded modalpopup"> Upload New File</a>
					</div>
                </div>

                <div class="row">
				<?php
									$i=3000;
									if ($handle = opendir(public_path('uploads'))) {

								while (false !== ($entry = readdir($handle))) {
								if ($entry != "." && $entry != "..") { ?>
                    <div class="col-xl-2 col-xxl-3 col-md-4 col-sm-6">
                        <div class="card">
                            <div class="card-body product-grid-card">
                                <div class="new-arrival-product">
                                    <div class="new-arrivals-img-contnent">
                                        <div class="form-check custom-checkbox mb-3 product-check">
											<input type="checkbox" name="images1[]" class="form-check-input" value="uploads/<?php echo $entry; ?>" id="customCheckBox<?php echo $i; ?>" >
																<?php $allowedMimeTypes = ['image/jpeg','image/gif','image/png','image/bmp','image/svg+xml','image/webp'];
																
																$contentType = mime_content_type('public/uploads/'.$entry);
																
																?>
											
										</div>
                                        															<?php if(in_array($contentType, $allowedMimeTypes) ){ ?>
															<img class="img-fluid rounded" src="<?php echo asset('/');?>uploads/<?php echo $entry; ?>" alt="">
															<?php } else { ?>
															<a href="<?php echo asset('/');?>uploads/<?php echo $entry; ?>">File</a>
															<?php }?>
                                    </div>
                                    <div class="new-arrival-content text-center mt-3">
                                        <h4><?php $file=str_replace("-", " ", $entry); echo preg_replace('/\.\w+$/', '', $file);?></h4>
                                        
                                        <span class="price"><?php $size = filesize('public/uploads/'.$entry);$units = array('B', 'KB', 'MB', 'GB', 'TB');
    $formattedSize = $size; for ($i = 0; $size >= 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
        $formattedSize = round($size, 2);
    } echo $formattedSize . ' ' . $units[$i];?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
					
                  <?php
										$i++;
										   }
    }
    closedir($handle);
}
?>
					
				</div>
			</div>
		</div>
		@endif
@endsection


	