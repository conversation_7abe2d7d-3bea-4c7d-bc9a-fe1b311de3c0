 @extends('admin.layout.master')

@section('content')
 @if(session('AddStaffs')==1)
     <div class="content-body default-height">
			<div class="container-fluid">
				
				<!-- Row -->
				<div class="row">
					<div class="col-xl-12">
					
						
						<div class="row">
							<div class="col-xl-12">
								
							
								
							


							
								
								
								

								<div class="filter cm-content-box box-primary">
									<div class="content-title">
										<div class="cpa">Add Promotion Banner					
										</div>
										<div class="tools">
											<a href="javascript:void(0);" class="expand SlideToolHeader"><i class="fal fa-angle-down"></i></a>
										</div>
									</div>
									<div class="cm-content-body  form excerpt">
										<div class="card-body">
											
											<div class="row">
<form method="post" action="{{route('promotionbannerssave')}}" enctype='multipart/form-data'>
  {{csrf_field()}}
<div class="row">
@if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif
                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Promo Name <span> *</span></label>
													<input name="PromoName" value="{!! old('PromoName') !!}" required class="form-control" placeholder="Promo Name">
												</div>
												 <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Title <span> *</span></label>
													<input name="title" value="{!! old('title') !!}" required class="form-control" placeholder="Title">
												</div>
												 <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Subtitle <span> *</span></label>
													<input name="subtitle" value="{!! old('subtitle') !!}" required class="form-control" placeholder="Sub Title">
												</div>
												 <div class="col-xl-12 col-sm-12">
												<div class="cm-content-body  form excerpt">
													<div class="card-body">
														<h5 class="mb-4"><i class="fa fa-paperclip"></i> Promo Image (167px158px)<span> *</span></h5>
														
															<a href="javascript:void(0);" class="imagepicker imagepicker-add thumbnail modalpopup" data-store="photo" data-loc="photos" data-count="1" > </a>
															<div id="photos"></div>
															<input type="hidden" name="photo" id="photo" value="" />
																
															
													</div>
												</div>
												</div>

											</div>
											
											<div class="row">
												 <div class="form-group col-md-6">
													<label  class="form-label">Type</label>
													<select  class="form-control default-select" required id="promotion_type" name="promotion_type">
                                                        <option value="">Select</option>
														
											    <option value="service">Service</option>
											     <option value="vendor">Vendor</option>
													
                                                       
                                                        
                                                        </select>
                                                </div>
											
												
												 <div class="form-group col-md-6 service" style="display:none;">
													<label  class="form-label">Service</label>
													<select  class="form-control default-select"  id="sel2" name="ServiceID">
                                                        <option value="">Select</option>
														 @foreach($servicesall as  $services)
											    <option value="{{$services->ServicesID}}">{{$services->Name}}</option>
														@endforeach  
                                                       
                                                        
                                                        </select>
                                                </div>
                                                 <div class="form-group col-md-6 vendor" style="display:none;">
													<label  class="form-label">Vendor</label>
													<select  class="form-control default-select"  id="vendor_id" name="vendor_id">
                                                        <option value="">Select</option>
														 @foreach($vendors as  $vendor)
											    <option value="{{$vendor->vendor_id}}">{{$vendor->vendor_name}}</option>
														@endforeach  
                                                       
                                                        
                                                        </select>
                                                </div>

                                               
</div>
									

											</div>
                                            <div class="filter cm-content-box box-primary style-1 mb-0 border-0">
                                                <div class="content-title">
                                                    
                                                    <button type="submit" class="btn btn-primary my-2">Submit</button>
                                                </div>
                                            </div>
											</form>
										</div>
									</div>
								</div>
							</div>
						
						</div>
					</div>
				</div>
			</div>
		</div>
		@endif
@endsection


	