 @extends('admin.layout.master')

@section('content')
 @if(session('EditStaff')==1)
     <div class="content-body default-height">
			<div class="container-fluid">
				
				<!-- Row -->
				<div class="row">
					<div class="col-xl-12">
					
						
						<div class="row">
							<div class="col-xl-12">
								
							
								
							


							
								
								
								

								<div class="filter cm-content-box box-primary">
									<div class="content-title">
										<div class="cpa">Edit User						
										</div>
										<div class="tools">
											<a href="javascript:void(0);" class="expand SlideToolHeader"><i class="fal fa-angle-down"></i></a>
										</div>
									</div>
									<div class="cm-content-body  form excerpt">
										<div class="card-body">
											
											<div class="row">
<form method="post" action="{{route('update-admin-user',$user->id)}}" enctype='multipart/form-data'>
  {{csrf_field()}}
<div class="row">
@if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif
                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Name <span> *</span></label>
													<input name="name" value="{{$user->admin_name}}" required class="form-control" placeholder="Name">
												</div>
												<div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">E-mail <span> *</span></label>
													<input name="email" value="{{$user->admin_email}}" required class="form-control" placeholder="Email">
												</div>
												<div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Phone <span> *</span></label>
													<input name="phone" value="{{$user->admin_phone}}" required class="form-control" placeholder="Phone">
												</div>

                                               

												<div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Password <span> *</span></label>
													<input type="password" name="password"  class="form-control" placeholder="Password">
												</div>

                                                <div class="col-xl-12 col-sm-12">
												<div class="cm-content-body  form excerpt">
													<div class="card-body">
														<h5 class="mb-4"><i class="fa fa-paperclip"></i> Photo <span> *</span></h5>
														
														<a href="javascript:void(0);" class="imagepicker imagepicker-add thumbnail modalpopup" data-store="photo" data-loc="photos" data-count="1" > </a>
															<div id="photos"></div>
															<input type="hidden" name="photo" id="photo" value="" />
																
															<img src="{{asset($user->admin_image)}}" width="100"/>
													</div>
												</div>
												</div>

											</div>
                                            <div class="filter cm-content-box box-primary style-1 mb-0 border-0">
                                                <div class="content-title">
                                                    
                                                    <button type="submit" class="btn btn-primary my-2">Submit</button>
                                                </div>
                                            </div>
											</form>
										</div>
									</div>
								</div>
							</div>
						
						</div>
					</div>
				</div>
			</div>
		</div>
		@endif
@endsection


	