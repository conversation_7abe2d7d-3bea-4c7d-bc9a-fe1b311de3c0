 @extends('admin.layout.master')

@section('content')
@if(session('ServiceCategoryList')==1)
  <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-12">
                        <div class="card">
                            <div class="card-header justify-content-between">
                                <h4 class="card-title">Ameneties</h4>

                                <a href="{{route('amenetise')}}" class="btn btn-primary btn-rounded"> Add Ameneties</a>
                            </div>
                            <div class="card-body">
								


                                <div class="table-responsive">
                                    <table id="example3" class="display min-w850">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th>ICON</th>
                                                <th>Ameneties Name</th>
                                               

                                               
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										<?php $k=1; ?>
										@foreach($ameneties as $amenety)
										
                                            <tr id="{{$amenety->Am_ID}}">
                                                <td>{{$k}}</td>
                                                <td>
												<img src="{{asset($amenety->AmentiesImage)}}" width="100"/>
												</td>
                                                <td>   
                                                    
                                                    <a href="javascript:void()" class="btn btn-primary light btn-xs mb-1">{{$amenety->AmentiesName}}</a> 
                                                    
                                                
                                                </td>

                                                
                                                
                                                
                                                <td>
													<div class="d-flex">
                                                       @if(session('EditServiceCategory')==1)
														<a href="{{route('edit-amenties',$amenety->Am_ID)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="Edit"><i class="fa fa-pencil"></i></a>
														@endif
														 @if(session('DeleteServiceCategory')==1)
														<a href="#"  data-id="{{$amenety->Am_ID}}" class="DeleteAmenety btn btn-danger shadow btn-xs sharp" title="delete"><i class="fa fa-trash"></i></a>
														@endif
													</div>												
												</td>												
                                            </tr>
											<?php $k++; ?>
											@endforeach
                                           
                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		@endif
@endsection


	