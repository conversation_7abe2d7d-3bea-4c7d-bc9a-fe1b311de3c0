 @extends('admin.layout.master')

@section('content')
@if(session('ViewSaleReport')==1)
<style>
	.export_excel{
		float:right !important;
		margin-right:15px  !important;
	}
	.export_pdf{
		float:right !important;
		margin-right:15px  !important;
		margin-bottom:20px !important;
	}
</style>
<div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-12">
                        <div class="card">
                            <div class="card-header justify-content-between">
                                <h4 class="card-title">Sale Report</h4>

                                
                            </div>


 


                            <div class="card-body">
                                <div class="row">
						 <div class="col-xl-12 col-xxl-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Sales Analytics</h4>
                                    </div>
                                    <div class="card-body">
                                        <div id="saleBar"></div>
                                    </div>
                                </div>
                            </div>
							</div>
                                <div class="row mb-5">
                                    <div class="col-xl-3 col-lg-3 col-sm-6 col-12">
                                        <select  id="vendor-sale-select" class="form-control default-select ">
										{{!!$vendorhtml!!}}
                                            </select>
                                    </div>
                                    <div class="col-xl-3 col-lg-3 col-sm-6 col-12 ">
                                        <select class="form-control default-select" id="sort2" class="sort">
                                                <option value="" selected="selected">Sorting</option>
                                                <option value="1">Sort by Latest</option>
                                                <option value="2">Sort by A-Z</option>
                                            </select>
                                    </div>
                                    <div class="col-xl-6 col-lg-6 col-12">


                                        <div class="input-group">
                                            <input type="text" class="form-control" id="searchuser" placeholder="search users..." aria-label="Username" aria-describedby="basic-addon1">
                                            <span class="input-group-text" id="basic-addon1"><a href="javascript:void(0);" class="vendorsearch2">Search</a></span>
                                      </div>
                                    </div>
                                </div>

                               
                        

                                <div class="table-responsive">
                                    <table id="example5" class="display min-w850">
                                        <thead>
                                            <tr>
                                                
                                                <th>S.No.</th>
                                                <th>Invoice ID</th>
                                                <th>Date</th>
                                                <th>Vendor Name</th>
                                                <th>User Name</th>
                                                <th>Email</th>
                                                <th>Phone</th>
                                                <th>Total Amount</th>
                                                <th>Staus</th>
                                               
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										
										@foreach($booking as $key => $book)
										
                                            <tr>
                                                <td>{{$key+1}}</td>
                                                <td>{{$book->Bookid}}</td>
                                                <td>{{$book->bookdate}}</td>
                                                <td>{{$book->vendor_name}}</td>
                                                <td><a href="{{route('customer-profile',$book->user_id)}}">{{$book->name}}</a></td>
                                                <td><a href="{{route('customer-profile',$book->user_id)}}">{{$book->email}}</a></td>
                                                 <td><a href="{{route('customer-profile',$book->user_id)}}">{{$book->user_phone}}</a></td>
                                                <td>KWD {{$book->total_price}}</td>
                                                <td> <span class="badge light badge-success">{{$book->payment_status}}</span>  </td>
                                             
                                                <td>
													<div class="d-flex">
                                                       
														<a href="{{route('view-bookdetails',$book->orderId)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="View"><i class="fa fa-eye"></i></a>
													
													</div>												
												</td>												
                                            </tr>
										@endforeach 
                                           




                                            
                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		@endif
		<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

<script>
    function renderChartAdmin(elementId, sumsData, saleCountData, categories) {
        console.log("Rendering chart with sumsData:", sumsData);
        console.log("Rendering chart with saleCountData:", saleCountData);
        console.log("Rendering chart with categories:", categories);

        var optionsArea = {
            series: [
                {
                    name: "Sale Amount",
                    data: sumsData
                },
                {
                    name: "Total Sales",
                    data: saleCountData
                }
            ],
            chart: {
                height: 400,
                type: 'area',
                toolbar: { show: false },
                zoom: { enabled: false }
            },
            dataLabels: { enabled: false },
            stroke: {
                width: [4, 4],
                colors: ['#007BFF', '#FF5733'],
                curve: 'smooth'
            },
            xaxis: {
                categories: categories,
                labels: {
                    style: {
                        colors: '#787878',
                        fontSize: '14px',
                        fontFamily: 'Poppins',
                        fontWeight: 100,
                    },
                },
            },
            yaxis: {
                labels: {
                    offsetX: -16,
                    style: {
                        colors: '#787878',
                        fontSize: '14px',
                        fontFamily: 'Poppins',
                        fontWeight: 100,
                    },
                },
            },
            fill: {
                colors: ['rgba(0, 143, 150, 1)', 'rgba(255, 87, 51, 1)'],
                type: 'solid',
                opacity: 0.7
            },
            colors: ['#FF3282', '#FF5733'],
            grid: {
                borderColor: '#f1f1f1',
                xaxis: { lines: { show: true } }
            },
            responsive: [{
                breakpoint: 575,
                options: { chart: { height: 250 } }
            }]
        };

        var chartArea = new ApexCharts(document.querySelector(`#${elementId}`), optionsArea);
        chartArea.render();
    }

    @if(isset($ordertotalsJson))
        document.addEventListener("DOMContentLoaded", function() {
            // Parse the JSON-encoded PHP variable
            var ordertotals = {!! $ordertotalsJson !!};

            console.log("ordertotals data:", ordertotals);

            // Ensure the data structure is an array
            if (Array.isArray(ordertotals)) {
                var sumsData = ordertotals.map(item => item.sums);
                var saleCountData = ordertotals.map(item => item.sale_count);
                var categories = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

                if (sumsData.length === 12 && saleCountData.length === 12) {
                    renderChartAdmin('saleBar', sumsData, saleCountData, categories);
                } else {
                    console.error("Error: Data for sums or sale counts is incomplete.");
                }
            } else {
                console.error("Error: ordertotals is not an array. Check the PHP json_encode structure.");
            }
        });
    @endif
</script>
@endsection






	