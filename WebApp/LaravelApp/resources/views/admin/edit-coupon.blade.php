 @extends('admin.layout.master')

@section('content')
@if(session('EditPromotion')==1)
     <div class="content-body default-height">
			<div class="container-fluid">
				
				<!-- Row -->
				<div class="row">
					<div class="col-xl-12">
					
						
						<div class="row">
							<div class="col-xl-12">
								
							
								
							


							
								
								
								

								<div class="filter cm-content-box box-primary">
									<div class="content-title">
										<div class="cpa">Edit Promotion						
										</div>
										<div class="tools">
											<a href="javascript:void(0);" class="expand SlideToolHeader"><i class="fal fa-angle-down"></i></a>
										</div>
									</div>
									<div class="cm-content-body  form excerpt">
										<div class="card-body">
											
											<div class="row">
<form method="post" action="{{route('update-coupon',$coupons->coupon_id)}}" >
  {{csrf_field()}}
<div class="row">
@if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif
                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Title <span> *</span></label>
													<input name="title" required class="form-control" value="{{$coupons->coupon_name}}" placeholder="Title">
												</div>
												<div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Code <span> *</span></label>
													<input name="code" required class="form-control" value="{{$coupons->coupon_code}}" placeholder="Code">
												</div>

                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Applicable For <span> *</span></label>
													<select name="applicable" required class="form-control default-select promo-user-applicable" id="sel1">
                                                        <option value="All Users" @if($coupons->applicable=='All Users') selected @endif>All Users</option>
                                                        <option value="Specific Users" @if($coupons->applicable=='Specific Users') selected @endif>Specific Users</option>
                                                     
                                                       
                                                    </select>
												</div>

                                                <div class="col-xl-6 col-sm-12 mb-3 usersdiv" style="display: @if($coupons->applicable=='Specific Users') block @else none @endif;">
													<label class="form-label">Users <span> *</span></label>
													<select name="users"   class="form-control default-select" id="single-select" style="width:100%">
													<option value="">Select</option>
                                                       @foreach($users as $user)
													   <option value="{{$user->id}}" @if($user->id==$coupons->user_id) selected @endif>{{$user->name}}</option>
													   @endforeach
                                                     
                                                       
                                                    </select>
												</div>
												
												  <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Applicable For Vendors <span> *</span></label>
													<select name="applicable_vendor" required class="form-control promo-venors-applicable" id="sel1" >
                                                        <option value="0" @if($coupons->available_for_vendors=='0') selected @endif>All Vendors</option>
                                                        <option value="1" @if($coupons->available_for_vendors=='1') selected @endif>Specific Vendors</option>
                                                     
                                                       
                                                    </select>
												</div>
											
												  <div class="col-xl-6 col-sm-12 mb-3 vendors" style="display: @if($coupons->available_for_vendors=='1') block @else none @endif;">
													<label class="form-label">Vendors <span> *</span></label>
													<select name="vendors[]"  class="form-control multi-select" id="sel1" multiple style="width:100%">
													<option value="">Select</option>
                                                       @foreach($vendors as $vendor)
													   <option value="{{$vendor->vendor_id}}" @if(in_array($vendor->vendor_id,explode(',', $coupons->available_vendors))) selected @endif>{{$vendor->vendor_name}}</option>
													   @endforeach
                                                     
                                                       
                                                    </select>
												</div>
												
												<div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Start Date <span> *</span></label>
													<input name="startdate" value="{{$coupons->start_date}}" required class="datepicker-default form-control" id="datepicker">
												</div>

												<div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">End Date <span> *</span></label>
													<input name="enddate" value="{{$coupons->end_date}}" required class="datepicker-default form-control" id="datepicker1">
												</div>
												
												  <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Offer Type <span> *</span></label>
													<select name="offertype" value="{$coupons->type}}" required class="form-control default-select" id="sel1">
                                                      
                                                        <option>Fixed Amount</option>
                                                        <option>A percent amount discount</option>
                                                    
                                                     
                                                       
                                                    </select>
												</div>


												<div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Condition <span> *</span></label>
													<input name="amount" value="{{$coupons->amount}}" required class="form-control" placeholder="Amount or Percentage">
												</div>

                                              

											</div>
                                            <div class="filter cm-content-box box-primary style-1 mb-0 border-0">
                                                <div class="content-title">
                                                    
                                                    <button type="submit" class="btn btn-primary my-2">Submit</button>
                                                </div>
                                            </div>
											</form>
										</div>
									</div>
								</div>
							</div>
						
						</div>
					</div>
				</div>
			</div>
		</div>
		@endif
@endsection


	