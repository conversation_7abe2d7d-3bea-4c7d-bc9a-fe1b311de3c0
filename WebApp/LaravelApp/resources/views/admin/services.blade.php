 @extends('admin.layout.master')

@section('content')
 @if(session('ServicesList')==1)
 <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-12">
                        <div class="card">
                            <div class="card-header justify-content-between">
                                <h4 class="card-title">Services</h4>

                                <a href="{{route('add-service')}}" class="btn btn-primary btn-rounded"> Add Service</a>
                            </div>
                            <div class="card-body">
								 <div class="row mb-5">
                                   
                                  
                                    <div class="col-xl-6 col-lg-6 col-12">


                                        <div class="input-group">
                                            <input type="text" id="searchcategory"  class="form-control" placeholder="search Category..." aria-label="Category" aria-describedby="basic-addon1">
                                            <span class="input-group-text" id="basic-addon1" ><a class="categorysearch3" href="javascript:void(0);">Search</a></span>
                                      </div>
                                    </div>
                                </div>


                                <div class="table-responsive">
                                    <table id="example10" class="display min-w850">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th>Service Name </th>
                                                <th>Service category</th>
                                               

                                               
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										<?php $k=1; ?>
										@foreach($services as $service)
                                            <tr id="{{$service->Services_ID}}">
                                                <td>{{$k}}</td>
                                                <td> <a href="javascript:void()" class="btn btn-primary light btn-xs mb-1">{{$service->Services_Name}}</a> 
												</td>
                                                <td>   
                                                    
                                                    <a href="javascript:void()" class="btn btn-primary light btn-xs mb-1">{{$service->Cat_Name}}</a> 
                                                    
                                                
                                                </td>

                                               
                                                
                                                
                                                <td>
													<div class="d-flex">
                                                       @if(session('EditServices')==1)
														<a href="{{route('editservice',$service->Services_ID)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="Edit"><i class="fa fa-pencil"></i></a>
														@endif
														 @if(session('DeleteServices')==1)
														<a href="#"  data-id="{{$service->Services_ID}}" class="Deleteservices btn btn-danger shadow btn-xs sharp" title="delete"><i class="fa fa-trash"></i></a>
														@endif
													</div>												
												</td>												
                                            </tr>
											<?php $k++; ?>
											@endforeach
                                           
                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		@endif
@endsection


	