@extends('admin.layout.master')

@section('content')
@if(session('SMTP')==1)
<div class="content-body default-height">
			<div class="container-fluid">
				
				<!-- Row -->
				<div class="row">
					<div class="col-xl-12">
					
						
						<div class="row">
							<div class="col-xl-6">
								

								<div class="filter cm-content-box box-primary">
									<div class="content-title">
										<div class="cpa">SMTP Settings					
										</div>
										<div class="tools">
											<a href="javascript:void(0);" class="expand SlideToolHeader"><i class="fal fa-angle-down"></i></a>
										</div>
									</div>
									<div class="cm-content-body  form excerpt">
										<div class="card-body">
										@if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif
										 @if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
											<form class="user" method="post" action="{{route('smtp-save')}}">
				     {{csrf_field()}}
											<div class="row">
                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Type <span> *</span></label>
													<select class="form-control default-select" id="sel1" name="type" required>
                                                        <option @if($smtp->type=='Sendmail')  selected   @endif >Sendmail</option>
                                                        <option @if($smtp->type=='SMTP')  selected   @endif>SMTP</option>
                                                        <option @if($smtp->type=='Mailgun')  selected   @endif>Mailgun</option>
                                                       
                                                    </select>
												</div>
                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">MAIL HOST <span> *</span></label>
													<input name="mail_host" class="form-control" placeholder="MAIL HOST" required value="{{$smtp->mail_host}}">
												</div>

                                              

                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">MAIL PORT <span> *</span></label>
													<input type="number" name="mail_port" class="form-control" placeholder="MAIL PORT" required value="{{$smtp->mail_port}}">
												</div>

                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">MAIL USERNAME <span> *</span></label>
													<input type="text" name="mail_username" class="form-control" placeholder="MAIL USERNAME" required value="{{$smtp->mail_username}}">
												</div>

                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">MAIL PASSWORD <span> *</span></label>
													<input type="password" name="mail_password" class="form-control" placeholder="MAIL PASSWORD" required value="{{$smtp->mail_password}}">
												</div>

                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">MAIL ENCRYPTION <span> *</span></label>
													<input type="text" name="mail_encryption" class="form-control" placeholder="MAIL ENCRYPTION" required value="{{$smtp->mail_encryption}}">
												</div>
												
                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">MAIL FROM ADDRESS <span> *</span></label>
													<input type="text" name="mail_from_address" class="form-control" placeholder="MAIL FROM ADDRESS" required value="{{$smtp->mail_from_address}}">
												</div>
                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">MAIL FROM NAME <span> *</span></label>
													<input type="text" name="mail_from_name" class="form-control" placeholder="MAIL FROM NAME" required value="{{$smtp->mail_from_name}}">
												</div>

											

											</div>
                                            <div class="filter cm-content-box box-primary style-1 mb-0 border-0">
                                                <div class="content-title">
                                                    
                                                    <button type="submit" class="btn btn-primary my-2">Save Configuration</button>
                                                </div>
                                            </div>
											</form>
										</div>
									</div>
								</div>
							</div>


                            <div class="col-xl-6">
								<div class="right-sidebar-sticky">
									<div class="filter cm-content-box box-primary">
										<div class="content-title">
											<div class="cpa">
												Test SMTP configuration
											</div>
											
										</div>
										<div class="cm-content-body  publish-content form excerpt">
											<div class="card-body pb-0">
												
												
                                                <div class="row mb-5">
                                  
                                                    <div class="col-xl-8 col-xxl-12">
                
                                                       
                                                            <input type="text" class="form-control" id="search" name="search" placeholder="<EMAIL>" onkeyup="sort_products()">
                                                        
                                                    </div>
                
                                               
                                
                                                    <div class="col-xl-4 col-xxl-12">
                                                        <a href="#" class="btn btn-primary btn-rounded"> Send test email</a>
                                                    
                                                        </div>
                                
                                                      
                
                                                        
                                                </div>
											
											
											
											</div>
											
										</div>
									</div>


                                    <div class="filter cm-content-box box-primary">
										<div class="content-title">
											<div class="cpa">
                                                Instruction
											</div>
											
										</div>
										<div class="cm-content-body publish-content form excerpt">
											<div class="card-body">
												<div class="p-3 mb-3">

                                                    <p class="text-danger"> Please be carefull when you are configuring SMTP. For incorrect configuration you will get error at the time of order place, new registration, sending newsletter.</p>


                                                    <h4> For Non-SSL</h4>

                                                    <ul class="list-icons">
                                                        <li> <span class="align-middle me-2"><i class="fa fa-check text-info"></i></span>
                                                            Select sendmail for Mail Driver if you face any issue after configuring smtp as Mail Driver
                                                        </li>

                                                        <li> <span class="align-middle me-2"><i class="fa fa-check text-info"></i></span>
                                                            Set Mail Host according to your server Mail Client Manual Settings
                                                        </li>

                                                        <li> <span class="align-middle me-2"><i class="fa fa-check text-info"></i></span>
                                                            Set Mail port as 587
                                                        </li>

                                                        <li> <span class="align-middle me-2"><i class="fa fa-check text-info"></i></span>
                                                            Set Mail Encryption as ssl if you face issue with tls
                                                        </li>

                                                        
                                                    </ul>


                                                    
                                                    <h4> For SSL</h4>

                                                    <ul class="list-icons">
                                                        <li> <span class="align-middle me-2"><i class="fa fa-check text-info"></i></span>
                                                            Select sendmail for Mail Driver if you face any issue after configuring smtp as Mail Driver
                                                        </li>

                                                        <li> <span class="align-middle me-2"><i class="fa fa-check text-info"></i></span>
                                                            Set Mail Host according to your server Mail Client Manual Settings
                                                        </li>

                                                        <li> <span class="align-middle me-2"><i class="fa fa-check text-info"></i></span>
                                                            Set Mail port as 465
                                                        </li>

                                                        <li> <span class="align-middle me-2"><i class="fa fa-check text-info"></i></span>
                                                            Set Mail Encryption as ssl
                                                        </li>

                                                        
                                                    </ul>
												
													
												</div>
											
											</div>
												
										</div>
									</div>

								
									
								
								</div>
							</div>
						
						</div>
					</div>
				</div>
			</div>
		</div>
		@endif
		@endsection