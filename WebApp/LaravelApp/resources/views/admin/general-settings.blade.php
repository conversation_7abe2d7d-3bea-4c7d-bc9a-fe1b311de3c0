 @extends('admin.layout.master')

@section('content')
@if(session('SettingsGeneral')==1)	
 <div class="content-body default-height">
			<div class="container-fluid">
				
				<!-- Row -->
				<div class="row">
					<div class="col-xl-12">
					
						
						<div class="row justify-content-center">
							<div class="col-xl-8">
								@if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif

  <form method="post" action="{{route('settings-save')}}" enctype='multipart/form-data'>
  {{csrf_field()}}

								<div class="filter cm-content-box box-primary">
									<div class="content-title">
										<div class="cpa">General Settings				
										</div>
										<div class="tools">
											<a href="javascript:void(0);" class="expand SlideToolHeader"><i class="fal fa-angle-down"></i></a>
										</div>
									</div>
									<div class="cm-content-body  form excerpt">
										<div class="card-body">
											
											<div class="row">

                                                <div class="col-xl-12 col-sm-12 mb-3">
													<label class="form-label">System Name <span> *</span></label>
													<input name="Name" class="form-control" required placeholder="System Name" value="{{$settings->Name}}">
												</div>


                                                <div class="col-xl-12 col-sm-12 mb-3">
													<label class="form-label">System Logo - White <span> *</span></label>
													<input class="form-control" type="file" id="LogoWhite" name="LogoWhite">

                                                <div class="col-md-3 mt-3">
                                                    <div class="card mb-3">
                                                        <img class="card-img-top img-fluid p-3" src="{{asset($settings->LogoWhite)}}" alt="Card image cap">
                                                       
                                                        <div class="card-body">
                                                            <p class="card-text">{{$settings->LogoWhite}}<br>
                                                               </p>
                                                          
                                                        </div>
                                                    </div>
                                                </div>

                                                <p class="small"> Will be used in admin panel side menu</p>
												</div>

                                                <div class="col-xl-12 col-sm-12 mb-3">
													<label class="form-label">System Logo - Black <span> *</span></label>
													<input class="form-control" type="file" id="LogoBlack" name="LogoBlack">

                                                <div class="col-md-3 mt-3">
                                                    <div class="card mb-3">
                                                        <img class="card-img-top img-fluid p-3" src="{{asset($settings->LogoBlack)}}" alt="Card image cap">
                                                       
                                                        <div class="card-body">
                                                            <p class="card-text">{{$settings->LogoBlack}}<br>
                                                                </p>
                                                          
                                                        </div>
                                                    </div>
                                                </div>

                                                <p class="small"> Will be used in admin panel topbar in mobile + Admin login page</p>
												</div>
                                               

                                              

                                                <div class="col-xl-12 col-sm-12 mb-3">
													<label class="form-label">System Timezone <span> *</span></label>
                                                    <select class="form-control default-select" required id="TimeZone" name="TimeZone">
                                                        <option  value="">Select Timezone</option>
														@if($settings->TimeZone!='')
														 <option  value="{{$settings->TimeZone}}" selected>{{$settings->TimeZone}}</option>
													 @endif
                                                       <option value="Pacific/Midway">(GMT-11:00) Midway Island, Samoa</option>
<option value="America/Adak">(GMT-10:00) Hawaii-Aleutian</option>
<option value="Etc/GMT+10">(GMT-10:00) Hawaii</option>
<option value="Pacific/Marquesas">(GMT-09:30) Marquesas Islands</option>
<option value="Pacific/Gambier">(GMT-09:00) Gambier Islands</option>
<option value="America/Anchorage">(GMT-09:00) Alaska</option>
<option value="America/Ensenada">(GMT-08:00) Tijuana, Baja California</option>
<option value="Etc/GMT+8">(GMT-08:00) Pitcairn Islands</option>
<option value="America/Los_Angeles">(GMT-08:00) Pacific Time (US & Canada)</option>
<option value="America/Denver">(GMT-07:00) Mountain Time (US & Canada)</option>
<option value="America/Chihuahua">(GMT-07:00) Chihuahua, La Paz, Mazatlan</option>
<option value="America/Dawson_Creek">(GMT-07:00) Arizona</option>
<option value="America/Belize">(GMT-06:00) Saskatchewan, Central America</option>
<option value="America/Cancun">(GMT-06:00) Guadalajara, Mexico City, Monterrey</option>
<option value="Chile/EasterIsland">(GMT-06:00) Easter Island</option>
<option value="America/Chicago">(GMT-06:00) Central Time (US & Canada)</option>
<option value="America/New_York">(GMT-05:00) Eastern Time (US & Canada)</option>
<option value="America/Havana">(GMT-05:00) Cuba</option>
<option value="America/Bogota">(GMT-05:00) Bogota, Lima, Quito, Rio Branco</option>
<option value="America/Caracas">(GMT-04:30) Caracas</option>
<option value="America/Santiago">(GMT-04:00) Santiago</option>
<option value="America/La_Paz">(GMT-04:00) La Paz</option>
<option value="Atlantic/Stanley">(GMT-04:00) Faukland Islands</option>
<option value="America/Campo_Grande">(GMT-04:00) Brazil</option>
<option value="America/Goose_Bay">(GMT-04:00) Atlantic Time (Goose Bay)</option>
<option value="America/Glace_Bay">(GMT-04:00) Atlantic Time (Canada)</option>
<option value="America/St_Johns">(GMT-03:30) Newfoundland</option>
<option value="America/Araguaina">(GMT-03:00) UTC-3</option>
<option value="America/Montevideo">(GMT-03:00) Montevideo</option>
<option value="America/Miquelon">(GMT-03:00) Miquelon, St. Pierre</option>
<option value="America/Godthab">(GMT-03:00) Greenland</option>
<option value="America/Argentina/Buenos_Aires">(GMT-03:00) Buenos Aires</option>
<option value="America/Sao_Paulo">(GMT-03:00) Brasilia</option>
<option value="America/Noronha">(GMT-02:00) Mid-Atlantic</option>
<option value="Atlantic/Cape_Verde">(GMT-01:00) Cape Verde Is.</option>
<option value="Atlantic/Azores">(GMT-01:00) Azores</option>
<option value="Europe/Belfast">(GMT) Greenwich Mean Time : Belfast</option>
<option value="Europe/Dublin">(GMT) Greenwich Mean Time : Dublin</option>
<option value="Europe/Lisbon">(GMT) Greenwich Mean Time : Lisbon</option>
<option value="Europe/London">(GMT) Greenwich Mean Time : London</option>
<option value="Africa/Abidjan">(GMT) Monrovia, Reykjavik</option>
<option value="Europe/Amsterdam">(GMT+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna</option>
<option value="Europe/Belgrade">(GMT+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague</option>
<option value="Europe/Brussels">(GMT+01:00) Brussels, Copenhagen, Madrid, Paris</option>
<option value="Africa/Algiers">(GMT+01:00) West Central Africa</option>
<option value="Africa/Windhoek">(GMT+01:00) Windhoek</option>
<option value="Asia/Beirut">(GMT+02:00) Beirut</option>
<option value="Africa/Cairo">(GMT+02:00) Cairo</option>
<option value="Asia/Gaza">(GMT+02:00) Gaza</option>
<option value="Africa/Blantyre">(GMT+02:00) Harare, Pretoria</option>
<option value="Asia/Jerusalem">(GMT+02:00) Jerusalem</option>
<option value="Europe/Minsk">(GMT+02:00) Minsk</option>
<option value="Asia/Damascus">(GMT+02:00) Syria</option>
<option value="Europe/Moscow">(GMT+03:00) Moscow, St. Petersburg, Volgograd</option>
<option value="Asia/Kuwait">(GMT+03:00) Kuwait</option>
<option value="Africa/Addis_Ababa">(GMT+03:00) Nairobi</option>
<option value="Asia/Tehran">(GMT+03:30) Tehran</option>
<option value="Asia/Dubai">(GMT+04:00) Abu Dhabi, Muscat</option>
<option value="Asia/Yerevan">(GMT+04:00) Yerevan</option>
<option value="Asia/Kabul">(GMT+04:30) Kabul</option>
<option value="Asia/Yekaterinburg">(GMT+05:00) Ekaterinburg</option>
<option value="Asia/Tashkent">(GMT+05:00) Tashkent</option>
<option value="Asia/Kolkata">(GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi</option>
<option value="Asia/Katmandu">(GMT+05:45) Kathmandu</option>
<option value="Asia/Dhaka">(GMT+06:00) Astana, Dhaka</option>
<option value="Asia/Novosibirsk">(GMT+06:00) Novosibirsk</option>
<option value="Asia/Rangoon">(GMT+06:30) Yangon (Rangoon)</option>
<option value="Asia/Bangkok">(GMT+07:00) Bangkok, Hanoi, Jakarta</option>
<option value="Asia/Krasnoyarsk">(GMT+07:00) Krasnoyarsk</option>
<option value="Asia/Hong_Kong">(GMT+08:00) Beijing, Chongqing, Hong Kong, Urumqi</option>
<option value="Asia/Irkutsk">(GMT+08:00) Irkutsk, Ulaan Bataar</option>
<option value="Australia/Perth">(GMT+08:00) Perth</option>
<option value="Australia/Eucla">(GMT+08:45) Eucla</option>
<option value="Asia/Tokyo">(GMT+09:00) Osaka, Sapporo, Tokyo</option>
<option value="Asia/Seoul">(GMT+09:00) Seoul</option>
<option value="Asia/Yakutsk">(GMT+09:00) Yakutsk</option>
<option value="Australia/Adelaide">(GMT+09:30) Adelaide</option>
<option value="Australia/Darwin">(GMT+09:30) Darwin</option>
<option value="Australia/Brisbane">(GMT+10:00) Brisbane</option>
<option value="Australia/Hobart">(GMT+10:00) Hobart</option>
<option value="Asia/Vladivostok">(GMT+10:00) Vladivostok</option>
<option value="Australia/Lord_Howe">(GMT+10:30) Lord Howe Island</option>
<option value="Etc/GMT-11">(GMT+11:00) Solomon Is., New Caledonia</option>
<option value="Asia/Magadan">(GMT+11:00) Magadan</option>
<option value="Pacific/Norfolk">(GMT+11:30) Norfolk Island</option>
<option value="Asia/Anadyr">(GMT+12:00) Anadyr, Kamchatka</option>
<option value="Pacific/Auckland">(GMT+12:00) Auckland, Wellington</option>
<option value="Etc/GMT-12">(GMT+12:00) Fiji, Kamchatka, Marshall Is.</option>
<option value="Pacific/Chatham">(GMT+12:45) Chatham Islands</option>
<option value="Pacific/Tongatapu">(GMT+13:00) Nuku'alofa</option>
<option value="Pacific/Kiritimati">(GMT+14:00) Kiritimati</option>
                                                    </select> 
												</div>
                                                

                                                <div class="col-xl-12 col-sm-12 mb-3">
													<label class="form-label">Admin login page background <span> *</span></label>
													<input class="form-control" required type="file" id="Page_Background" name="Page_Background" required>

                                                <div class="col-md-3 mt-3">
                                                    <div class="card mb-3">
                                                        <img class="card-img-top img-fluid p-3" src="{{asset($settings->Page_Background)}}" alt="Card image cap">
                                                       
                                                        <div class="card-body">
                                                            <p class="card-text">{{$settings->Page_Background}}<br>
                                                              </p>
                                                          
                                                        </div>
                                                    </div>
                                                </div>

                                              
												</div>
                                                <h5 class="mb-3 mt-3"> Refer & Earn Settings</h5>
                                                <hr>

                                                <div class="col-xl-12 col-sm-12 mb-3">
													<label class="form-label">Refer & Earn Status? <span> *</span></label>
                                                    <div class="material-switch justify-content-center">
                                                        <input id="Refer" name="Refer" type="checkbox" value="1" @if($settings->Refer) checked @endif />
                                                        <label for="Refer" class="label-default"></label>
                                                    </div>
												</div>

                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Minimum Refer & Earn Order Amount <span> *</span></label>
													<input name="Minimum_Refer" required="Minimum_Refer" value="{{$settings->Minimum_Refer}}" required class="form-control" placeholder="Minimum Refer & Earn Order Amount">
												</div>

                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Refer & Earn Bonus ( OR %) <span> *</span></label>
													<input name="Earn_Bonus" required class="form-control" value="{{$settings->Earn_Bonus}}" placeholder="Refer & Earn Bonus ( OR %)">
												</div>
												
                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Refer & Earn Method <span> *</span></label>
                                                    <select class="form-control default-select" required id="Refer_EarnMethod" name="Refer_EarnMethod">
                                                        <option  value="">Select</option>
                                                        <option value="Percentage" @if($settings->Refer_EarnMethod=='Percentage') selected @endif>Percentage</option>
                                                        <option value="Amount"  @if($settings->Refer_EarnMethod=='Amount') selected @endif>Amount</option>
                                                       
                                                    </select> 
												</div>
                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Maximum Refer & Earn Amount <span> *</span></label>
													<input name="MaximumRefer_Earn" required class="form-control" value="{{$settings->MaximumRefer_Earn}}" placeholder="Maximum Refer & Earn Amount">
												</div>
                                                <h5 class="mb-3 mt-3"> Refer & Earn Settings</h5>
                                                <hr>
 <div class="row align-items-center">

    <div class="col-xl-4 col-sm-12 mb-3">
        <label class="form-label">Set Time for Cancel Request <span> *</span></label>
        </div>
                                                <div class="col-xl-4 col-sm-12 mb-3">
													
													<input name="SetTime_Cancel" value="{{$settings->SetTime_Cancel}}" required class="form-control" placeholder="1">
												</div>

                                                <div class="col-xl-4 col-sm-12 mb-3">
												
													<input name="Settime_Intervel" value="{{$settings->Settime_Intervel}}" required class="form-control" placeholder="Hour">
												</div>

                                            </div>


                                            <h5 class="mb-3 mt-3"> Disable Cash On Delivery</h5>
                                            <hr>
<div class="row align-items-center">

<div class="col-xl-4 col-sm-12 mb-3">
    <label class="form-label">Set order amount <span> *</span></label>
    </div>
                                            <div class="col-xl-4 col-sm-12 mb-3">
                                                
                                                <input name="SetOrder_Amount" value="{{$settings->SetOrder_Amount}}" required class="form-control" placeholder="20">
                                            </div>

                                            <div class="col-xl-4 col-sm-12 mb-3">
                                            
                                                <input name="SetOrder_Currency"  value="{{$settings->SetOrder_Currency}}" required class="form-control" placeholder="amount">
                                            </div>

                                        </div>


                                            </div>

											

											</div>
                                            <div class="filter cm-content-box box-primary style-1 mb-0 border-0">
                                                <div class="content-title">
                                                    
                                                    <button type="submit" class="btn btn-primary my-2">Update</button>
                                                </div>
                                            </div>
										</div>
									</div>
								</div>
							</div>

</form>
                           
						
						</div>
					</div>
				</div>
			</div>
		</div>
		@endif
@endsection


	