 @extends('admin.layout.master')

@section('content')
	@if(session('Areas')==1)
 <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-8">
                        <div class="card">
                            <div class="card-header justify-content-between">
                                <h4 class="card-title">Splash Screen
                                </h4>
                               

                               
                            </div>





                            <div class="card-body">
                                <div class="row mb-5 justify-content-end align-items-end">
                                  
                                 
                                
                
                                   
                                </div>

                               
                        

                                <div class="table-responsive">
                                    <table  class="table" id="table">
                                        <thead class="thead-info">
                                            <tr>
                                                <th></th>
                                                <th>Splash Title</th>
                                                <th>Splash Image</th>
                                                
                                                <th>Status</th>
												<th>Options</th>
                                                
                                            </tr>
                                        </thead>
                                        <tbody>
										@foreach($splashscreens as $key => $splashscreen)
                                            <tr id="{{$splashscreen->splash_id}}">
                                                <td>{{$key+1}}</td>
                                                <td>{{$splashscreen->splash_title}}</td>
                                                
                                             <td><img src="{{asset($splashscreen->splash_image)}}" class="featimage"></td>
                                                <td>
                                                    <div class="material-switch">
                                                        <input id="someSwitchOptionDefault02{{$splashscreen->splash_id}}" class="enablesplash" data-id="{{$splashscreen->splash_id}}" name="enable" value="{{$splashscreen->enabled}}" @if($splashscreen->enabled==1) checked @endif type="checkbox"/>
                                                        <label for="someSwitchOptionDefault02{{$splashscreen->splash_id}}" class="label-default"></label>
                                                    </div>
												  </td>
												  <td>
												  
                                                    <div class="d-flex">
                                                     
														<a href="{{route('edit-splash-screen',$splashscreen->splash_id)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="Edit"><i class="fa fa-pencil"></i></a>
													
														<a href="#" data-id="{{$splashscreen->splash_id}}" class="deletesplash btn btn-danger shadow btn-xs sharp" title="delete"><i class="fa fa-trash"></i></a>
													</div>		
												</td>
                                             
												
											
                                            </tr>
@endforeach  

                                           


                                          
                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="col-xl-4">
								

                        <div class="filter cm-content-box box-primary">
                            <div class="content-title">
                                <div class="cpa">Edit New Splash Screen		
                                </div>
                                <div class="tools">
                                    <a href="javascript:void(0);" class="expand SlideToolHeader"><i class="fal fa-angle-down"></i></a>
                                </div>
                            </div>
                            <div class="cm-content-body  form excerpt">
                                <div class="card-body">
                                    @if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif

  <form method="post" action="{{route('splash-update',$splashscreensedit->splash_id)}}">
  {{csrf_field()}}
                                    <div class="row">

                                        <div class="col-xl-12 col-sm-12 mb-3">
                                            <label class="form-label">Splash Title<span> *</span></label>
                                            <input type="text" required name="splash_title" class="form-control" value="{{$splashscreensedit->splash_title}}" placeholder="Splash Title">
                                        </div>
                                       <div class="col-xl-12 col-sm-12 mb-3">
                                            <label class="form-label">Splash Title (Arabic)<span> *</span></label>
                                            <input type="text" required name="splash_titlear" class="form-control" value="{{$splashscreensedit->splash_titlear}}" placeholder="Splash Title">
                                        </div>
                                       <div class="col-xl-12 col-sm-12 mb-3">
                                            <label class="form-label">Splash Description<span> *</span></label>
											<textarea name="splash_description" class="form-control"  placeholder="Splash Description">{{$splashscreensedit->splash_description}}</textarea>
                                            
                                        </div>
										 <div class="col-xl-12 col-sm-12 mb-3">
                                            <label class="form-label">Splash Description (Arabic)<span> *</span></label>
											<textarea name="splash_descriptionar" class="form-control"  placeholder="Splash Description">{{$splashscreensedit->splash_descriptionar}}</textarea>
                                            
                                        </div>
 <div class="col-xl-12 col-sm-12 mb-3">
  <label class="form-label">Splash Image (359px x 303px)<span> *</span></label>
                                      <div class="cm-content-body  publish-content form excerpt">
									  <img src="{{asset($splashscreensedit->splash_image)}}" class="featimage">
											<a href="javascript:void(0);" class="imagepicker imagepicker-add thumbnail modalpopup" data-store="splash_imagesfeatured" data-loc="splash_imagefeatured" data-count="1" > </a>
															<div id="splash_imagefeatured"></div>
															<input type="hidden" name="splash_imagesfeatured" id="splash_imagesfeatured" value="" />
										</div>

                                        </div>

                                       <div class="col-xl-12 col-sm-12 mb-3">
                                            <label class="form-label">Order<span> *</span></label>
                                            <input type="number" required name="order1" class="form-control" value="{{$splashscreensedit->order1}}" placeholder="Order">
                                        </div>

                                        
                                    

                                    </div>
                                    <div class="filter cm-content-box box-primary style-1 mb-0 border-0">
                                        <div class="content-title">
                                            
                                            <button type="submit" class="btn btn-primary my-2">Save</button>
                                        </div>
                                    </div>
									</form>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		@endif
@endsection


	