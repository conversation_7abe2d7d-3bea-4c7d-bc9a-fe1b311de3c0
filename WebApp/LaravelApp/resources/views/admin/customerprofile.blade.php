 @extends('admin.layout.master')

@section('content')
    <div class="content-body default-height">
            <div class="container-fluid">
           
                <!-- row -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="profile card card-body px-3 pt-3 pb-0">
                            <div class="profile-head">
                              
                                <div class="profile-info">
									<div class="profile-photo">
										@if($user->image!=null)<img src="{{asset($user->image)}}" class="img-fluid rounded-circle" alt="">@endif
									</div>
									<div class="profile-details">
										<div class="profile-name px-3 pt-2">
											<h4 class="text-primary mb-0">{{$user->name}}</h4>
											<p>{{$user->address}}</p>
										</div>
										<div class="profile-email px-2 pt-2">
											<h4 class="text-muted mb-0">{{$user->email}}</h4>
											<p>{{$user->user_phone}}</p>
										</div>
										<div class="dropdown ms-auto">
											<a href="#" class="btn btn-primary light sharp" data-bs-toggle="dropdown" aria-expanded="true"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="18px" height="18px" viewBox="0 0 24 24" version="1.1"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><rect x="0" y="0" width="24" height="24"></rect><circle fill="#000000" cx="5" cy="12" r="2"></circle><circle fill="#000000" cx="12" cy="12" r="2"></circle><circle fill="#000000" cx="19" cy="12" r="2"></circle></g></svg></a>
											<ul class="dropdown-menu dropdown-menu-end">
												
												<li class="dropdown-item"><a href="javascript:void(0);" class="text-danger"><i class="fa fa-ban text-danger me-2"></i> Block</a></li>
											</ul>
										</div>
									</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    
                    <div class="col-xl-12">
                        <div class="card h-auto">
                            <div class="card-body">
                                <div class="profile-tab">
                                    <div class="custom-tab-1">
                                        <ul class="nav nav-tabs">
                                          
                                            <li class="nav-item"><a href="#about-me" data-bs-toggle="tab" class="nav-link active show">About Me</a>
                                            </li>
                                            <li class="nav-item"><a href="#profile-settings" data-bs-toggle="tab" class="nav-link">Booked Vendor</a>
                                            </li>

                                            
                                        </ul>
                                        <div class="tab-content">
                                           
                                            <div id="about-me" class="tab-pane fade active show">
                                                <div class="profile-about-me">
                                                    <div class="pt-4 border-bottom-1 pb-3">
                                                        <h4 class="text-primary">About Me</h4>
														{{$user->aboutme}}
                                                    </div>
                                                </div>
                                               <!-- <div class="profile-skills mb-5">
                                                    <h4 class="text-primary mb-2">Skills</h4>
                                                    <a href="javascript:void()" class="btn btn-primary light btn-xs mb-1">Admin</a>
                                                    <a href="javascript:void()" class="btn btn-primary light btn-xs mb-1">Dashboard</a>
                                                    <a href="javascript:void()" class="btn btn-primary light btn-xs mb-1">Photoshop</a>
                                                    <a href="javascript:void()" class="btn btn-primary light btn-xs mb-1">Bootstrap</a>
                                                    <a href="javascript:void()" class="btn btn-primary light btn-xs mb-1">Responsive</a>
                                                    <a href="javascript:void()" class="btn btn-primary light btn-xs mb-1">Crypto</a>
                                                </div>-->
                                                
                                               
                                            </div>



                                            <div id="profile-settings" class="tab-pane fade">
                                                <div class="pt-3">
                                                    <div class="col-12">
                                                        <div class="card bg-transparent border-0">
                                                           
                                                            <div class="card-body">
                                                                <div class="table-responsive">
                                                                    <table id="example4" class="display min-w850">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th>BOOKING ID</th>
                                                <th>USER NAME</th>
                                                 <th>VENDOR</th>
                                                <th>BOOKING AT</th>
                                                <th>DATE</th>
                                               
												<th>DURATION</th>
                                                <th>AMOUNT</th>
                                                <th>TYPE</th>
                                                <th>PAID</th>
												<th>STATUS</th>
												
												<th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										
										@foreach($orders as $key => $book)
                                            <tr>
                                                <td>{{$key+1}}</td>
                                                <td>{{$book->Bookid}}</td>
                                                <td>{{$book->name}}</td>
                                                 <td>
                                                     
                                                      @if($book->vendor_id>0)
                                                   
 @php
                                                        $vendor = DB::table('vendor')
    ->where('vendor_id', $book->vendor_id)
    ->pluck('vendor_name')->implode(', ');
                                                        @endphp
                                                        {{ $vendor }}
                                                        @else
                                                        
                                                    @endif
                                                    
                                                     
                                                     {{$book->vendor_id}}</td>
                                                <td>{{$book->Bookingat}}</td>
                                                <td>{{date('Y-m-d',strtotime($book->service_date))}}</td>
                                             
                                               
												  <td>
												  
                                                   {{$book->service_time}}
												</td>


                                                <td>
												  
                                                    {{$book->total_price}}
												</td>


                                                <td>
												  
                                                    <span class="badge light badge-success mb-2">{{$book->payment_method}}</span>
												</td>

                                                <td>
												  
                                                    <span class="badge light badge-success mb-2">{{$book->payment_status}}</span>
												</td>
  <td>
@if($book->status==1) <span class="badge light bg-yellow mb-2">Scheduled</span> @endif
@if($book->status==3) <span class="badge light bg-red mb-2">Cancelled</span> @endif
@if($book->status==6) Approved @endif
@if($book->status==2) <span class="badge light bg-green mb-2">Completed</span> @endif
   </td>                                             
   
  

												<td> <a href="{{route('view-bookdetails',$book->id)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="View"  ><i class="fa fa-eye"></i></a></td>
												
											
                                            </tr>
											@endforeach
                                            
                                            
                                          
                                        </tbody>
                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>


                                            
                                        
										
										
										
										
										
										
										</div>
                                    </div>
									<!-- Modal -->
									<div class="modal fade" id="replyModal">
										<div class="modal-dialog modal-dialog-centered" role="document">
											<div class="modal-content">
												<div class="modal-header">
													<h5 class="modal-title">Post Reply</h5>
													<button type="button" class="btn-close" data-bs-dismiss="modal"></button>
												</div>
												<div class="modal-body">
													<form>
														<textarea class="form-control" rows="4">Message</textarea>
													</form>
												</div>
												<div class="modal-footer">
													<button type="button" class="btn btn-danger light" data-bs-dismiss="modal">Close</button>
													<button type="button" class="btn btn-primary">Reply</button>
												</div>
											</div>
										</div>
									</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
       
@endsection


	