@extends('admin.layout.master')

@section('content')
 @if(session('AddServiceCategory')==1)
  <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   @if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif
                  
					<div class="col-12">
                        <div class="card">
							<div class="card-header justify-content-between">
                                <h4 class="card-title">Services Category</h4>

								<a href="#" data-bs-toggle="modal" data-bs-target="#event-modal" class="btn btn-primary btn-rounded">
									Add New Service Category
								</a>
                            </div>
                            <div class="card-body">
							
   <div class="row mb-5">
                                   
                                  
                                    <div class="col-xl-6 col-lg-6 col-12">


                                        <div class="input-group">
                                            <input type="text" id="searchcategory"  class="form-control" placeholder="search Category..." aria-label="Category" aria-describedby="basic-addon1">
                                            <span class="input-group-text" id="basic-addon1" ><a class="categorysearch85" href="javascript:void(0);">Search</a></span>
                                      </div>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table id="example85" class="display min-w850">
                                        <thead>
                                            <tr>
                                                <th></th>
                                             
                                                <th>NAME</th>
												<th>NAME(Arabic)</th>
												<th>VENDOR</th>
                                                <th>ORDERING</th>
                                              
                                                <th>STATUS</th>
                                            
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										 @foreach($servicescategory as $key => $service)
										
                                            <tr id="{{$service->Cat_ID}}">
                                                <td>{{$key+1}}</td>
                                             
                                                <td>{{$service->Cat_Name}}</td>
												<td>{{$service->Cat_Name_ar}}</td>
												 <td>{{$service->vendor_name}}</td>
                                                <td>{{$service->Ordering}}</td>
                                              
                                                <td> <div class="material-switch justify-content-center">
													<input id="someSwitchOptionDefault{{$key+1}}" data-id="{{$service->Cat_ID}}" class="servicecategoryenable" name="enabled" type="checkbox" value="1" @if($service->enabled==1) checked @endif/>
													<label for="someSwitchOptionDefault{{$key+1}}" class="label-default"></label>
												</div></td>
                                              
                                                <td>
													<div class="d-flex">
                                                       
														<a href="{{route('editservicescategory-vendor',$service->Cat_ID)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="Edit"><i class="fa fa-pencil"></i></a>
														<a href="#" data-id="{{$service->Cat_ID}}" class="Deleteservicecategorysvendor btn btn-danger shadow btn-xs sharp" title="delete"><i class="fa fa-trash"></i></a>
													</div>												
												</td>												
                                            </tr>
                                           
                                           @endforeach
                                            
                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
<div class="modal fade none-border" id="event-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title"><strong>Create Service Category</strong></h4>
                </div>
                <div class="modal-body">
                    <form method="post" action="{{route('vendor-services-category-save')}}" enctype='multipart/form-data'>
					{{csrf_field()}}
                        <div class="row">
						<div class="col-md-6 mb-2">
						  <label class="control-label form-label">Select Vendor <span> *</span></label>
						<select class="form-control default-select" id="single-select" style="width:100%;" required name="vendor_id" >
														<option value="">Select</option>
                                                           @foreach($vendor as $vend)
														   
      <option value="{{$vend->vendor_id}}">{{$vend->vendor_name}}</option>

														 
															   
														    @endforeach
                                                           
                                                        </select>
														 </div>
                            <div class="col-md-6 mb-2">
                                <label class="control-label form-label">Service Category Name (English)<span> *</span></label>
								
                                <input class="form-control form-white" placeholder="Service Name" type="text" name="Cat_Name" required>
                           
														 </div>
                            
<div class="col-md-6 mb-2">
                                <label class="control-label form-label">Service Category Name (Arabic)<span> *</span></label>
								
                                <input class="form-control form-white" placeholder="Service Name" type="text" name="Cat_Name_ar" required>
                           
														 </div>
                            <div class="col-md-6 mb-2">
                                <label class="control-label form-label">Ordering <span> *</span></label>
                                <input class="form-control form-white" placeholder="Ordering" type="text" name="Ordering" required>
                            </div>

                          
							
							
							 


                          

                         

                            
                        </div>
                   

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default waves-effect" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-success save-event waves-effect waves-light">Create
                        </button>

                  
                </div>
				 </form>
            </div>
        </div>
    </div>
	@endif
@endsection


