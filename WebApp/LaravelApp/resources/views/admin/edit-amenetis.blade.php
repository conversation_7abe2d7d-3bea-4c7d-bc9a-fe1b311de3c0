 @extends('admin.layout.master')

@section('content')
 @if(session('AddServiceCategory')==1)
  <div class="content-body default-height">
			<div class="container-fluid">
				
				<!-- Row -->
				<div class="row">
					<div class="col-xl-12">
					
						<div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Edit Ameneties
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
										  <form method="post" action="{{route('editsave-amenties',$ameneties->Am_ID)}}" enctype='multipart/form-data'>
  {{csrf_field()}}
                                              <div class="row">
										@if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif


                                                <div class="form-group col-md-6">
                                                    <label>Amenties Name<span> *</span></label>
                                                    <input type="text" name="AmentiesName" required class="form-control" value="{{$ameneties->AmentiesName}}" placeholder="Amenties Name">
                                                </div>
                                              <div class="form-group col-md-6">
                                                    <label>Amenties Name (Arabic) <span> *</span></label>
                                                    <input type="text" name="AmentiesName_ar" required class="form-control" value="{{$ameneties->AmentiesName_ar}}" placeholder="Amenties Name">
                                                </div>
        
												

												<div class="form-group col-md-6">
                                                    <label>Icon<span> *</span></label>
                                                    <a href="javascript:void(0);" class="imagepicker imagepicker-add thumbnail modalpopup" data-store="AmentiesImage" data-loc="AmentiesImages" data-count="1" > </a>
															<div id="AmentiesImages"></div>
															<input type="hidden" name="AmentiesImage" id="AmentiesImage" value="{{asset($ameneties->AmentiesImage)}}" />
															<img src="{{asset($ameneties->AmentiesImage)}}" width="75"/>
                                                </div>
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
									<div class="filter cm-content-box box-primary style-1 mb-0 border-0">
										<div class="content-title">
											
											<button type="submit" class="btn btn-primary my-2">Submit</button>
										</div>
									</div>
									
								</div>
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		@endif
@endsection


	