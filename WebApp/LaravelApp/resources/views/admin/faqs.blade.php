 @extends('admin.layout.master')

@section('content')
	@if(session('Areas')==1)
 <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-8">
                        <div class="card">
                            <div class="card-header justify-content-between">
                                <h4 class="card-title">Faqs
                                </h4>
                                

                               
                            </div>





                            <div class="card-body">
                                <div class="row mb-5 justify-content-end align-items-end">
                                  
                                 
                                
                
                                   
                                </div>

                               
                        

                                <div class="table-responsive">
                                    <table  class="table" id="table">
                                        <thead class="thead-info">
                                            <tr>
                                                <th></th>
                                                <th>Question</th>
                                                <th>Answer</th>
                                                
                                               
												<th>Options</th>
                                                
                                            </tr>
                                        </thead>
                                        <tbody>
										@foreach($faqs as $key => $faq)
                                            <tr id="{{$faq->Faq_ID}}">
                                                <td>{{$key+1}}</td>
                                                <td>{{$faq->Question}}</td>
                                                
                                             <td>{{$faq->Answer}}</td>
                                               
												  <td>
												  
                                                    <div class="d-flex">
                                                     
														<a href="{{route('edit-faq',$faq->Faq_ID)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="Edit"><i class="fa fa-pencil"></i></a>
													
														<a href="#" data-id="{{$faq->Faq_ID}}" class="deletefaq btn btn-danger shadow btn-xs sharp" title="delete"><i class="fa fa-trash"></i></a>
													</div>		
												</td>
                                             
												
											
                                            </tr>
@endforeach  

                                           


                                          
                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="col-xl-4">
								

                        <div class="filter cm-content-box box-primary">
                            <div class="content-title">
                                <div class="cpa">Add New Faq		
                                </div>
                                <div class="tools">
                                    <a href="javascript:void(0);" class="expand SlideToolHeader"><i class="fal fa-angle-down"></i></a>
                                </div>
                            </div>
                            <div class="cm-content-body  form excerpt">
                                <div class="card-body">
                                    @if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif

  <form method="post" action="{{route('faq-save')}}">
  {{csrf_field()}}
                                    <div class="row">

                                        <div class="col-xl-12 col-sm-12 mb-3">
                                            <label class="form-label">Question<span> *</span></label>
                                            <input type="text" required name="Question" class="form-control" value="{!! old('Question') !!}" placeholder="Question">
                                        </div>
                                       
                                        <div class="col-xl-12 col-sm-12 mb-3">
                                            <label class="form-label">Question (Arabic)<span> *</span></label>
                                            <input type="text" required name="Question_ar" class="form-control" value="{!! old('Question_ar') !!}" placeholder="Question">
                                        </div>
                                     

                                      

                                        <div class="col-xl-12 col-sm-12 mb-3">
                                            <label class="form-label">Answer<span> *</span></label>
                                           <textarea type="text" required name="Answer" class="form-control" value="{!! old('Answer') !!}" placeholder="Answer"></textarea>
                                        </div>
										
										  <div class="col-xl-12 col-sm-12 mb-3">
                                            <label class="form-label">Answer (Arabic)<span> *</span></label>
                                           <textarea type="text" required name="Answer_ar" class="form-control" value="{!! old('Answer_ar') !!}" placeholder="Answer"></textarea>
                                        </div>

                                      

                                        
                                    

                                    </div>
                                    <div class="filter cm-content-box box-primary style-1 mb-0 border-0">
                                        <div class="content-title">
                                            
                                            <button type="submit" class="btn btn-primary my-2">Save</button>
                                        </div>
                                    </div>
									</form>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		@endif
@endsection


	