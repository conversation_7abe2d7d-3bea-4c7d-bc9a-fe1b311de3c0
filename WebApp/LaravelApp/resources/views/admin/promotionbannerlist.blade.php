 @extends('admin.layout.master')

@section('content')
@if(session('ViewAllBooking')==1)
 <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-12">
                        <div class="card">
                            <div class="card-header justify-content-between">
                                <h4 class="card-title">All Promotion Banner List</h4>

                                
                            </div>





                            <div class="card-body">
                               

                               
                        

                                <div class="table-responsive">
                                    <table id="example4" class="display min-w850">
                                        <thead>
                                            <tr>
                                                <th>S.No.</th>
                                                <th>Promotion Banner Name</th>
												<th>Promotion Image</th>
                                                <th>Service</th>
                                                <th>Title</th>
												 <th>Subtitle</th>
                                                <th>Status</th>
                                               
                                               
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										@foreach($banners as $key => $banner)
                                            <tr id="{{$banner->PromoID}}">
                                                <td>{{$key+1}}</td>
                                                <td>{{$banner->PromoName}}</td>
                                                <td><img src="{{asset($banner->PromoImage)}}" width="50" ></td>
                                                <td>{{$banner->Name}}</td>
												  <td>{{$banner->title}}</td>
												    <td>{{$banner->subtitle}}</td>
                                                <td><div class="material-switch justify-content-center">
														<input id="someSwitchOptionDefault{{$key+1}}" class="changebannerstatus" name="someSwitchOption001" data-id="{{$banner->PromoID}}" type="checkbox" @if($banner->enabled==1) checked @endif/>
														<label for="someSwitchOptionDefault{{$key+1}}" class="label-default"></label>
													</div></td>
                                               
                                                    <td> 
<a href="{{route('promotionbanneredit',$banner->PromoID)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="Edit"><i class="fa fa-pencil"></i></a>

													<a href="javascript:void(0);" class="Deletepromobanner btn btn-primary shadow btn-xs sharp me-1" data-id="{{$banner->PromoID}}" title="view"><i class="fa fa-trash"></i></a>
														
														
													</td>
                                                											
                                            </tr>
											 @endforeach  
											





                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		@endif
@endsection





	