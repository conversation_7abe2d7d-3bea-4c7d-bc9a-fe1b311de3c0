<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Title -->
	<title>{{ config('app.name') }}</title>

	<!-- Meta -->
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="author" content="">
	<meta name="robots" content="">


	<meta property="og:image" content="social-image.png">
	<meta name="format-detection" content="telephone=no">

	<!-- Mobile Specific -->
	<meta name="viewport" content="width=device-width, initial-scale=1">

	<!-- Favicon icon -->
	<link rel="shortcut icon" type="image/x-icon" href="#">
	<link rel="stylesheet" href="{{ asset('vendor/chartist/css/chartist.min.css') }}">
    <link href="{{ asset('vendor/bootstrap-select/css/bootstrap-select.min.css') }}" rel="stylesheet">
	<link href="{{ asset('vendor/owl-carousel/owl.carousel.css') }}" rel="stylesheet">
   
	<!-- Datatable -->
    <link href="{{ asset('vendor/datatables/css/jquery.dataTables.min.css') }}" rel="stylesheet">
	 <link href="{{ asset('vendor/summernote/summernote.css') }}" rel="stylesheet">
	<link rel="stylesheet" href="{{ asset('vendor/chartist/css/chartist.min.css') }}">
	 <link href="{{ asset('vendor/dropzone/dropzone.css') }}" rel="stylesheet">
	 <link href="{{ asset('vendor/bootstrap-daterangepicker/daterangepicker.css') }}" rel="stylesheet">
	     <link href="{{ asset('vendor/clockpicker/css/bootstrap-clockpicker.min.css') }}" rel="stylesheet">
    <!-- asColorpicker -->
    <link href="{{ asset('vendor/jquery-asColorPicker/css/asColorPicker.min.css') }}" rel="stylesheet">
    <!-- Material color picker -->
    <link href="{{ asset('vendor/bootstrap-material-datetimepicker/css/bootstrap-material-datetimepicker.css') }}" rel="stylesheet">
	 <!-- Pick date -->
	 <link rel="stylesheet" href="{{ asset('vendor/pickadate/themes/default.css') }}">
	 <link rel="stylesheet" href="{{ asset('vendor/pickadate/themes/default.date.css') }}">
	 <link rel="stylesheet" href="{{ asset('vendor/select2/css/select2.min.css') }}">
		<script src="https://cdn.datatables.net/buttons/2.3.5/css/buttons.dataTables.min.css"></script>

    <!-- Clockpicker -->
    <link href="{{ asset('vendor/clockpicker/css/bootstrap-clockpicker.min.css') }}" rel="stylesheet">
	 <link href="{{ asset('css/style.css') }}" rel="stylesheet">
	 <link href="{{ asset('css/jquery.timepicker.min.css') }}" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&amp;family=Roboto:wght@100;300;400;500;700;900&amp;display=swap" rel="stylesheet" crossorigin="anonymous">
	<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" crossorigin="anonymous">
	
<style>
.select2-container--default .select2-selection--multiple {
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}
</style>

</head>
<body>

    <!--*******************
        Preloader start
    ********************-->
    <div id="preloader">
        <div class="sk-three-bounce">
            <div class="sk-child sk-bounce1"></div>
            <div class="sk-child sk-bounce2"></div>
            <div class="sk-child sk-bounce3"></div>
        </div>
    </div>
    <!--*******************
        Preloader end
    ********************-->

    <!--**********************************
        Main wrapper start
    ***********************************-->
    <div id="main-wrapper">

        <!--**********************************
            Nav header start
        ***********************************-->
         <div class="nav-header">
            <a href="{{route('adminDashboard')}}" class="brand-logo" aria-label="Gymove">
                <img class="logo-abbr" src="{{ asset('images/logo.png') }}" alt="">
                <img class="logo-compact" src="{{ asset('images/logo-text.png') }}" alt="">
                <img class="brand-title" src="{{ asset('images/logo-text.png') }}" alt="">
            </a>

            <div class="nav-control">
                <div class="hamburger">
                    <span class="line"></span><span class="line"></span><span class="line"></span>
                </div>
            </div>
        </div>
        <!--**********************************
            Nav header end
        ***********************************-->
		
		<!--**********************************
            Chat box start
        ***********************************-->
		<div class="chatbox">
			<div class="chatbox-close"></div>
			
		</div>
		<!--**********************************
            Chat box End
        ***********************************-->
		
		<!--**********************************
            Header start
        ***********************************-->
        <header class="header">
            <div class="header-content">
                <nav class="navbar navbar-expand">
                    <div class="collapse navbar-collapse justify-content-between">
                        <div class="header-left">
                            <div class="dashboard_bar">
								Dashboard
                            </div>
                        </div>
                        <ul class="navbar-nav header-right">
							<!--<li class="nav-item">
								<form>
									<div class="input-group search-area d-lg-inline-flex d-none me-3">
									  <span class="input-group-text" id="header-search">
											<button class="bg-transparent border-0" type="button" aria-label="header-search">
												<i class="flaticon-381-search-2"></i>
											</button>
									  </span>
									  <input type="text" class="form-control" placeholder="Search here" aria-label="Username" aria-describedby="header-search">
									</div>
								</form>
							</li>-->
							
							
							
							
                            <li class="nav-item dropdown header-profile">
                                <a class="nav-link" href="javascript:void(0)" role="button" data-bs-toggle="dropdown">
                                    <img src="{{asset(session('admin_image1'))}}" width="20" alt="">
									<div class="header-info">
										<span><strong>{{session('admin_name1')}}</strong></span>
										
									</div>
                                </a>
                                <div class="dropdown-menu dropdown-menu-end">
                                    <a href="{{route('edit-profile')}}" class="dropdown-item ai-icon">
                                        <svg id="icon-user1" xmlns="http://www.w3.org/2000/svg" class="text-primary" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                                        <span class="ms-2">Profile </span>
                                    </a>
                                  
                                    <a href="{{route('logout')}}" class="dropdown-item ai-icon">
                                        <svg id="icon-logout" xmlns="http://www.w3.org/2000/svg" class="text-danger" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path><polyline points="16 17 21 12 16 7"></polyline><line x1="21" y1="12" x2="9" y2="12"></line></svg>
                                        <span class="ms-2">Logout </span>
                                    </a>
                                </div>
                            </li>
                        </ul>
                    </div>
                </nav>
            </div>
        </header>
        <!--**********************************
            Header end ti-comment-alt
        ***********************************-->

        <!--**********************************
            Sidebar start
        ***********************************-->
          <div class="deznav">
            <div class="deznav-scroll">
				<ul class="metismenu" id="menu">
				@if(session('DashBoardView')==1)
                    <li><a class="ai-icon" href="{{route('adminDashboard')}}" aria-expanded="false">
							<i class="flaticon-381-networking"></i>
							<span class="nav-text">Dashboard</span>
						</a>
                        
                    </li>
				@endif	
@if(session('CustomerView')==1)
					<li><a class="ai-icon" href="{{route('customer-list')}}" aria-expanded="false">
						<i><svg xmlns="http://www.w3.org/2000/svg" width="34" height="34" viewBox="0 0 42.631 44.999">
							<g id="bx-user" transform="translate(-4.5 -3)">
							  <path id="Path_19" data-name="Path 19" d="M27.71,13.105A7.105,7.105,0,1,1,20.605,6,7.105,7.105,0,0,1,27.71,13.105Z" transform="translate(5.21 1.737)" fill="#fff"></path>
							  <path id="Path_20" data-name="Path 20" d="M25.815,3A11.842,11.842,0,1,0,37.657,14.842,11.855,11.855,0,0,0,25.815,3Zm0,18.947a7.105,7.105,0,1,1,7.105-7.105A7.114,7.114,0,0,1,25.815,21.947ZM47.131,48V45.631A16.6,16.6,0,0,0,30.552,29.052H21.079A16.6,16.6,0,0,0,4.5,45.631V48H9.237V45.631A11.854,11.854,0,0,1,21.079,33.789h9.473A11.854,11.854,0,0,1,42.394,45.631V48Z" transform="translate(0 0)" fill="#fff"></path>
							</g>
						  </svg></i>
						<span class="nav-text">Customers</span>
					</a>
				   
				</li>
				@endif	
                    <li><a class="has-arrow ai-icon" href="javascript:void()" aria-expanded="false">
							<i> <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="24" x="0" y="0" viewBox="0 0 64 64" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><g fill="#000" fill-rule="evenodd" clip-rule="evenodd"><path d="M7 2a2 2 0 0 0-1.93 1.474l-3 11A2 2 0 0 0 2 15a8.977 8.977 0 0 0 3 6.708V52c0 5.523 4.477 10 10 10h17a2 2 0 1 0 0-4H15a6 6 0 0 1-6-6V23.777A9.033 9.033 0 0 0 11 24a8.983 8.983 0 0 0 7-3.343C19.65 22.697 22.173 24 25 24s5.35-1.304 7-3.343C33.65 22.697 36.173 24 39 24s5.35-1.304 7-3.343A8.983 8.983 0 0 0 53 24c.687 0 1.357-.077 2-.223V33.5a2 2 0 1 0 4 0V21.708A8.978 8.978 0 0 0 62 15c0-.178-.024-.355-.07-.526l-3-11A2 2 0 0 0 57 2zm18 18a5 5 0 0 1-5-5 2 2 0 1 0-4 0 5 5 0 0 1-9.994.246L8.528 6h46.944l2.522 9.246A5 5 0 0 1 48 15a2 2 0 1 0-4 0 5 5 0 0 1-10 0 2 2 0 1 0-4 0 5 5 0 0 1-5 5z" fill="#000000" opacity="1" data-original="#000000" class=""></path><path d="M57 43a7.971 7.971 0 0 1-2.106 5.41A13 13 0 0 1 62 60a2 2 0 1 1-4 0 9 9 0 1 0-18 0 2 2 0 1 1-4 0 13 13 0 0 1 7.106-11.59A8 8 0 1 1 57 43zm-12 0a4 4 0 1 1 8 0 4 4 0 0 1-8 0z" fill="#000000" opacity="1" data-original="#000000" class=""></path></g></g></svg> </i>
							<span class="nav-text">Vendor</span>
						</a>
                       <ul aria-expanded="false">
					   @if(session('AllVendorView')==1)
							<li><a href="{{route('list-vendors')}}">All Vendor</a></li>
                         @endif
						  @if(session('AddVendor')==1)
							<li><a href="{{route('add-vendor')}}">Add Vendor</a></li>
						@endif
						</ul>
                    </li>
					<!--<li><a class="has-arrow ai-icon" href="javascript:void()" aria-expanded="false">
							<i> <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="24" x="0" y="0" viewBox="0 0 605.654 605.654" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M494.618 60.571h80.752a29.473 29.473 0 0 0 21.268-8.812 29.908 29.908 0 0 0 8.893-24.105C603.78 11.754 590.235-.214 574.24.007H444.147c-22.299 0-40.376 18.077-40.376 40.376v524.891c0 22.299 18.077 40.376 40.376 40.376s40.376-18.077 40.376-40.376V393.675c0-5.575 4.519-10.094 10.094-10.094h80.752a29.473 29.473 0 0 0 21.268-8.812 29.91 29.91 0 0 0 8.893-24.105c-1.751-15.901-15.296-27.869-31.292-27.648h-79.622c-5.575 0-10.094-4.519-10.094-10.094v-10.094h100.941c5.575 0 10.094-4.519 10.094-10.094s-4.519-10.094-10.094-10.094h-100.94v-20.188h100.941c5.575 0 10.094-4.519 10.094-10.094s-4.519-10.094-10.094-10.094H484.523v-20.188h100.941c5.575 0 10.094-4.519 10.094-10.094s-4.519-10.094-10.094-10.094H484.523V181.7h100.941c5.575 0 10.094-4.519 10.094-10.094s-4.519-10.094-10.094-10.094H484.523v-20.188h100.941c5.575 0 10.094-4.519 10.094-10.094s-4.519-10.094-10.094-10.094H484.523v-20.188h100.941c5.575 0 10.094-4.519 10.094-10.094s-4.519-10.094-10.094-10.094H484.523V70.665c0-5.575 4.52-10.094 10.095-10.094zm0 282.634h79.622a10.933 10.933 0 0 1 11.225 9.165 9.885 9.885 0 0 1-3.028 8.075 9.676 9.676 0 0 1-7.066 2.947h-80.752c-16.724 0-30.282 13.558-30.282 30.282v171.599c0 11.15-9.039 20.188-20.188 20.188-11.15 0-20.188-9.039-20.188-20.188V40.383c0-11.15 9.039-20.188 20.188-20.188H574.24a10.933 10.933 0 0 1 11.225 9.165 9.886 9.886 0 0 1-3.028 8.126 9.683 9.683 0 0 1-7.066 2.897h-80.752c-16.724 0-30.282 13.558-30.282 30.282v242.257c-.002 16.725 13.556 30.283 30.281 30.283zM302.83 565.274c22.299 0 40.376-18.077 40.376-40.376V504.71c0-22.299-18.077-40.376-40.376-40.376s-40.376 18.077-40.376 40.376v20.188c0 22.299 18.077 40.376 40.376 40.376zm-20.188-60.564c0-11.15 9.039-20.188 20.188-20.188s20.188 9.039 20.188 20.188v20.188c0 11.15-9.039 20.188-20.188 20.188s-20.188-9.039-20.188-20.188zM80.761 565.274c22.299 0 40.376-18.077 40.376-40.376V504.71c0-22.299-18.077-40.376-40.376-40.376S40.385 482.41 40.385 504.71v20.188c0 22.299 18.077 40.376 40.376 40.376zM60.573 504.71c0-11.15 9.039-20.188 20.188-20.188s20.188 9.039 20.188 20.188v20.188c0 11.15-9.039 20.188-20.188 20.188s-20.188-9.039-20.188-20.188z" fill="#000000" opacity="1" data-original="#000000" class=""></path><path d="M78.127 605.65h2.372a78.837 78.837 0 0 0 55.144-22.772 85.447 85.447 0 0 0 25.871-60.938v-62.785a50.25 50.25 0 0 1 14.808-35.753l15.474-15.474 15.464 15.464a50.942 50.942 0 0 1 14.818 35.763v62.795a85.424 85.424 0 0 0 25.942 60.968 78.664 78.664 0 0 0 54.962 22.732h2.463c43.574-1.411 78.161-37.155 78.138-80.753V506.98a80.814 80.814 0 0 0-93.128-82.105 10.094 10.094 0 0 1-11.154-6.178l-40.063-101.526 82.963-211.592a86.526 86.526 0 0 0 6.955-33.815 76.494 76.494 0 0 0-35.329-68.64 19.848 19.848 0 0 0-16.837-2.11 20.278 20.278 0 0 0-12.628 11.77L191.796 196.78 119.199 12.705A20.244 20.244 0 0 0 106.46.956a19.715 19.715 0 0 0-16.716 2.19 76.523 76.523 0 0 0-35.329 68.64 85.972 85.972 0 0 0 6.854 33.563l83.084 211.723-40.073 101.626a9.981 9.981 0 0 1-11.285 6.147 81.804 81.804 0 0 0-17.594-.686C32.367 427.601-.613 463.844.008 507.011v17.887c.014 43.574 34.568 79.294 78.119 80.752zM282.925 20.104a56.698 56.698 0 0 1 26.063 51.661 66.613 66.613 0 0 1-5.461 26.245l-75.14 191.595-25.75-65.298zM74.584 71.806a56.81 56.81 0 0 1 25.861-51.611l160.082 405.923c5.25 13.208 18.979 20.988 33.008 18.704a60.747 60.747 0 0 1 13.365-.565c32.392 2.807 57.08 30.216 56.496 62.724v17.917c.018 32.676-25.888 59.475-58.546 60.564a59.14 59.14 0 0 1-42.738-16.978 65.108 65.108 0 0 1-19.845-46.534v-62.795a71.233 71.233 0 0 0-20.733-50.036l-24.418-24.418a51.97 51.97 0 0 1-10.659-15.525L79.943 97.757a66.307 66.307 0 0 1-5.359-25.951zM20.197 507.011c-.625-32.552 24.133-59.995 56.577-62.714 4.406-.3 8.832-.131 13.203.505 14.055 2.367 27.85-5.425 33.078-18.684l32.109-81.449 12.668 32.301a72.765 72.765 0 0 0 10.195 16.15l-15.979 15.979a70.267 70.267 0 0 0-20.723 50.056v62.785a65.12 65.12 0 0 1-19.744 46.433 59.996 59.996 0 0 1-42.839 17.089c-32.644-1.12-58.532-27.901-58.546-60.564v-17.887z" fill="#000000" opacity="1" data-original="#000000" class=""></path></g></svg></i>
							<span class="nav-text">Artist </span>
						</a>
                       <ul aria-expanded="false">
							<li><a href="all-artist.html">All Artist</a></li>
						
							<li><a href="add-artist.html">Add Artist</a></li>	
							
						</ul>
                    </li>-->
					<li><a class="ai-icon" href="{{route('PopularArtist')}}" aria-expanded="false">
						<i><svg xmlns="http://www.w3.org/2000/svg" width="34" height="34" viewBox="0 0 42.631 44.999">
							<g id="bx-user" transform="translate(-4.5 -3)">
							  <path id="Path_19" data-name="Path 19" d="M27.71,13.105A7.105,7.105,0,1,1,20.605,6,7.105,7.105,0,0,1,27.71,13.105Z" transform="translate(5.21 1.737)" fill="#fff"></path>
							  <path id="Path_20" data-name="Path 20" d="M25.815,3A11.842,11.842,0,1,0,37.657,14.842,11.855,11.855,0,0,0,25.815,3Zm0,18.947a7.105,7.105,0,1,1,7.105-7.105A7.114,7.114,0,0,1,25.815,21.947ZM47.131,48V45.631A16.6,16.6,0,0,0,30.552,29.052H21.079A16.6,16.6,0,0,0,4.5,45.631V48H9.237V45.631A11.854,11.854,0,0,1,21.079,33.789h9.473A11.854,11.854,0,0,1,42.394,45.631V48Z" transform="translate(0 0)" fill="#fff"></path>
							</g>
						  </svg></i>
						<span class="nav-text">Popular Artist</span>
					</a>
				   
				</li>
				<li><a class="has-arrow ai-icon" href="javascript:void()" aria-expanded="false">
					<i> <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="512" height="512" x="0" y="0" viewBox="0 0 64 64" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M55.3 62H41.7C38 62 35 59 35 55.3V37c0-1.1.9-2 2-2h18.3c3.7 0 6.7 3 6.7 6.7v13.6c0 3.7-3 6.7-6.7 6.7zM39 39v16.3c0 1.5 1.2 2.7 2.7 2.7h13.6c1.5 0 2.7-1.2 2.7-2.7V41.7c0-1.5-1.2-2.7-2.7-2.7zM22.3 62H8.7C5 62 2 59 2 55.3V41.7C2 38 5 35 8.7 35H27c1.1 0 2 .9 2 2v18.3c0 3.7-3 6.7-6.7 6.7zM8.7 39C7.2 39 6 40.2 6 41.7v13.6C6 56.8 7.2 58 8.7 58h13.6c1.5 0 2.7-1.2 2.7-2.7V39zM55.3 29H37c-1.1 0-2-.9-2-2V8.7C35 5 38 2 41.7 2h13.6C59 2 62 5 62 8.7v13.6c0 3.7-3 6.7-6.7 6.7zM39 25h16.3c1.5 0 2.7-1.2 2.7-2.7V8.7C58 7.2 56.8 6 55.3 6H41.7C40.2 6 39 7.2 39 8.7zM27 29H8.7C5 29 2 26 2 22.3V8.7C2 5 5 2 8.7 2h13.6C26 2 29 5 29 8.7V27c0 1.1-.9 2-2 2zM8.7 6C7.2 6 6 7.2 6 8.7v13.6C6 23.8 7.2 25 8.7 25H25V8.7C25 7.2 23.8 6 22.3 6z" fill="#000000" opacity="1" data-original="#000000"></path></g></svg></i>
					<span class="nav-text">Service</span>
				</a>
				<ul aria-expanded="false">
				@if(session('ServiceCategoryList')==1)
					<li><a href="{{route('service-categories')}}">Main Categories </a></li>
				@endif
				@if(session('ServicesList')==1)
					<li><a href="{{route('vendor-servicescategories')}}">Vendor Categories</a></li>
				@endif
				   
				</ul>
			</li>
			
			<li><a class="has-arrow ai-icon" href="javascript:void()" aria-expanded="false">
					<i> <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="512" height="512" x="0" y="0" viewBox="0 0 64 64" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M55.3 62H41.7C38 62 35 59 35 55.3V37c0-1.1.9-2 2-2h18.3c3.7 0 6.7 3 6.7 6.7v13.6c0 3.7-3 6.7-6.7 6.7zM39 39v16.3c0 1.5 1.2 2.7 2.7 2.7h13.6c1.5 0 2.7-1.2 2.7-2.7V41.7c0-1.5-1.2-2.7-2.7-2.7zM22.3 62H8.7C5 62 2 59 2 55.3V41.7C2 38 5 35 8.7 35H27c1.1 0 2 .9 2 2v18.3c0 3.7-3 6.7-6.7 6.7zM8.7 39C7.2 39 6 40.2 6 41.7v13.6C6 56.8 7.2 58 8.7 58h13.6c1.5 0 2.7-1.2 2.7-2.7V39zM55.3 29H37c-1.1 0-2-.9-2-2V8.7C35 5 38 2 41.7 2h13.6C59 2 62 5 62 8.7v13.6c0 3.7-3 6.7-6.7 6.7zM39 25h16.3c1.5 0 2.7-1.2 2.7-2.7V8.7C58 7.2 56.8 6 55.3 6H41.7C40.2 6 39 7.2 39 8.7zM27 29H8.7C5 29 2 26 2 22.3V8.7C2 5 5 2 8.7 2h13.6C26 2 29 5 29 8.7V27c0 1.1-.9 2-2 2zM8.7 6C7.2 6 6 7.2 6 8.7v13.6C6 23.8 7.2 25 8.7 25H25V8.7C25 7.2 23.8 6 22.3 6z" fill="#000000" opacity="1" data-original="#000000"></path></g></svg></i>
					<span class="nav-text">Promotional Banner</span>
				</a>
				<ul aria-expanded="false">
				
					<li><a href="{{route('promotionbanners')}}">Promotional Banners</a></li>
				
					<li><a href="{{route('promotionbannersadd')}}">Add Banner</a></li>
				
				   
				</ul>
			</li>
			
				<li><a class="ai-icon" href="{{route('customer-list')}}" aria-expanded="false">
						<i><svg xmlns="http://www.w3.org/2000/svg" width="34" height="34" viewBox="0 0 42.631 44.999">
							<g id="bx-user" transform="translate(-4.5 -3)">
							  <path id="Path_19" data-name="Path 19" d="M27.71,13.105A7.105,7.105,0,1,1,20.605,6,7.105,7.105,0,0,1,27.71,13.105Z" transform="translate(5.21 1.737)" fill="#fff"></path>
							  <path id="Path_20" data-name="Path 20" d="M25.815,3A11.842,11.842,0,1,0,37.657,14.842,11.855,11.855,0,0,0,25.815,3Zm0,18.947a7.105,7.105,0,1,1,7.105-7.105A7.114,7.114,0,0,1,25.815,21.947ZM47.131,48V45.631A16.6,16.6,0,0,0,30.552,29.052H21.079A16.6,16.6,0,0,0,4.5,45.631V48H9.237V45.631A11.854,11.854,0,0,1,21.079,33.789h9.473A11.854,11.854,0,0,1,42.394,45.631V48Z" transform="translate(0 0)" fill="#fff"></path>
							</g>
						  </svg></i>
						<span class="nav-text">Promo Code</span>
					</a>
				   
				</li>

				
                    <li><a class="has-arrow ai-icon" href="javascript:void()" aria-expanded="false">
							<i> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
								<g id="vuesax_outline_printer" data-name="vuesax/outline/printer" transform="translate(-108 -188)">
								  <g id="printer">
									<path id="Vector" d="M10.25,6.5H.75A.755.755,0,0,1,0,5.75v-2C0,1.19,1.19,0,3.75,0h3.5C9.81,0,11,1.19,11,3.75v2A.755.755,0,0,1,10.25,6.5ZM1.5,5h8V3.75c0-1.7-.55-2.25-2.25-2.25H3.75c-1.7,0-2.25.55-2.25,2.25Z" transform="translate(114.5 189.25)" fill="#757575"></path>
									<path id="Vector-2" data-name="Vector" d="M5.75,8.5h-2A3.381,3.381,0,0,1,0,4.75v-4A.755.755,0,0,1,.75,0h8A.755.755,0,0,1,9.5.75v4A3.381,3.381,0,0,1,5.75,8.5ZM1.5,1.5V4.75C1.5,6.33,2.17,7,3.75,7h2C7.33,7,8,6.33,8,4.75V1.5Z" transform="translate(115.25 202.25)" fill="#757575"></path>
									<path id="Vector-3" data-name="Vector" d="M15.75,12.5h-2a.755.755,0,0,1-.75-.75V9.5H6.5v2.25a.755.755,0,0,1-.75.75h-2A3.381,3.381,0,0,1,0,8.75v-5A3.381,3.381,0,0,1,3.75,0h12A3.381,3.381,0,0,1,19.5,3.75v5A3.381,3.381,0,0,1,15.75,12.5ZM14.5,11h1.25C17.33,11,18,10.33,18,8.75v-5c0-1.58-.67-2.25-2.25-2.25h-12c-1.58,0-2.25.67-2.25,2.25v5c0,1.58.67,2.25,2.25,2.25H5V8.75A.755.755,0,0,1,5.75,8h8a.755.755,0,0,1,.75.75Z" transform="translate(110.25 194.25)" fill="#757575"></path>
									<path id="Vector-4" data-name="Vector" d="M10.75,1.5H.75A.755.755,0,0,1,0,.75.755.755,0,0,1,.75,0h10a.755.755,0,0,1,.75.75A.755.755,0,0,1,10.75,1.5Z" transform="translate(114.25 202.25)" fill="#757575"></path>
									<path id="Vector-5" data-name="Vector" d="M3.75,1.5h-3A.755.755,0,0,1,0,.75.755.755,0,0,1,.75,0h3A.755.755,0,0,1,4.5.75.755.755,0,0,1,3.75,1.5Z" transform="translate(114.25 198.25)" fill="#757575"></path>
									<path id="Vector-6" data-name="Vector" d="M0,0H24V24H0Z" transform="translate(108 188)" fill="none" opacity="0"></path>
								  </g>
								</g>
							  </svg></i>
							<span class="nav-text">Reports</span>
						</a>
                        <ul aria-expanded="false">
						@if(session('ViewSaleReport')==1)
                            <li><a href="{{route('sale-report')}}">Sale Report</a></li>
						@endif
						@if(session('ViewVendorReport')==1)
                            <li><a href="{{route('vendor-report')}}">Vendor Report</a></li>
						@endif
						@if(session('ViewUserReport')==1)
                            <li><a href="{{route('user-report')}}">User Report</a></li>
                         @endif  
                        </ul>
                    </li>
                    <li><a class="has-arrow ai-icon" href="javascript:void()" aria-expanded="false">
							<i>  
								<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="512" height="512" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M456.832 32.133H415.6V15c0-8.284-6.716-15-15-15s-15 6.716-15 15v17.133h-66.4V15c0-8.284-6.716-15-15-15s-15 6.716-15 15v17.133h-66.4V15c0-8.284-6.716-15-15-15s-15 6.716-15 15v17.133h-66.4V15c0-8.284-6.716-15-15-15s-15 6.716-15 15v17.133H55.166C24.748 32.133 0 56.88 0 87.3v369.533C0 487.252 24.748 512 55.166 512h401.666C487.251 512 512 487.252 512 456.833V87.3c0-30.42-24.749-55.167-55.168-55.167zm-401.666 30H96.4v17.134c0 8.284 6.716 15 15 15s15-6.716 15-15V62.133h66.398v17.134c0 8.284 6.716 15 15 15s15-6.716 15-15V62.133h66.4v17.134c0 8.284 6.716 15 15 15s15-6.716 15-15V62.133h66.4v17.134c0 8.284 6.716 15 15 15s15-6.716 15-15V62.133h41.232c13.879 0 25.17 11.29 25.17 25.167v41.233H30V87.3c0-13.877 11.29-25.167 25.166-25.167zM456.832 482H55.166C41.29 482 30 470.71 30 456.833v-298.3h452v298.3C482 470.71 470.709 482 456.832 482z" fill="#000000" opacity="1" data-original="#000000"></path><path d="M151.566 208.867H87.299c-8.284 0-15 6.716-15 15v64.266c0 8.284 6.716 15 15 15h64.268c8.284 0 15-6.716 15-15v-64.266c-.001-8.284-6.716-15-15.001-15zm-15 64.266h-34.268v-34.266h34.268zM424.699 208.867h-64.266c-8.284 0-15 6.716-15 15v64.266c0 8.284 6.716 15 15 15h64.266c8.284 0 15-6.716 15-15v-64.266c0-8.284-6.716-15-15-15zm-15 64.266h-34.266v-34.266h34.266zM151.566 337.4H87.299c-8.284 0-15 6.716-15 15v64.266c0 8.284 6.716 15 15 15h64.268c8.284 0 15-6.716 15-15V352.4c-.001-8.284-6.716-15-15.001-15zm-15 64.266h-34.268V367.4h34.268zM424.699 337.4h-64.266c-8.284 0-15 6.716-15 15v64.266c0 8.284 6.716 15 15 15h64.266c8.284 0 15-6.716 15-15V352.4c0-8.284-6.716-15-15-15zm-15 64.266h-34.266V367.4h34.266zM288.133 337.4h-64.268c-8.284 0-15 6.716-15 15v64.266c0 8.284 6.716 15 15 15h64.268c8.284 0 15-6.716 15-15V352.4c0-8.284-6.716-15-15-15zm-15 64.266h-34.268V367.4h34.268zM279.475 222.673l-34.834 34.836-12.116-12.116c-5.857-5.858-15.355-5.858-21.213 0s-5.858 15.355 0 21.213l22.723 22.723a15 15 0 0 0 21.213 0l45.441-45.443c5.857-5.858 5.857-15.355 0-21.213-5.859-5.858-15.356-5.858-21.214 0z" fill="#000000" opacity="1" data-original="#000000"></path></g></svg></i>
							<span class="nav-text">Booking</span>
						</a>
                        <ul aria-expanded="false">
						@if(session('ViewAllBooking')==1)
                            <li><a href="{{route('booking-list')}}">All Booking</a></li>
						@endif
						@if(session('ViewAllCancelBooking')==1)						
                            <li><a href="{{route('booking-listcancelled')}}">Cancel Booking</a></li>
                         @endif
                           

                        </ul>
                    </li>
					@if(session('ViewNotification')==1)
                    <li><a class="ai-icon" href="{{route('send-notification')}}" aria-expanded="false">
							<i> <svg xmlns="http://www.w3.org/2000/svg" width="24.5" height="24.5" viewBox="0 0 24.5 24.5">
								<g id="vuesax_outline_notification" data-name="vuesax/outline/notification" transform="translate(-172 -188)">
								  <g id="notification" transform="translate(172 188)">
									<path id="Vector" d="M8.977,18.743a22.126,22.126,0,0,1-7.013-1.133,2.84,2.84,0,0,1-1.8-1.684A2.757,2.757,0,0,1,.442,13.5l1.174-1.95a4.132,4.132,0,0,0,.47-1.7V6.891a6.891,6.891,0,0,1,13.781,0v2.95a4.266,4.266,0,0,0,.47,1.715L17.5,13.5a2.826,2.826,0,0,1,.225,2.43,2.777,2.777,0,0,1-1.746,1.684A22.017,22.017,0,0,1,8.977,18.743Zm0-17.211A5.365,5.365,0,0,0,3.617,6.891v2.95a5.758,5.758,0,0,1-.684,2.491l-1.174,1.95a1.269,1.269,0,0,0-.153,1.113,1.291,1.291,0,0,0,.847.755,20.416,20.416,0,0,0,13.056,0,1.246,1.246,0,0,0,.786-.766,1.271,1.271,0,0,0-.1-1.1l-1.174-1.95a5.737,5.737,0,0,1-.684-2.5V6.891A5.359,5.359,0,0,0,8.977,1.531Z" transform="translate(3.294 2.215)" fill="#7d7d7d"></path>
									<path id="Vector-2" data-name="Vector" d="M4.548,2.818a.777.777,0,0,1-.214-.031A7.193,7.193,0,0,0,3.476,2.6a5.969,5.969,0,0,0-2.491.184A.76.76,0,0,1,.056,1.776,2.781,2.781,0,0,1,2.659,0a2.766,2.766,0,0,1,2.6,1.776.778.778,0,0,1-.143.8A.789.789,0,0,1,4.548,2.818Z" transform="translate(9.622 1.205)" fill="#7d7d7d"></path>
									<path id="Vector-3" data-name="Vector" d="M3.828,3.828A3.842,3.842,0,0,1,1.123,2.705,3.842,3.842,0,0,1,0,0H1.531A2.313,2.313,0,0,0,2.2,1.623,2.313,2.313,0,0,0,3.828,2.3,2.3,2.3,0,0,0,6.125,0H7.656A3.83,3.83,0,0,1,3.828,3.828Z" transform="translate(8.442 19.457)" fill="#7d7d7d"></path>
									<path id="Vector-4" data-name="Vector" d="M0,0H24.5V24.5H0Z" fill="none" opacity="0"></path>
								  </g>
								</g>
							  </svg> </i>
							<span class="nav-text">Send Notifications</span>
						</a>
                        
                    </li>
					 @endif
					 @if(session('SendEmails')==1)
                    <li><a href="{{route('send-email')}}" class="ai-icon" aria-expanded="false">
							<i> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
								<g id="vuesax_outline_direct-send" data-name="vuesax/outline/direct-send" transform="translate(-620 -188)">
								  <g id="direct-send">
									<path id="Vector" d="M2.747,8.5A.755.755,0,0,1,2,7.749V2.559l-.72.72a.75.75,0,0,1-1.06-1.06l2-2a.753.753,0,0,1,.82-.16.741.741,0,0,1,.46.69v7A.755.755,0,0,1,2.747,8.5Z" transform="translate(629.253 189.251)" fill="#7d7d7d"></path>
									<path id="Vector-2" data-name="Vector" d="M2.747,3.5a.742.742,0,0,1-.53-.22l-2-2A.75.75,0,0,1,1.277.218l2,2a.754.754,0,0,1,0,1.06A.742.742,0,0,1,2.747,3.5Z" transform="translate(631.253 189.253)" fill="#7d7d7d"></path>
									<path id="Vector-3" data-name="Vector" d="M12.53,5.5H9A2.731,2.731,0,0,1,6.54,3.98L5.37,1.64a.24.24,0,0,0-.22-.14H.75A.755.755,0,0,1,0,.75.755.755,0,0,1,.75,0H5.16A1.743,1.743,0,0,1,6.73.97L7.9,3.31A1.231,1.231,0,0,0,9.02,4h3.53a1.231,1.231,0,0,0,1.12-.69L14.84.97A1.743,1.743,0,0,1,16.41,0h4.36a.755.755,0,0,1,.75.75.755.755,0,0,1-.75.75H16.41a.23.23,0,0,0-.22.14L15.02,3.98A2.809,2.809,0,0,1,12.53,5.5Z" transform="translate(621.23 200.25)" fill="#7d7d7d"></path>
									<path id="Vector-4" data-name="Vector" d="M13.75,18.368h-6c-5.43,0-7.75-2.32-7.75-7.75v-3C0,2.928,1.74.578,5.64.008a.741.741,0,0,1,.85.63.747.747,0,0,1-.63.85c-3.14.46-4.36,2.18-4.36,6.13v3c0,4.61,1.64,6.25,6.25,6.25h6c4.61,0,6.25-1.64,6.25-6.25v-3c0-3.95-1.22-5.67-4.36-6.13a.748.748,0,0,1,.22-1.48c3.9.57,5.64,2.92,5.64,7.61v3C21.5,16.048,19.18,18.368,13.75,18.368Z" transform="translate(621.25 192.382)" fill="#7d7d7d"></path>
									<path id="Vector-5" data-name="Vector" d="M0,0H24V24H0Z" transform="translate(620 188)" fill="none" opacity="0"></path>
								  </g>
								</g>
							  </svg> </i>
							<span class="nav-text">Send Emails  </span>
						</a>
					</li>
					@endif
					@if(session('SendMessages')==1)
                     <li><a class="ai-icon" href="{{route('send-sms')}}" aria-expanded="false">
							<i> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
								<g id="vuesax_outline_sms" data-name="vuesax/outline/sms" transform="translate(-556 -252)">
								  <g id="sms">
									<path id="Vector" d="M15.75,18.5h-10C2.1,18.5,0,16.4,0,12.75v-7C0,2.1,2.1,0,5.75,0h10C19.4,0,21.5,2.1,21.5,5.75v7C21.5,16.4,19.4,18.5,15.75,18.5Zm-10-17C2.89,1.5,1.5,2.89,1.5,5.75v7C1.5,15.61,2.89,17,5.75,17h10C18.61,17,20,15.61,20,12.75v-7c0-2.86-1.39-4.25-4.25-4.25Z" transform="translate(557.25 254.75)" fill="#7d7d7d"></path>
									<path id="Vector-2" data-name="Vector" d="M5.753,4.626a3.717,3.717,0,0,1-2.34-.79l-3.13-2.5a.747.747,0,1,1,.93-1.17l3.13,2.5a2.386,2.386,0,0,0,2.81,0l3.13-2.5a.738.738,0,0,1,1.05.12.738.738,0,0,1-.12,1.05l-3.13,2.5A3.67,3.67,0,0,1,5.753,4.626Z" transform="translate(562.247 260.244)" fill="#7d7d7d"></path>
									<path id="Vector-3" data-name="Vector" d="M0,0H24V24H0Z" transform="translate(556 252)" fill="none" opacity="0"></path>
								  </g>
								</g>
							  </svg> </i>
							<span class="nav-text">Messages</span>
						</a>
                       
                    </li>
					@endif
						@if(session('ViewUploadedFiles')==1)
					<li><a class="ai-icon" href="{{route('uploadedfiles')}}" aria-expanded="false">
						<i> <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="24" height="24" x="0" y="0" viewBox="0 0 24 24" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M22 13a1 1 0 0 0-1 1v4.213A2.79 2.79 0 0 1 18.213 21H5.787A2.79 2.79 0 0 1 3 18.213V14a1 1 0 0 0-2 0v4.213A4.792 4.792 0 0 0 5.787 23h12.426A4.792 4.792 0 0 0 23 18.213V14a1 1 0 0 0-1-1Z" fill="#000000" opacity="1" data-original="#000000"></path><path d="M6.707 8.707 11 4.414V17a1 1 0 0 0 2 0V4.414l4.293 4.293a1 1 0 0 0 1.414-1.414l-6-6a1 1 0 0 0-1.414 0l-6 6a1 1 0 0 0 1.414 1.414Z" fill="#000000" opacity="1" data-original="#000000"></path></g></svg></i>
						<span class="nav-text">Uploaded files</span>
					</a>
				   
				</li>
					@endif
					@if(session('ViewPromotionsList')==1)	
					<li><a class="ai-icon" href="{{route('list-coupon')}}" aria-expanded="false">
						<i> <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="25" height="25" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="m476.861 159.118-12.084-34.032 1.632-9.256c2.476-13.983-2.047-28.332-12.092-38.39l-26.666-26.666 32.681-32.681c3.125-3.125 3.125-8.186 0-11.311s-8.186-3.125-11.311 0L416.34 39.463 389.677 12.8C379.631 2.762 365.352-1.788 351.291.7l-73.639 12.987a43.352 43.352 0 0 0-23.239 12.104L45.331 234.87c-17.026 17.018-17.038 44.599 0 61.629l94.141 94.138-4.489 16.238c-6.575 23.757 7.341 48.314 31.113 54.887l175.751 48.587c23.808 6.574 48.361-7.495 54.891-31.113l81.075-293.302c2.462-8.869 2.126-18.142-.952-26.816zM56.642 246.181 265.724 37.102a27.44 27.44 0 0 1 14.709-7.659l73.639-12.987a27.579 27.579 0 0 1 24.294 7.655l26.664 26.662-23.284 23.283c-9.62-4.633-21.926-3.182-29.705 4.603-10.038 10.038-10.038 26.372 0 36.417 10.069 10.053 26.403 10.018 36.417-.004 8.052-8.056 9.53-20.115 4.664-29.77l23.218-23.219 26.665 26.664a27.612 27.612 0 0 1 7.655 24.298l-12.983 73.631a27.537 27.537 0 0 1-7.663 14.713L220.933 410.472c-10.421 10.421-28.582 10.421-39.003 0L56.642 285.187c-10.772-10.772-10.782-28.224 0-39.006zM377.147 89.97c3.796 3.8 3.796 9.987 0 13.791a9.764 9.764 0 0 1-13.795 0c-3.796-3.804-3.796-9.991 0-13.787a9.724 9.724 0 0 1 13.795-.004zm85.247 91.696-81.075 293.306c-4.203 15.217-20.044 24.145-35.206 19.958l-175.751-48.587c-15.238-4.209-24.177-19.959-19.958-35.202l2.073-7.499 18.143 18.142c17.019 17.035 44.587 17.038 61.625 0l209.082-209.078c6.351-6.347 10.53-14.381 12.1-23.243l5.718-32.427 2.641 7.438a28.41 28.41 0 0 1 .608 17.192z" fill="#000000" opacity="1" data-original="#000000"></path><path d="M309.086 234.299a8.003 8.003 0 0 0-7.999-8.003l-136.474-.062c-4.413 0-7.999 3.578-7.999 7.995s3.578 7.999 7.999 8.003l136.474.062c4.414 0 7.999-3.577 7.999-7.995zM205.685 262.569c-9.163 9.171-9.163 24.079 0 33.234 9.148 9.156 24.077 9.157 33.23.004 9.234-9.242 9.149-24.097 0-33.238-8.897-8.909-24.309-8.917-33.23 0zm21.919 21.919v.004c-2.812 2.804-7.796 2.808-10.608-.004-2.929-2.922-2.921-7.683 0-10.604 2.952-2.952 7.622-2.986 10.608 0a7.474 7.474 0 0 1 0 10.604zM260.014 205.967v-.004c9.155-9.163 9.155-24.071 0-33.238-9.178-9.163-24.091-9.147-33.238.004-9.155 9.163-9.155 24.071 0 33.234 9.133 9.141 24.054 9.172 33.238.004zm-21.927-21.923c2.939-2.946 7.602-3.01 10.616-.004 2.921 2.925 2.921 7.687 0 10.612-2.812 2.804-7.804 2.804-10.616 0-2.921-2.925-2.921-7.686 0-10.608z" fill="#000000" opacity="1" data-original="#000000"></path></g></svg></i>
						<span class="nav-text">Promotions</span>
					</a>
				   
				</li>
@endif
				<li><a class="has-arrow ai-icon" href="javascript:void()" aria-expanded="false">
					<i>  
						<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="25" height="25" x="0" y="0" viewBox="0 0 682.667 682.667" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><defs><clipPath id="a" clipPathUnits="userSpaceOnUse"><path d="M0 512h512V0H0Z" fill="#000000" opacity="1" data-original="#000000"></path></clipPath></defs><g clip-path="asset(#a)" transform="matrix(1.33333 0 0 -1.33333 0 682.667)"><path d="M0 0a191.783 191.783 0 0 0 49.719-20.638l15.688 15.69a32.121 32.121 0 0 0 22.727 9.415 32.116 32.116 0 0 0 22.718-9.415l22.718-22.719a32.11 32.11 0 0 0 9.415-22.718 32.113 32.113 0 0 0-9.415-22.726L117.881-88.8a191.838 191.838 0 0 0 20.638-49.719h22.147c17.746 0 32.134-14.388 32.134-32.133v-32.134c0-17.745-14.388-32.133-32.134-32.133h-22.147a191.831 191.831 0 0 0-20.638-49.718l15.689-15.689a32.117 32.117 0 0 0 9.415-22.727 32.11 32.11 0 0 0-9.415-22.718l-22.718-22.718a32.112 32.112 0 0 0-22.718-9.415 32.117 32.117 0 0 0-22.727 9.415L49.719-352.8A191.78 191.78 0 0 0 0-373.437v-22.148c0-17.746-14.388-32.134-32.134-32.134h-32.133c-17.746 0-32.133 14.388-32.133 32.134v22.148a191.78 191.78 0 0 0-49.719 20.637l-15.689-15.689a32.115 32.115 0 0 0-22.726-9.415 32.108 32.108 0 0 0-22.718 9.415l-22.719 22.718a32.114 32.114 0 0 0-9.415 22.718 32.121 32.121 0 0 0 9.415 22.727l15.69 15.689a191.796 191.796 0 0 0-20.638 49.718h-22.147c-17.746 0-32.134 14.388-32.134 32.133v32.134c0 17.745 14.388 32.133 32.134 32.133h22.147A191.803 191.803 0 0 0-214.281-88.8l-15.69 15.689a32.117 32.117 0 0 0-9.415 22.726 32.114 32.114 0 0 0 9.415 22.718l22.719 22.719a32.112 32.112 0 0 0 22.718 9.415 32.119 32.119 0 0 0 22.726-9.415l15.689-15.69A191.783 191.783 0 0 0-96.4 0v22.148c0 17.746 14.387 32.133 32.133 32.133h32.133C-14.388 54.281 0 39.894 0 22.148Z" style="stroke-width:30;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;stroke-dasharray:none;stroke-opacity:1" transform="translate(304.2 442.719)" fill="none" stroke="#000000" stroke-width="30" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-dasharray="none" stroke-opacity="" data-original="#000000" class=""></path><path d="M0 0c53.205 0 96.4-43.195 96.4-96.4 0-53.204-43.195-96.4-96.4-96.4-53.205 0-96.4 43.196-96.4 96.4C-96.4-43.195-53.205 0 0 0Z" style="stroke-width:30;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;stroke-dasharray:none;stroke-opacity:1" transform="translate(256 352.4)" fill="none" stroke="#000000" stroke-width="30" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-dasharray="none" stroke-opacity="" data-original="#000000" class=""></path></g></g></svg></i>
					<span class="nav-text">Settings</span>
				</a>
				<ul aria-expanded="false">
					@if(session('SMTP')==1)	
					<li><a href="{{route('smtp-settings')}}">SMTP Settings</a></li>
				@endif
				@if(session('SettingsGeneral')==1)	
					<li><a href="{{route('general-settings')}}">General Settings</a></li>
				@endif
				@if(session('Governarate')==1)
					<li><a href="{{route('governorate')}}">Governorate</a></li>
				@endif
				
				 @if(session('Areas')==1)
					<li><a href="{{route('areas')}}">Areas</a></li>
				@endif
				<li><a href="{{route('listamenetise')}}">Ameneties</a></li>
				@if(session('PrivacyPolicy')==1)
					<li><a href="{{route('privacy-policy')}}">Privacy Policy</a></li>
				@endif
				@if(session('TermsandConditions')==1)
					<li><a href="{{route('terms')}}">Terms & Condition</a></li>
				 @endif
				 @if(session('TermsandConditions')==1)
					<li><a href="{{route('contactus')}}">Contact Us</a></li>
				 @endif
 @if(session('TermsandConditions')==1)
					<li><a href="{{route('faqs')}}">Faqs</a></li>
				 @endif
				 @if(session('Areas')==1)
					<li><a href="{{route('add-splash-screen')}}">Splash Screen</a></li>
				@endif
				</ul>
			</li>


				<li><a class="has-arrow ai-icon" href="javascript:void()" aria-expanded="false">
					<i>  
						<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="25" height="25" x="0" y="0" viewBox="0 0 74 74" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M22.5 49.36H3a1 1 0 0 1-1-1v-4.5a9.911 9.911 0 0 1 9.9-9.9h9.38a9.848 9.848 0 0 1 9.767 8.322 1 1 0 0 1-1.975.316 7.856 7.856 0 0 0-7.792-6.638H11.9a7.909 7.909 0 0 0-7.9 7.9v3.5h18.5a1 1 0 0 1 0 2zM16.587 31.081a7.746 7.746 0 1 1 7.745-7.746 7.754 7.754 0 0 1-7.745 7.746zm0-13.491a5.746 5.746 0 1 0 5.745 5.745 5.752 5.752 0 0 0-5.745-5.745zM71 49.36H51.5a1 1 0 0 1 0-2H70v-3.5a7.909 7.909 0 0 0-7.9-7.9h-9.38a7.856 7.856 0 0 0-7.792 6.64 1 1 0 0 1-1.975-.316 9.848 9.848 0 0 1 9.767-8.324h9.38a9.911 9.911 0 0 1 9.9 9.9v4.5a1 1 0 0 1-1 1zM57.413 31.081a7.746 7.746 0 1 1 7.746-7.746 7.755 7.755 0 0 1-7.746 7.746zm0-13.491a5.746 5.746 0 1 0 5.746 5.745 5.751 5.751 0 0 0-5.746-5.745z" fill="#000000" opacity="1" data-original="#000000"></path><path d="M52.325 57.41h-30.65a1 1 0 0 1-1-1v-4.072A11.049 11.049 0 0 1 31.712 41.3h10.576a11.049 11.049 0 0 1 11.037 11.038v4.072a1 1 0 0 1-1 1zm-29.65-2h28.65v-3.072a9.047 9.047 0 0 0-9.037-9.038H31.712a9.047 9.047 0 0 0-9.037 9.037zM37 37.8a8.609 8.609 0 1 1 8.608-8.609A8.617 8.617 0 0 1 37 37.8zm0-15.22a6.609 6.609 0 1 0 6.608 6.608A6.615 6.615 0 0 0 37 22.58z" fill="#000000" opacity="1" data-original="#000000"></path></g></svg></i>
					<span class="nav-text">Staffs</span>
				</a>
				<ul aria-expanded="false">
				@if(session('ViewStaffs')==1)
					<li><a href="{{route('list-admin-users')}}">All Staffs</a></li>
				 @endif
				 @if(session('AddStaffs')==1)
					<li><a href="{{route('add-admin-user')}}">Add Staff</a></li>
				@endif
				   

				</ul>
			</li>
                    
                   
                </ul>
				
				<div class="copyright">
					<p><strong>{{ config('app.name') }}</strong> © {{ date('Y') }} All Rights Reserved</p>
					<p>Powered By Chrisans Web Solutions</p>
				</div>
			</div>
        </div>
    
	
         @yield('content')
      
        <footer class="footer">
            <div class="copyright">
                  <p>Copyright © 2023 Designed & Developed by <a href="https://chrisansgroup.com/" target="_blank">Chrisans Web Solutions</a></p>
            </div>
        </footer>
        <!--**********************************
            Footer end
        ***********************************-->
    </div>
    <!--**********************************
        Main wrapper end
    ***********************************-->

    <!--**********************************
        Scripts
    ***********************************-->
    <!-- Required vendors -->
	
    <script src="{{ asset('vendor/global/global.min.js') }}"></script>
	<script src="{{ asset('vendor/bootstrap-select/js/bootstrap-select.min.js') }}"></script>
	<script src="{{ asset('vendor/owl-carousel/owl.carousel.js') }}"></script>
	 <script src="{{ asset('vendor/select2/js/select2.full.min.js') }}"></script>
<script src="{{ asset('vendor/bootstrap-select/js/bootstrap-select.min.js') }}"></script>
	 
	<!-- Chart ChartJS plugin files -->
    <script src="{{ asset('vendor/chart-js/chart.bundle.min.js') }}"></script>
    <script src="{{ asset('js/plugins-init/chartjs-init.js') }}"></script>
	<!-- Dashboard 1 -->
	<script src="{{ asset('js/dashboard/dashboard-1.js') }}"></script>

	 
	 <script src="{{ asset('vendor/moment/moment.min.js') }}"></script>
    <script src="{{ asset('vendor/bootstrap-daterangepicker/daterangepicker.js') }}"></script>
    <!-- clockpicker -->
    <script src="{{ asset('vendor/clockpicker/js/bootstrap-clockpicker.min.js') }}"></script>
    <!-- asColorPicker -->
    <script src="{{ asset('vendor/jquery-asColor/jquery-asColor.min.js') }}"></script>
    <script src="{{ asset('vendor/jquery-asGradient/jquery-asGradient.min.js') }}"></script>
    <script src="{{ asset('vendor/jquery-asColorPicker/js/jquery-asColorPicker.min.js') }}"></script>
    <!-- Material color picker -->
    <script src="{{ asset('vendor/bootstrap-material-datetimepicker/js/bootstrap-material-datetimepicker.js') }}"></script>
    <!-- pickdate -->
    <script src="{{ asset('vendor/pickadate/picker.js') }}"></script>
    <script src="{{ asset('vendor/pickadate/picker.time.js') }}"></script>
    <script src="{{ asset('vendor/pickadate/picker.date.js') }}"></script>



    <!-- Daterangepicker -->
    <script src="{{ asset('js/plugins-init/bs-daterange-picker-init.js') }}"></script>
    <!-- Clockpicker init -->
    <script src="{{ asset('js/plugins-init/clock-picker-init.js') }}"></script>
    <!-- asColorPicker init -->
    <script src="{{ asset('js/plugins-init/jquery-asColorPicker.init.js') }}"></script>
    <!-- Material color picker init -->
    <script src="{{ asset('js/plugins-init/material-date-picker-init.js') }}"></script>
    <!-- Pickdate -->
    <script src="{{ asset('js/plugins-init/pickadate-init.js') }}"></script>
	
	<script src="{{ asset('vendor/datatables/js/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('js/plugins-init/datatables.init.js') }}"></script>
	<script src="{{ asset('js/dashboard/cms.js') }}"></script>
	<script src="{{ asset('vendor/dropzone/dropzone.js') }}"></script>
  
    <script src="{{ asset('js/custom.min.js') }}"></script>
	<script src="{{ asset('js/deznav-init.js') }}"></script>
	<script src="{{ asset('js/sweetalert.min.js') }}"></script>
 <script src="{{ asset('vendor/summernote/js/summernote.min.js') }}"></script>
    <!-- Summernote init -->
    <script src="{{ asset('js/plugins-init/summernote-init.js') }}"></script>
	 <script src="{{ asset('js/plugins-init/select2-init.js') }}"></script>
	<script src="{{ asset('js/jquery.timepicker.min.js') }}"></script>

	<script src="https://cdn.datatables.net/buttons/2.3.5/js/dataTables.buttons.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
	<script src="https://cdn.datatables.net/buttons/2.3.5/js/buttons.html5.min.js"></script>
	<script src="https://cdn.datatables.net/buttons/2.3.5/js/buttons.print.min.js"></script>
	

    <script>
       $('#promotion_type').change(function(){
			type=$(this).val();
		
	if(type=="service"){
	    $('.service').show();
	    $('.vendor').hide();
	}
	else{
	     $('.vendor').show();
	     $('.service').hide();
	}
    
});
      $('.promo-venors-applicable').change(function(){
			type=$(this).val();
		
	if(type==1){
	    $('.vendors').show();
	   
	  
	   
	}
	else{
	     $('.vendors').hide();
	    
	}
    
});

   $('.promo-user-applicable').change(function(){
			type=$(this).val();
		
	if(type=="Specific Users"){
	    $('.usersdiv').show();
	   
	}
	else{
	     $('.usersdiv').hide();
	    
	}
    
});


	 @if(isset($ordertotals))
	var areaChart3 = function(){	
		//gradient area chart
		if(jQuery('#areaChart_3').length > 0 ){
			const areaChart_3 = document.getElementById("areaChart_3").getContext('2d');
			
			areaChart_3.height = 100;

			new Chart(areaChart_3, {
				type: 'line',
				data: {
					defaultFontFamily: 'Poppins',
					labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul"],
					datasets: [
						{
							label: "My First dataset",
							data: [{{$ordertotals}}],
							borderColor: 'rgb(11, 42, 151)',
							borderWidth: "1",
							tension:0.5,
							fill:true,
							backgroundColor: 'rgba(11, 42, 151, .5)'
						}
					]
				},
				options: {
					plugins:{
						legend:false,
					},
					scales: {
						y:{
							max: {{$max}}, 
							min: 0, 
							ticks: {
								beginAtZero: true, 
								stepSize: 20, 
								padding: 10
							}
						},
						x:{ 
							ticks: {
								padding: 5
							}
						}
					}
				}
			});
		}
	}
	@endif
	
	
	 @if(isset($totalSales))
	var areaChart3 = function(){
	   
		//gradient area chart
		if(jQuery('#areaChart_3').length > 0 ){
			const areaChart_3 = document.getElementById("areaChart_3").getContext('2d');
			
			areaChart_3.height = 100;

			new Chart(areaChart_3, {
				type: 'line',
				data: {
					defaultFontFamily: 'Poppins',
					labels:  ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul",'Aug','Sep','Oct','Nov','Dec'],
					datasets: [
						{
							label: "My First dataset",
							data: [{{$totalSales}}],
							borderColor: 'rgb(11, 42, 151)',
							borderWidth: "1",
							tension:0.5,
							fill:true,
							backgroundColor: 'rgba(11, 42, 151, .5)'
						}
					]
				},
				options: {
					plugins:{
						legend:false,
					},
					scales: {
						y:{
					
							min: 0, 
							ticks: {
								beginAtZero: true, 
								stepSize: 20, 
								padding: 10
							}
						},
						x:{ 
							ticks: {
								padding: 5
							}
						}
					}
				}
			});
		}
	}
	@endif
        function readURL(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            $('#imagePreview').css('background-image', 'asset('+e.target.result +')');
            $('#imagePreview').hide();
            $('#imagePreview').fadeIn(650);
        }
        reader.readAsDataURL(input.files[0]);
    }
}
$("#imageUpload").on('change',function() {
    
    readURL(this);
});
    $('.remove-img').on('click', function() {
        var imageUrl = "images/no-img-avatar.png";
        $('.avatar-preview, #imagePreview').removeAttr('style');
        $('#imagePreview').css('background-image', 'asset(' + imageUrl + ')');
    });
	var datatable1 = $('#example85').DataTable();
	
    $(".categorysearch85").click(function(){
    
  
		
        var value = $('#searchcategory').val();
       
        datatable1.columns(1).search(value).draw();
    });
	$(".Deleteservicecategorysvendor").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "You will not be able to recover this action",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/servicecategory-vendor/delete/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Service Category Deleted Successfully", "success");
                    //$('.alert').show();	
                     $("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
	$(".servicecategoryenable").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/servicecategory/enable/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Enable)=="success")
                    {
                    swal("Success!", "Service Status Updated Successfully", "success");
                    //$('.alert').show();	
                     //$("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});		
</script>
<script type="text/javascript">
      var base_url = '{{URL::to("/")}}';
</script>
<script>
$(".updatecommision").click(function(){
			id=$(this).attr('data-id');
			vendor=$(this).attr('data-vendor');
			commision=$('#'+id).val();
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/vendor/update-commision/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}",vendor:vendor,commision:commision},
                success: function(msg){
                   
                    if($.trim(msg.Enable)=="success")
                    {
                    swal("Success!", "Commision Updated Successfully", "success");
                    //$('.alert').show();	
                     //$("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
$(".changesellerpermission").click(function(){
			id=$(this).attr('data-id');
			field1=$(this).attr('id');
			
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/vendor/update-permission/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}",field:field1},
                success: function(msg){
                   
                    if($.trim(msg.Enable)=="success")
                    {
                    swal("Success!", "Permission Updated Successfully", "success");
                    //$('.alert').show();	
                     //$("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});

$(".changepermission").click(function(){
			id=$(this).attr('data-id');
			field1=$(this).attr('id');
			
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/admin-user/update-permission/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}",field:field1},
                success: function(msg){
                   
                    if($.trim(msg.Enable)=="success")
                    {
                    swal("Success!", "Permission Updated Successfully", "success");
                    //$('.alert').show();	
                     //$("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});




$(function() {
	 var datatable8 = $('#example8').DataTable();
	$("#searchuser1").keyup(function(){
		 var value = $('#searchuser1').val();

  datatable8.columns(2).search(value).draw();
});
	 var datatable7 = $('#example7').DataTable();
      $(".vendorsearch7").click(function(){
    
  
		
        var value = $('#searchuser').val();
       
        datatable7.columns(1).search(value).draw();
    });
   $("#sort7").change(function(){
    
  
		
        var value = $(this).val();
       if(value==1)
	   {
		   $('#example7').DataTable().order([0, 'desc']).draw();
		   
	   }
	   if(value==2)
	   {
		  $('#example7').DataTable().order([1, 'desc']).draw();
		   
	   }
       
    });
    $("#usertype-select").change(function(){
    
  
		
        var value = $(this).val();
       
        datatable7.columns(7).search(value).draw();
        
       
   
       
    });
    $("#vendor-select").change(function(){
    
  
		
        var value = $(this).val();
       
        datatable7.columns(2).search(value).draw();
        
        var filteredCount = datatable7.rows({ filter: 'applied' }).count();

    // Display the count (you can modify where you want to show it)
    console.log("Total rows after filtering: " + filteredCount);
    
    // Optionally, update an HTML element to display the count
     if(value==""){
             $('#filtered-count').html("");
        }
        else{
             $('#filtered-count').html("<b>No Of Customers:</b> "+ filteredCount);
        }
   
       
    });
  
	if ($.fn.DataTable.isDataTable('#example2')) {
    $('#example2').DataTable().destroy();
    }
    toBase64('https://projects.crisance.com/bookme/adminpanel/public/images/logo-full.png', function(base64) {
	 var  datatable4 = $('#example2').DataTable({
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'pdfHtml5',
               	"text": '<span style="color: white; margin-right:10px">Export As PDF</span>',
               
    			"titleAttr": 'PDF',
    			 orientation : 'landscape',
    			className: 'btn btn-primary btn-sm export_pdf',
    			filename: 'user_report',
    				customize: function (doc) {
                       doc.content.splice(0, 1); // Remove default title
                    doc.content.unshift({
                        margin: [0, 0, 0, 12],
                        alignment: 'center',
                        image: base64,
                        width: 200  // Adjust width if necessary
                    });
                    }
            },
            {
			"extend": 'excel',
			"text": '<span style="color: white; margin-right:10px">Export As Excel</span>',
			"titleAttr": 'Excel',
			className: 'btn btn-primary btn-sm export_excel',
			filename: 'user_report',
		  }
        ]
    });
    });
   $(".vendorsearch4").click(function(){
    
  
		
        var value = $('#searchuser').val();
       
        datatable4.columns(4).search(value).draw();
    });
   $("#sort4").change(function(){
    
  
		
        var value = $(this).val();
       if(value==1)
	   {
		   $('#example2').DataTable().order([0, 'desc']).draw();
		   
	   }
	   if(value==2)
	   {
		  $('#example2').DataTable().order([2, 'desc']).draw();
		   
	   }
       
    });
	
	if ($.fn.DataTable.isDataTable('#example6')) {
    $('#example6').DataTable().destroy();
    }
    toBase64('https://projects.crisance.com/bookme/adminpanel/public/images/logo-full.png', function(base64) {
	 var datatable3 = $('#example6').DataTable({
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'pdfHtml5',
               	"text": '<span style="color: white; margin-right:10px">Export As PDF</span>',
    			"titleAttr": 'PDF',
    			 orientation : 'landscape',
    			className: 'btn btn-primary btn-sm export_pdf',
    			filename: 'vendor_report',
    			customize: function (doc) {
                       doc.content.splice(0, 1); // Remove default title
                    doc.content.unshift({
                        margin: [0, 0, 0, 12],
                        alignment: 'center',
                        image: base64,
                        width: 200  // Adjust width if necessary
                    });
                    }
            },
            {
			"extend": 'excel',
			"text": '<span style="color: white; margin-right:10px">Export As Excel</span>',
			"titleAttr": 'Excel',
			className: 'btn btn-primary btn-sm export_excel',
			filename: 'vendor_report',
		  }
        ]
    }); 
    });   
	    $("#vendor-vendor-select").change(function(){
    
  
		var value = $(this).val();
      
        datatable3.columns(1).search(value).draw();
    });
   $(".vendorsearch3").click(function(){
    
  
		
        var value = $('#searchuser').val();
       
        datatable3.columns(1).search(value).draw();
    });
   $("#sort3").change(function(){
    
  
		
        var value = $(this).val();
       if(value==1)
	   {
		   $('#example6').DataTable().order([0, 'desc']).draw();
		   
	   }
	   if(value==2)
	   {
		  $('#example6').DataTable().order([1, 'desc']).draw();
		   
	   }
       
    });
   if ($.fn.DataTable.isDataTable('#example5')) {
    $('#example5').DataTable().destroy();
    }
    function toBase64(url, callback) {
    var xhr = new XMLHttpRequest();
    xhr.onload = function() {
        var reader = new FileReader();
        reader.onloadend = function() {
            callback(reader.result);
        }
        reader.readAsDataURL(xhr.response);
    };
    xhr.open('GET', url);
    xhr.responseType = 'blob';
    xhr.send();
}
toBase64('https://projects.crisance.com/bookme/adminpanel/public/images/logo-full.png', function(base64) {
    var datatable2 = $('#example5').DataTable({
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'pdfHtml5',
               	"text": '<span style="color: white; margin-right:10px">Export As PDF</span>',
    			"titleAttr": 'PDF',
    			 orientation : 'landscape',
    			className: 'btn btn-primary btn-sm export_pdf',
    			filename: 'sale_report',
    			customize: function (doc) {
                        doc.content.splice(0, 1); // Remove default title
                    doc.content.unshift({
                        margin: [0, 0, 0, 12],
                        alignment: 'center',
                        image: base64,
                        width: 200  // Adjust width if necessary
                    });
                    }
            },
            {
			"extend": 'excel',
			"text": '<span style="color: white; margin-right:10px">Export As Excel</span>',
			"titleAttr": 'Excel',
			className: 'btn btn-primary btn-sm export_excel',
			filename: 'sale_report',
		  }
        ]
    }); 
}); 
   $("#vendor-sale-select").change(function(){
    
  
		var value = $(this).val();
      
        datatable2.columns(3).search(value).draw();
    });
    $(".vendorsearch2").click(function(){
    
  
		
        var value = $('#searchuser').val();
       
        datatable2.columns(4).search(value).draw();
    });
   $("#sort2").change(function(){
    
  
		
        var value = $(this).val();
       if(value==1)
	   {
		   $('#example5').DataTable().order([0, 'desc']).draw();
		   
	   }
	   if(value==2)
	   {
		  $('#example5').DataTable().order([3, 'desc']).draw();
		   
	   }
       
    });
 var datatable1 = $('#example4').DataTable();
   $("#sort1").change(function(){
    
  
		
        var value = $(this).val();
       if(value==1)
	   {
		   $('#example4').DataTable().order([0, 'desc']).draw();
		   
	   }
	   if(value==2)
	   {
		  $('#example4').DataTable().order([4, 'desc']).draw();
		   
	   }
       
    });
	  $("#payment").change(function(){
    
  
		
        var value = $(this).val();
     
        datatable1.columns(7).search(value).draw();
    });
	
	$("#status1").change(function(){
    
  
		
        var value = $(this).val();
     
        datatable1.columns(9).search(value).draw();
    });
$(".vendorsearch1").click(function(){
    
  
		
        var value = $('#searchuser').val();
       
        datatable1.columns(3).search(value).draw();
    });
    var datatable = $('#example3').DataTable();
	var datatable10 = $('#example10').DataTable();
    $(".categorysearch3").click(function(){
    
  
		
        var value = $('#searchcategory').val();
        datatable10.columns(1).search(value).draw();
        datatable.columns(2).search(value).draw();
    });
   $("#single-select").change(function(){
    
  
		
        var value = $(this).val();
        datatable1.columns(5).search(value).draw();
        datatable.columns(2).search(value).draw();
    });
       
    
    
	 $("#single-select").change(function(){
    
  
		
        var value = $(this).val();
       
        datatable.columns(2).search(value).draw();
    });
	
	$(".vendorsearch").click(function(){
    
  
		
        var value = $('#searchuser').val();
       
        datatable.columns(1).search(value).draw();
    });
	
	 $("#sorting").change(function(){
    
  
		
        var value = $(this).val();
       if(value==2)
	   {
		   $('#example3').DataTable().order([1, 'asc']).draw();
		   
	   }
	   if(value==4)
	   {
		   $('#example3').DataTable().order([1, 'desc']).draw();
		   
	   }
	   if(value==3)
	   {
		  $('#example3').DataTable().order([2, 'asc']).draw();
		   
	   }
	   if(value==5)
	   {
		  $('#example3').DataTable().order([2, 'desc']).draw();
		   
	   }
	    if(value==6)
	   {
		  $('#example3').DataTable().order([3, 'asc']).draw();
		   
	   }
	    if(value==7)
	   {
		  $('#example3').DataTable().order([3, 'desc']).draw();
		   
	   }
       
    });
 

$(".enablesplash").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/splash/enable/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Enable)=="success")
                    {
                    swal("Success!", "Splash Status Updated Successfully", "success");
                    //$('.alert').show();	
                     //$("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});

$(".deletesplash").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "You will not be able to recover this action",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/splash/delete/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Splash Screen  Deleted Successfully", "success");
                    //$('.alert').show();	
                     $("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});


$(".Enablecoupon").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/coupon/enable/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Enable)=="success")
                    {
                    swal("Success!", "Coupon Status Updated Successfully", "success");
                    //$('.alert').show();	
                     //$("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});


$(".DeleteAmenety").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/delete-ameneties/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Ameneties Deleted Successfully", "success");
                    //$('.alert').show();	
                     $("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});




$(".Deletecoupon").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/delete-coupon/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Coupon Deleted Successfully", "success");
                    //$('.alert').show();	
                     //$("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});


$(".statusbooking").click(function(){
			id=$(this).attr('data-id');
			status1=$(this).attr('data-status');
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/booking/status/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}",status:status1},
                success: function(msg){
                   
                    if($.trim(msg.Update)=="success")
                    {
                    swal("Success!", "Booking Status Updated Successfully", "success");
                    //$('.alert').show();	
                     //$("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});

$(".showmodalpopup").click(function(){
			id=$(this).attr('data-id');
			startdate=$(this).attr('data-start');
			enddate=$(this).attr('data-end');
			content=$(this).attr('data-content');
			$('#vendorid').val(id);
			$('#datepicker').val(startdate);
			$('#datepicker1').val(enddate);
			$('#contractdescription').val(content);
			 $('#exampleModalpopover').show();
			
	});		
$(".enabledvendor").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/vendor/enable/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Vendor Status Updated Successfully", "success");
                    //$('.alert').show();	
                     //$("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
			
			
			
			
			$(".changebannerstatus").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/promotionbanner/enable/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Enable)=="success")
                    {
                    swal("Success!", "Promo Banner Status Updated Successfully", "success");
                    //$('.alert').show();	
                     //$("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
			
			
			
			$(".Deletepromobanner").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "You will not be able to recover this action",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/promotionbanner/delete/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Promo Banner Deleted Successfully", "success");
                    //$('.alert').show();	
                     $("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
			
			
			
			
			

$(".deleteattachment").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "You will not be able to recover this action",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/vendor/deleteattachment/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Attachment Deleted Successfully", "success");
                    //$('.alert').show();	
                     $("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
$(".deletephotos").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "You will not be able to recover this action",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/vendor/deletephoto/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Photo Deleted Successfully", "success");
                    //$('.alert').show();	
                     $("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
$(".deletegov").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "You will not be able to recover this action",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/governorate/delete/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Governorate Deleted Successfully", "success");
                    //$('.alert').show();	
                     $("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
			
			$(".enablegov").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/governorate/enable/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Governorate Status Updated Successfully", "success");
                    //$('.alert').show();	
                     //$("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
			
				$(".deletefaq").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "You will not be able to recover this action",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/faq/delete/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Faq Deleted Successfully", "success");
                    //$('.alert').show();	
                     $("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
			
			$(".deletearea").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "You will not be able to recover this action",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/area/delete/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Area Deleted Successfully", "success");
                    //$('.alert').show();	
                     $("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
			
			
			$(".enablearea").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/area/enable/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Area Status Updated Successfully", "success");
                    //$('.alert').show();	
                     //$("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
			
			$(".enableuser").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/customer/enable/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Enable)=="success")
                    {
                    swal("Success!", "Customer Status Updated Successfully", "success");
                    //$('.alert').show();	
                     //$("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
			
			$(".DeleteCategory").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "You will not be able to recover this action",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/service-category/delete/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Service Category Deleted Successfully", "success");
                    //$('.alert').show();	
                     $("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
			
			
			$(".Deleteservices").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "You will not be able to recover this action",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/services/delete/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Service  Deleted Successfully", "success");
                    //$('.alert').show();	
                     $("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
			
			var $rows = $('#table tr:not(:first)');
$('#search').keyup(function() {
    var val = $.trim($(this).val()).replace(/ +/g, ' ').toLowerCase();

    $rows.show().filter(function() {
        var text = $(this).text().replace(/\s+/g, ' ').toLowerCase();
        return !~text.indexOf(val);
    }).hide();
});

$(".modalpopup").click(function(e){
	e.preventDefault();
	
			$('.bd-example-modal-lg').modal('show');
			count=$(this).attr('data-count');
			loc=$(this).attr('data-loc');
			store=$(this).attr('data-store');
			$('#filecount').val(count);
			$('#filelocation').val(loc);
			$('#datastore').val(store);
			});
			
			
$( "body" ).on( "click", ".selectimage", function(e) {

	count=$('#filecount').val();
	loc==$('#filelocation').val();
	store=$('#datastore').val();
	checkedcount=$('input[name="images[]"]:checked').length;
	files='';
	if(checkedcount<=count)
	{
	$("input[name='images[]']:checked").each(function ()
{
	fil=$(this).val()+',';
	file='<?php echo asset('/');?>'+$(this).val();
$('#'+loc).append("<img src="+file+" width='80'>");
files=files+fil;

});
$('#'+store).val(files);
$('.bd-example-modal-lg').modal('hide');
	}
	else{
	swal("Error!","You can select only "+count+" Image" , "error");	
	}
	});
});
</script> 
	
	<script>
		function carouselReview(){
			/*  testimonial one function by = owl.carousel.js */
			jQuery('.testimonial-one').owlCarousel({
				nav:true,
				loop:true,
				autoplay:true,
				margin:30,
				dots: false,
				left:true,
				navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
				responsive:{
					0:{
						items:1
					},
					484:{
						items:2
					},
					882:{
						items:3
					},	
					1200:{
						items:2
					},			
					
					1540:{
						items:3
					},
					1740:{
						items:4
					}
				}
			})			
		}
		jQuery(window).on('load',function(){
			setTimeout(function(){
				carouselReview();
			}, 1000); 
		});
		
		$('#sel1').on('change', function() {
			var value = $(this).val();
			
			files='';
			if(value==1)
			{
				$("input[name='images1[]']:checked").each(function ()
{
	fil=$(this).val()+',';
	file='<?php echo asset('/');?>'+$(this).val();
	
	files=files+fil;
	});	

	
	  $.ajax({
                url: base_url+"/files/delete",
                type:   'POST',
                 data: {_token: "{{csrf_token()}}",files1:files},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Images  Deleted Successfully", "success");
					window.location.href='';
                    //$('.alert').show();	
                     
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			
	
	}
			
		});	
			
	</script>
	
	
	<div class="modal fade bd-example-modal-lg" tabindex="-1" role="dialog" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Media File</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal">
					</button>
				</div>
				<div class="modal-body">
					<div class="row align-items-center justify-content-between">
<div class="col-xl-7 col-xxl-7 col-8">
	<ul class="nav nav-pills mb-4 light">
		<li class=" nav-item">
			<a href="#navpills-1" class="nav-link active" data-bs-toggle="tab" aria-expanded="false">Select File</a>
		</li>
		<li class="nav-item">
			<a href="#navpills-2" class="nav-link" data-bs-toggle="tab" aria-expanded="false">Upload Media</a>
		</li>
		
	</ul>
</div>

						<div class="col-xl-2 col-xxl-2 col-3 text-right mb-4">
							<a href="javascript:void(0);"  class="btn btn-primary selectimage"> Select Image</a>
							
							</div>
					</div>
					
				
					<div class="tab-content">
						<div id="navpills-1" class="tab-pane active">
							<div class="row">
								<div class="col-md-12"> 

									<div class="row" id="mediaappend">
									<?php
									$i=1;
									if ($handle = opendir(public_path('uploads'))) {

								while (false !== ($entry = readdir($handle))) {
								if ($entry != "." && $entry != "..") { ?>
										<div class="col-xl-3 col-xxl-3 col-md-4 col-sm-6">
											<div class="card">
												<div class="card-body product-grid-card">
													<div class="new-arrival-product">
														<div class="new-arrivals-img-contnent">
															<div class="form-check custom-checkbox mb-3 product-check">
																<input type="checkbox" name="images[]" class="form-check-input" value="uploads/<?php echo $entry; ?>" id="customCheckBox<?php echo $i; ?>" >
																<?php $allowedMimeTypes = ['image/jpeg','image/gif','image/png','image/bmp','image/svg+xml','image/webp'];
																
																$contentType = mime_content_type('public/uploads/'.$entry);
																
																?>
															</div>
															<?php if(in_array($contentType, $allowedMimeTypes) ){ ?>
															<img class="img-fluid rounded" src="<?php echo asset('/');?>uploads/<?php echo $entry; ?>" alt="">
															<?php } else { ?>
															<a href="<?php echo asset('/');?>uploads/<?php echo $entry; ?>">File</a>
															<?php }?>
														</div>
														<div class="new-arrival-content text-center mt-3">
															<h4><?php $file=str_replace("-", " ", $entry); echo preg_replace('/\.\w+$/', '', $file);?></h4>
															
															<span class="price"><?php $size = filesize('public/uploads/'.$entry);$units = array('B', 'KB', 'MB', 'GB', 'TB');
    $formattedSize = $size; for ($i = 0; $size >= 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
        $formattedSize = round($size, 2);
    } echo $formattedSize . ' ' . $units[$i];?></span>
														</div>
													</div>
												</div>
											</div>
										</div>
										<?php
										$i++;
										   }
    }
    closedir($handle);
}
?>
										
										
									
									
									
										
										
										
										
										
									</div>
								</div>
							</div>
						</div>
						<div id="navpills-2" class="tab-pane">
							<div class="row">
								<div class="col-xl-12 col-sm-12">
									<div class="cm-content-body  form excerpt">
									<form id="upload" action="{{route('uploadfiles')}}" name="upload" method="POST"  enctype='multipart/form-data'>
										{{csrf_field()}}
										<div class="card-body">
											<h5 class="mb-4"><i class="fa fa-paperclip"></i> Attatchment <span> *</span></h5>
											<label class="imagepicker imagepicker-add thumbnail">
												<input type="file" id="files" name="files[]" multiple />
											  </label>
											  <button type="submit" class="btn btn-primary my-2">Upload</button>
										</div>
										</form>
										<div style="display:none;"  class="alert alert-success"></div>
										<input id="filelocation" name="filelocation" type="hidden">
										<input id="filecount" name="filecount" type="hidden">
										<input id="datastore" name="datastore" type="hidden">
									</div>
									</div>
							</div>
						</div>
					
					</div>

				</div>
			
			</div>
		</div>
	</div>
	<script type="text/javascript">
var allowed_file_size = "10485760"; //allowed file size
var allowed_files = ['image/jpg', 'image/png', 'image/jpeg','image/gif']; //allowed file types
var border_color = "#C2C2C2"; 

var $upload = $('#upload');

$upload.on('submit', function(ev){
	
    ev.preventDefault();

	
 proceed = true;
  if(window.File && window.FileReader && window.FileList && window.Blob){
        var total_files_size = 0;
        $(this.elements['files'].files).each(function(i, ifile){
            if(ifile.value !== ""){ //continue only if file(s) are selected
                if(allowed_files.indexOf(ifile.type) === -1){ //check unsupported file
                    alert( ifile.name + " is unsupported file type!");
                    proceed = false;
                }
             total_files_size = total_files_size + ifile.size; //add file size to total size
            }
        }); 
       if(total_files_size > allowed_file_size){ 
            alert( "Make sure total file size is less than 1 MB!");
            proceed = false;
        }
    }
	
	  if(proceed){
		   swal("Uploading!", "Uploading Please Wait...", "success");
		 
    var post_url = base_url+"/uploadfiles"; //get form action url
        var request_method = $(this).attr("method"); //get form GET/POST method
        var form_data = new FormData(this); //constructs key/value pairs representing fields and values
        
        $.ajax({ //ajax form submit
            url : post_url,
            type: request_method,
            data : form_data,
            dataType : "json",
            contentType: false,
            cache: false,
            processData:false
        }).done(function(res){ //fetch server "json" messages when done
            if(res.type == "error"){
				$(".alert").show();
                $(".alert").html(res.text);
            }
            
            if(res.type == "done"){
				$('#upload')[0].reset();
               swal("Uploading!", "Media Uploaded Successfully", "success");
				$(".alert").show();
                $("#mediaappend").html(res.html);
            }
            
               if(res.type=='')
              
                {
 swal("Error!","Invalid Captcha" , "error");  
                } 
            
        });
	  }
});
</script>
<script>
	$(document).ready(function(){
	$('#timeInput').timepicker({
		timeFormat: 'H:mm',
    interval: 30,
   
    dynamic: false,
    dropdown: true,
    scrollbar: true,
	  
		});
		
	//var roundDate=$('#timeInput').val();
	
		$('#timeInput2').timepicker({
		timeFormat: 'H:mm',
    interval: 30,
    dynamic: false,
    dropdown: true,
    scrollbar: true
		});
	
	});
	</script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dom-to-image/2.6.0/dom-to-image.min.js"
    integrity="sha256-c9vxcXyAG4paArQG3xk6DjyW/9aHxai2ef9RpMWO44A=" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.3.5/jspdf.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.9.2/html2pdf.bundle.min.js"></script>


<script>
//     $('#downloadPDF').click(function () {
//     domtoimage.toPng(document.getElementById('content2'))
//         .then(function (blob) {
//             var pdf = new jsPDF('l', 'pt', [$('#content2').width(), $('#content2').height()]);

//             pdf.addImage(blob, 'PNG', 0, 0, $('#content2').width(), $('#content2').height());
//             pdf.save("test.pdf");

//             that.options.api.optionsChanged();
//         });
// });


$(document).ready(function() {
    $("#downloadPDF").on("click", function() {
       
        const element = document.getElementById('content2');
        const bookingId = this.getAttribute('data-booking-id');
        // Set up options for html2pdf.js
        const options = {
            margin:       0.5,
            filename:     'Invoice-'+ bookingId+'.pdf',
            image:        { type: 'jpeg', quality: 0.98 },
            html2canvas:  { scale: 2 },
            jsPDF:        { unit: 'in', format: 'a4', orientation: 'portrait' }
        };

        // Generate and download the PDF
        html2pdf().set(options).from(element).save();
    });
});


var datatable85 = $('#example85').DataTable();
      $(".artistsearch").click(function(){
    
  
		
        var value = $('#searchpopular').val();
       
        datatable85.columns(2).search(value).draw();
    });
	
	$(".vendorsort").change(function(){
    
  
		
        var value = $(this).val();
      
		  datatable85.columns(1).search(value).draw();
		   
	  
       
    });
	
	
	$(".enabledpopular").click(function(){
			id=$(this).attr('data-id');
			
			swal({
      title: "Are you sure?",
      text: "",
      icon: "warning",
      buttons: [
        'No, cancel it!',
        'Yes, I am sure!'
      ],
      dangerMode: true,
    }).then(function(isConfirm) {
      if (isConfirm) {
			  $.ajax({
                url: base_url+"/PopularArtistAjax/"+id,
                type:   'POST',
                 data: {_token: "{{csrf_token()}}"},
                success: function(msg){
                   
                    if($.trim(msg.Delete)=="success")
                    {
                    swal("Success!", "Popular Artist Updated Successfully", "success");
                    //$('.alert').show();	
                     //$("#"+id).remove();
                    }
                    else
                    {
                        if($.trim(msg)!='')
                        {
                      swal("Error!",msg , "error");  
                        }
                       
                    }
                },
                error: function() {
                    alert("Bad submit");
                }
            });
			 } else {
        swal("Cancelled", "", "error");
      }
    })
			});
    </script>



	
</body>

</html>