@extends('admin.layout.master')

@section('content')
@if(session('DashBoardView')==1)
<style>
	.export_excel{
		float:right !important;
		margin-right:15px  !important;
	}
	.export_pdf{
		float:right !important;
		margin-right:15px  !important;
		margin-bottom:20px !important;
	}
</style>
<div class="content-body default-height">
            <!-- row -->
			<div class="container-fluid">
				<div class="row mb-5">

					<h3 class="title mb-5"> Daily Statistics</h3>

					<div class="col-xl-12 col-xxl-12">

					
						<div class="row">
							<div class="col-sm-3">
							    <a href="{{ route('customer-list') }}">
								<div class="card avtivity-card bg-grad-2 overflow-hidden">
									<div class="card-body">
										<div class="media align-items-center">
											<span class="activity-icon bgl-success">
												<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M437.02 330.98c-27.883-27.882-61.071-48.523-97.281-61.018C378.521 243.251 404 198.548 404 148 404 66.393 337.607 0 256 0S108 66.393 108 148c0 50.548 25.479 95.251 64.262 121.962-36.21 12.495-69.398 33.136-97.281 61.018C26.629 379.333 0 443.62 0 512h40c0-119.103 96.897-216 216-216s216 96.897 216 216h40c0-68.38-26.629-132.667-74.98-181.02zM256 256c-59.551 0-108-48.448-108-108S196.449 40 256 40s108 48.448 108 108-48.449 108-108 108z" fill="#ffffff" opacity="1" data-original="#000000" class=""></path></g></svg>
											</span>
											<div class="media-body">
												<p class="fs-16 font-w600 mb-2">Active Users</p>
												<span class="title font-w600">{{$users}}</span>
											</div>
										</div>
										
									</div>
									<div class="effect bg-success"></div>
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
										<path fill="rgba(255,255,255,0.3)" fill-opacity="1" d="M0,128L34.3,112C68.6,96,137,64,206,96C274.3,128,343,224,411,250.7C480,277,549,235,617,213.3C685.7,192,754,192,823,181.3C891.4,171,960,149,1029,117.3C1097.1,85,1166,43,1234,58.7C1302.9,75,1371,149,1406,186.7L1440,224L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z"></path>
									</svg>
								</div>
								</a>
							</div>
							<div class="col-sm-3">
							    <a href="{{ route('booking-list') }}">
								<div class="card avtivity-card bg-grad-3 overflow-hidden">
									<div class="card-body">
										<div class="media align-items-center">
											<span class="activity-icon bgl-success">
												<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" x="0" y="0" viewBox="0 0 64 64" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><g  fill="#ffffff" fill-rule="evenodd" clip-rule="evenodd"><path d="M7 2a2 2 0 0 0-1.93 1.474l-3 11A2 2 0 0 0 2 15a8.977 8.977 0 0 0 3 6.708V52c0 5.523 4.477 10 10 10h17a2 2 0 1 0 0-4H15a6 6 0 0 1-6-6V23.777A9.033 9.033 0 0 0 11 24a8.983 8.983 0 0 0 7-3.343C19.65 22.697 22.173 24 25 24s5.35-1.304 7-3.343C33.65 22.697 36.173 24 39 24s5.35-1.304 7-3.343A8.983 8.983 0 0 0 53 24c.687 0 1.357-.077 2-.223V33.5a2 2 0 1 0 4 0V21.708A8.978 8.978 0 0 0 62 15c0-.178-.024-.355-.07-.526l-3-11A2 2 0 0 0 57 2zm18 18a5 5 0 0 1-5-5 2 2 0 1 0-4 0 5 5 0 0 1-9.994.246L8.528 6h46.944l2.522 9.246A5 5 0 0 1 48 15a2 2 0 1 0-4 0 5 5 0 0 1-10 0 2 2 0 1 0-4 0 5 5 0 0 1-5 5z"  fill="#ffffff" opacity="1" data-original="#000000" class=""></path><path d="M57 43a7.971 7.971 0 0 1-2.106 5.41A13 13 0 0 1 62 60a2 2 0 1 1-4 0 9 9 0 1 0-18 0 2 2 0 1 1-4 0 13 13 0 0 1 7.106-11.59A8 8 0 1 1 57 43zm-12 0a4 4 0 1 1 8 0 4 4 0 0 1-8 0z"  fill="#ffffff" opacity="1" data-original="#000000" class=""></path></g></g></svg>
											</span>
											<div class="media-body">
												<p class="fs-16 font-w600 mb-2">Total Orders</p>
												<span class="title font-w600">{{$orders}}</span>
											</div>
										</div>
										
									</div>
									<div class="effect bg-success"></div>
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
										<path fill="rgba(255,255,255,0.3)" fill-opacity="1" d="M0,128L34.3,112C68.6,96,137,64,206,96C274.3,128,343,224,411,250.7C480,277,549,235,617,213.3C685.7,192,754,192,823,181.3C891.4,171,960,149,1029,117.3C1097.1,85,1166,43,1234,58.7C1302.9,75,1371,149,1406,186.7L1440,224L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z"></path>
									</svg>
								</div>
								</a>
							</div>


							<div class="col-sm-3">
							     <a href="{{ route('booking-listcancelled') }}">
								<div class="card avtivity-card bg-grad-1 overflow-hidden">
									<div class="card-body">
										<div class="media align-items-center">
											<span class="activity-icon bgl-success">
												<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink"  width="40" height="40"  x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M230.022 369.583h-97.517c-8.273 0-14.981 6.707-14.981 14.981s6.707 14.981 14.981 14.981h97.517c8.274 0 14.981-6.707 14.981-14.981-.001-8.274-6.708-14.981-14.981-14.981z" fill="#ffffff"  opacity="1" data-original="#000000" class=""></path><path d="M391.156.33c-77.863 0-135.688 73.04-117.568 148.808H42.576C19.095 149.138 0 168.243 0 191.713v277.382c0 23.47 19.095 42.575 42.575 42.575h277.372c23.48 0 42.585-19.105 42.585-42.575V238.583a120.246 120.246 0 0 0 28.623 3.436c66.634 0 120.844-54.21 120.844-120.844C512 54.54 457.79.33 391.156.33zM137.153 179.099h88.216v77.4l-29.083-17.827c-9.268-5.663-20.783-5.663-30.071.01l-29.063 17.817v-77.4zm195.418 289.996c0 6.951-5.663 12.614-12.624 12.614H42.575c-6.961 0-12.614-5.663-12.614-12.614V191.713c0-6.951 5.653-12.614 12.614-12.614h64.617v79.517c0 22.556 24.809 36.221 43.814 24.528l30.261-18.546 30.241 18.546c19.18 11.772 43.824-2.11 43.824-24.528v-79.517h29.802a121.554 121.554 0 0 0 47.439 47.739v242.257zm58.585-257.038c-27.623 0-53.024-12.486-69.91-32.888h-.01c-48.957-58.95-6.919-148.878 69.92-148.878 50.115 0 90.883 40.767 90.883 90.883 0 50.115-40.768 90.883-90.883 90.883z" fill="#ffffff"  opacity="1" data-original="#000000" class=""></path><path d="m412.346 121.176 20.246-20.246c5.852-5.853 5.852-15.34 0-21.193-5.852-5.843-15.34-5.843-21.183 0l-20.246 20.256-20.252-20.253c-5.85-5.851-15.335-5.851-21.186 0-5.85 5.85-5.85 15.336 0 21.186l20.248 20.248-20.244 20.244c-4.714 4.714-5.633 11.795-2.746 17.427 4.664 9.063 16.766 10.928 23.929 3.765l20.254-20.244 20.241 20.241A14.929 14.929 0 0 0 422 166.995c13.225 0 20.058-16.109 10.593-25.573z" fill="#ffffff"  opacity="1" data-original="#000000" class=""></path></g></svg>
											</span>
											<div class="media-body">
												<p class="fs-16 font-w600 mb-2">Cancelled Orders</p>
												<span class="title font-w600">{{$cancelledorders}}</span>
											</div>
										</div>
										
									</div>
									<div class="effect bg-success"></div>
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
										<path fill="rgba(255,255,255,0.3)" fill-opacity="1" d="M0,128L34.3,112C68.6,96,137,64,206,96C274.3,128,343,224,411,250.7C480,277,549,235,617,213.3C685.7,192,754,192,823,181.3C891.4,171,960,149,1029,117.3C1097.1,85,1166,43,1234,58.7C1302.9,75,1371,149,1406,186.7L1440,224L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z"></path>
									</svg>
								</div>
								</a>
							</div>

							<div class="col-sm-3">
							     <a href="{{ route('booking-list') }}">
								<div class="card avtivity-card bg-grad-4 overflow-hidden">
									<div class="card-body">
										<div class="media align-items-center">
											<span class="activity-icon bgl-success">
											<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" x="0" y="0" viewBox="0 0 682.667 682.667" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><defs><clipPath id="a" clipPathUnits="userSpaceOnUse"><path d="M0 512h512V0H0Z" fill="#ffffff" opacity="1" data-original="#ffffff"></path></clipPath></defs><g clip-path="asset(#a)" transform="matrix(1.33333 0 0 -1.33333 0 682.667)"><path d="m0 0 107.983 26.073c32.948 7.327 41.506-32.141 14.801-41.793C104.428-22.138 21.84-57.406-35.019-78.724c-40.897-15.335-53.265-17.384-96.335-15.33l-65.476 3.122-11.8-18.754c-6.469-10.279-9.364-12.215-23.801-12.215h-104.464c-13.199-.001-20.872 14.872-13.838 27.524l29.793 52.615c59.495 109.16 162.302 55.651 212.799 60.317 10.805.998 76.339 8.46 87.147 9.458C-9.718 29.055.337 20.364 1.348 8.699 2.358-2.966-6.877-9.883-17.32-14.405c-27.385-11.856-57.174-22.197-82.162-28.064" style="stroke-width:20;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:2.613;stroke-dasharray:none;stroke-opacity:1" transform="translate(363.397 131.901)" fill="none" stroke="#ffffff" stroke-width="20" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="2.613" stroke-dasharray="none" stroke-opacity="" data-original="#000000"></path><path d="M0 0c-1.391 2.546-2.085 5.436-1.818 8.488l19.279 220.408c.881 10.067 8.675 18.368 19.278 18.368h231.342c10.603 0 18.398-8.302 19.277-18.368l19.28-220.408c.881-10.067-8.676-18.366-19.278-18.366h-24.453" style="stroke-width:20;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:2.613;stroke-dasharray:none;stroke-opacity:1" transform="translate(103.589 151.88)" fill="none" stroke="#ffffff" stroke-width="20" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="2.613" stroke-dasharray="none" stroke-opacity="" data-original="#000000"></path><path d="M0 0v76.655c0 31.822 26.035 57.857 57.857 57.857 31.821 0 57.856-26.041 57.856-57.857V0" style="stroke-width:20;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:2.613;stroke-dasharray:none;stroke-opacity:1" transform="translate(198.142 367.49)" fill="none" stroke="#ffffff" stroke-width="20" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="2.613" stroke-dasharray="none" stroke-opacity="" data-original="#000000"></path><path d="m0 0 20.068-38.712 43.02-7.124-30.616-31.049L38.99-120 0-100.477-38.99-120l6.518 43.115-30.616 31.049 43.02 7.124z" style="stroke-width:20;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22.926;stroke-dasharray:none;stroke-opacity:1" transform="translate(255.999 330.572)" fill="none" stroke="#ffffff" stroke-width="20" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="22.926" stroke-dasharray="none" stroke-opacity="" data-original="#000000"></path></g></g></svg>
											</span>
											<div class="media-body">
												<p class="fs-16 font-w600 mb-2">Total Sales</p>
												<span class="title font-w600">@if($totalamount[0]->total=='') 0 KWD @else  {{($totalamount[0]->total)}} KWD @endif</span>
											</div>
										</div>
										
									</div>
									<div class="effect bg-success"></div>

									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
										<path fill="rgba(255,255,255,0.3)" fill-opacity="1" d="M0,128L34.3,112C68.6,96,137,64,206,96C274.3,128,343,224,411,250.7C480,277,549,235,617,213.3C685.7,192,754,192,823,181.3C891.4,171,960,149,1029,117.3C1097.1,85,1166,43,1234,58.7C1302.9,75,1371,149,1406,186.7L1440,224L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z"></path>
									</svg>
								</div>
								</a>
							</div>
							
							
						</div>
					</div>

					<div class="col-xl-12 col-xxl-12">
						<div class="col-12">
							<div class="card">
								<div class="card-header">
									<h4 class="card-title">Scheduled Booking Details</h4>

									<a href="{{route('booking-list')}}">
										<button class="btn btn-primary">View all</button>
									</a>
								</div>
								<div class="card-body">
									<div class="table-responsive">
										<table id="example2" class="display">
											<thead>
												 <tr>
                                                <th>S.No.</th>
                                                <th>Date</th>
												<th>Booking ID</th>
                                                <th>User</th>
                                                <th>Vendor</th>
												<th>Area</th>
												
                                                <th>Total Amount</th>
												
                                                <th>Status</th>
                                               
                                               
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										@foreach($bookingpending as $key => $book)
                                            <tr>
                                                <td>{{$key+1}}</td>
                                                <td data-order="{{strtotime($book->bookdate)}}">{{$book->bookdate}}</td>
                                                <td>{{$book->Bookid}}</td>
                                                <td>{{$book->name}}<br> {{@$book->email}}<br> {{@$book->user_phone}}</td>
                                                <td>{{$book->vendor_name}}</td>
                                                <td>{{$book->Area_Title}}</td>
                                                
                                              
                                             
                                                    <td data-order="{{$book->total_price}}">KWD {{$book->total_price}}</td>
													
													
                                                    <td><span class="badge light badge-warning mb-2">@if($book->status==1) Scheduled @endif @if($book->status==2) Completed @endif @if($book->status==3) Cancelled @endif @if($book->status==6) Failed @endif</span>
														
													</td>
                                                <td>
													<div class="d-flex">
                                                      
														<a href="{{route('view-bookdetails',$book->id)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="View"><i class="fa fa-eye"></i></a>
													
													</div>												
												</td>												
                                            </tr>
											 @endforeach  
												

											
											</tfoot>
										</table>
									</div>
								</div>
							</div>
						</div>

						</div>
					
					
					
				</div>




				<div class="row">
				<?php /*
					<div class="row">
						<div class="col-xl-7 col-xxl-7">
					<h3 class="title mb-5"> Overall Statistics</h3>
					</div>

					<div class="col-xl-3 col-xxl-3 col-6">

						<select class="form-control default-select" id="sel1">
							<option value="" selected="selected">Filter by date</option>
							<option value="1">Today</option>
							<option value="2"> Yesterday </option>
							<option value="2"> Last 30 Days </option>
							<option value="2"> This Month </option>
							<option value="2"> Last Month </option>
							<option value="2"> Custom Range </option>
						</select>
					</div>

					<div class="col-xl-1 col-xxl-1 col-3">
						<a href="#" class="btn btn-primary btn-rounded"> Filter</a>
					
						</div>

						<div class="col-xl-1 col-xxl-1 col-3">
							<a href="#" class="btn btn-primary btn-rounded"> Reset</a>
							
							</div>
				</div>
				*/?>
					<div class="col-xl-12 col-xxl-12">

					
						<div class="row">
							<div class="col-sm-3">
								<div class="card avtivity-card bg-grad-8 overflow-hidden">
									<div class="card-body">
										<div class="media align-items-center">
											<span class="activity-icon bgl-success">
												<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M437.02 330.98c-27.883-27.882-61.071-48.523-97.281-61.018C378.521 243.251 404 198.548 404 148 404 66.393 337.607 0 256 0S108 66.393 108 148c0 50.548 25.479 95.251 64.262 121.962-36.21 12.495-69.398 33.136-97.281 61.018C26.629 379.333 0 443.62 0 512h40c0-119.103 96.897-216 216-216s216 96.897 216 216h40c0-68.38-26.629-132.667-74.98-181.02zM256 256c-59.551 0-108-48.448-108-108S196.449 40 256 40s108 48.448 108 108-48.449 108-108 108z" fill="#ffffff" opacity="1" data-original="#000000" class=""></path></g></svg>
											</span>
											<div class="media-body">
												<p class="fs-16 font-w600 mb-2">Registered users</p>
												<span class="title font-w600">{{$totalusers}}</span>
											</div>
										</div>
										
									</div>
									<div class="effect bg-success"></div>

									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
										<path fill="rgba(255,255,255,0.3)" fill-opacity="1" d="M0,128L34.3,112C68.6,96,137,64,206,96C274.3,128,343,224,411,250.7C480,277,549,235,617,213.3C685.7,192,754,192,823,181.3C891.4,171,960,149,1029,117.3C1097.1,85,1166,43,1234,58.7C1302.9,75,1371,149,1406,186.7L1440,224L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z"></path>
									</svg>
								</div>
							</div>
							<div class="col-sm-3">
								<div class="card avtivity-card bg-grad-11 overflow-hidden">
									<div class="card-body">
										<div class="media align-items-center">
											<span class="activity-icon bgl-success">
												<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" x="0" y="0" viewBox="0 0 64 64" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><g fill="#ffffff" fill-rule="evenodd" clip-rule="evenodd"><path d="M7 2a2 2 0 0 0-1.93 1.474l-3 11A2 2 0 0 0 2 15a8.977 8.977 0 0 0 3 6.708V52c0 5.523 4.477 10 10 10h17a2 2 0 1 0 0-4H15a6 6 0 0 1-6-6V23.777A9.033 9.033 0 0 0 11 24a8.983 8.983 0 0 0 7-3.343C19.65 22.697 22.173 24 25 24s5.35-1.304 7-3.343C33.65 22.697 36.173 24 39 24s5.35-1.304 7-3.343A8.983 8.983 0 0 0 53 24c.687 0 1.357-.077 2-.223V33.5a2 2 0 1 0 4 0V21.708A8.978 8.978 0 0 0 62 15c0-.178-.024-.355-.07-.526l-3-11A2 2 0 0 0 57 2zm18 18a5 5 0 0 1-5-5 2 2 0 1 0-4 0 5 5 0 0 1-9.994.246L8.528 6h46.944l2.522 9.246A5 5 0 0 1 48 15a2 2 0 1 0-4 0 5 5 0 0 1-10 0 2 2 0 1 0-4 0 5 5 0 0 1-5 5z" fill="#ffffff" opacity="1" data-original="#000000" class=""></path><path d="M57 43a7.971 7.971 0 0 1-2.106 5.41A13 13 0 0 1 62 60a2 2 0 1 1-4 0 9 9 0 1 0-18 0 2 2 0 1 1-4 0 13 13 0 0 1 7.106-11.59A8 8 0 1 1 57 43zm-12 0a4 4 0 1 1 8 0 4 4 0 0 1-8 0z" fill="#ffffff" opacity="1" data-original="#000000" class=""></path></g></g></svg>
											</span>
											<div class="media-body">
												<p class="fs-16 font-w600 mb-2">Total Vendors</p>
												<span class="title font-w600">{{$totalvendors}}</span>
											</div>
										</div>
										
									</div>
									<div class="effect bg-success"></div>

									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
										<path fill="rgba(255,255,255,0.3)" fill-opacity="1" d="M0,128L34.3,112C68.6,96,137,64,206,96C274.3,128,343,224,411,250.7C480,277,549,235,617,213.3C685.7,192,754,192,823,181.3C891.4,171,960,149,1029,117.3C1097.1,85,1166,43,1234,58.7C1302.9,75,1371,149,1406,186.7L1440,224L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z"></path>
									</svg>
								</div>
							</div>


							<div class="col-sm-3">
								<div class="card avtivity-card bg-grad-12 overflow-hidden">
									<div class="card-body">
										<div class="media align-items-center">
											<span class="activity-icon bgl-success">
											<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" x="0" y="0" viewBox="0 0 128 128" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><path d="M99.47 25.874V23.66a6.427 6.427 0 0 0-6.41-6.41 6.417 6.417 0 0 0-6.41 6.41v2.11h-6.56v-2.11a6.41 6.41 0 0 0-6.4-6.41 6.418 6.418 0 0 0-6.41 6.41v2.11h-6.56v-2.11a6.405 6.405 0 1 0-12.81 0v2.11h-6.56v-2.11a6.427 6.427 0 0 0-6.41-6.41 6.418 6.418 0 0 0-6.41 6.41v2.214A12.853 12.853 0 0 0 17.25 38.61v59.3a12.855 12.855 0 0 0 12.84 12.84h67.82a12.855 12.855 0 0 0 12.84-12.84v-59.3a12.853 12.853 0 0 0-11.28-12.736zm-9.32-2.214a2.914 2.914 0 0 1 2.91-2.91 2.928 2.928 0 0 1 2.91 2.91v7.72a2.91 2.91 0 0 1-5.82 0zm-19.37 0a2.913 2.913 0 0 1 2.91-2.91 2.909 2.909 0 0 1 2.9 2.91v7.72a2.905 2.905 0 1 1-5.81 0zm-19.37 0a2.905 2.905 0 1 1 5.81 0v7.72a2.905 2.905 0 1 1-5.81 0zm-19.38 0a2.913 2.913 0 0 1 2.91-2.91 2.928 2.928 0 0 1 2.91 2.91v7.72a2.91 2.91 0 0 1-5.82 0zm-3.5 5.751v1.969a6.41 6.41 0 0 0 12.82 0v-2.11h6.56v2.11a6.405 6.405 0 1 0 12.81 0v-2.11h6.56v2.11a6.405 6.405 0 1 0 12.81 0v-2.11h6.56v2.11a6.41 6.41 0 0 0 12.82 0v-1.969a9.348 9.348 0 0 1 7.78 9.2v5.267h-86.5V38.61a9.348 9.348 0 0 1 7.78-9.199zm69.38 77.839H30.09a9.351 9.351 0 0 1-9.34-9.34V47.38h86.5v50.53a9.351 9.351 0 0 1-9.34 9.34z" fill="#ffff" opacity="1" data-original="#000000" class=""></path><path d="M41.684 56.135h-7.066a4.646 4.646 0 0 0-4.64 4.64v7.066a4.646 4.646 0 0 0 4.64 4.64h7.066a4.646 4.646 0 0 0 4.64-4.64v-7.066a4.646 4.646 0 0 0-4.64-4.64zm1.14 11.706a1.141 1.141 0 0 1-1.14 1.14h-7.066a1.141 1.141 0 0 1-1.14-1.14v-7.066a1.141 1.141 0 0 1 1.14-1.14h7.066a1.141 1.141 0 0 1 1.14 1.14zM67.533 56.135h-7.066a4.646 4.646 0 0 0-4.64 4.64v7.066a4.646 4.646 0 0 0 4.64 4.64h7.066a4.646 4.646 0 0 0 4.64-4.64v-7.066a4.646 4.646 0 0 0-4.64-4.64zm1.14 11.706a1.141 1.141 0 0 1-1.14 1.14h-7.066a1.141 1.141 0 0 1-1.14-1.14v-7.066a1.141 1.141 0 0 1 1.14-1.14h7.066a1.141 1.141 0 0 1 1.14 1.14zM95.65 58.529l-7.85 7.846-3.756-3.756a1.75 1.75 0 0 0-2.474 2.475l4.993 4.993a1.749 1.749 0 0 0 2.475 0L98.125 61a1.75 1.75 0 0 0-2.475-2.475zM41.684 80.241h-7.066a4.646 4.646 0 0 0-4.64 4.64v7.066a4.646 4.646 0 0 0 4.64 4.64h7.066a4.646 4.646 0 0 0 4.64-4.64v-7.066a4.646 4.646 0 0 0-4.64-4.64zm1.14 11.706a1.141 1.141 0 0 1-1.14 1.14h-7.066a1.141 1.141 0 0 1-1.14-1.14v-7.066a1.141 1.141 0 0 1 1.14-1.14h7.066a1.141 1.141 0 0 1 1.14 1.14zM67.533 80.241h-7.066a4.646 4.646 0 0 0-4.64 4.64v7.066a4.646 4.646 0 0 0 4.64 4.64h7.066a4.646 4.646 0 0 0 4.64-4.64v-7.066a4.646 4.646 0 0 0-4.64-4.64zm1.14 11.706a1.141 1.141 0 0 1-1.14 1.14h-7.066a1.141 1.141 0 0 1-1.14-1.14v-7.066a1.141 1.141 0 0 1 1.14-1.14h7.066a1.141 1.141 0 0 1 1.14 1.14zM93.382 80.241h-7.066a4.646 4.646 0 0 0-4.64 4.64v7.066a4.646 4.646 0 0 0 4.64 4.64h7.066a4.646 4.646 0 0 0 4.64-4.64v-7.066a4.646 4.646 0 0 0-4.64-4.64zm1.14 11.706a1.141 1.141 0 0 1-1.14 1.14h-7.066a1.141 1.141 0 0 1-1.14-1.14v-7.066a1.141 1.141 0 0 1 1.14-1.14h7.066a1.141 1.141 0 0 1 1.14 1.14z" fill="#ffff" opacity="1" data-original="#000000" class=""></path></g></svg>
											</span>
											<div class="media-body">
												<p class="fs-16 font-w600 mb-2">Total
													Booking</p>
												<span class="title font-w600">{{$totalbooking}}</span>
											</div>
										</div>
										
									</div>
									<div class="effect bg-success"></div>

									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
										<path fill="rgba(255,255,255,0.3)" fill-opacity="1" d="M0,128L34.3,112C68.6,96,137,64,206,96C274.3,128,343,224,411,250.7C480,277,549,235,617,213.3C685.7,192,754,192,823,181.3C891.4,171,960,149,1029,117.3C1097.1,85,1166,43,1234,58.7C1302.9,75,1371,149,1406,186.7L1440,224L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z"></path>
									</svg>
								</div>
							</div>


							


							<div class="col-sm-3">
								<div class="card avtivity-card bg-grad-13 overflow-hidden">
									<div class="card-body">
										<div class="media align-items-center">
											<span class="activity-icon bgl-success">
												<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="40" height="40" x="0" y="0" viewBox="0 0 682.667 682.667" style="enable-background:new 0 0 512 512" xml:space="preserve" class=""><g><defs><clipPath id="a" clipPathUnits="userSpaceOnUse"><path d="M0 512h512V0H0Z" fill="#ffffff" opacity="1" data-original="#ffffff"></path></clipPath></defs><g clip-path="asset(#a)" transform="matrix(1.33333 0 0 -1.33333 0 682.667)"><path d="m0 0 107.983 26.073c32.948 7.327 41.506-32.141 14.801-41.793C104.428-22.138 21.84-57.406-35.019-78.724c-40.897-15.335-53.265-17.384-96.335-15.33l-65.476 3.122-11.8-18.754c-6.469-10.279-9.364-12.215-23.801-12.215h-104.464c-13.199-.001-20.872 14.872-13.838 27.524l29.793 52.615c59.495 109.16 162.302 55.651 212.799 60.317 10.805.998 76.339 8.46 87.147 9.458C-9.718 29.055.337 20.364 1.348 8.699 2.358-2.966-6.877-9.883-17.32-14.405c-27.385-11.856-57.174-22.197-82.162-28.064" style="stroke-width:20;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:2.613;stroke-dasharray:none;stroke-opacity:1" transform="translate(363.397 131.901)" fill="none" stroke="#ffffff" stroke-width="20" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="2.613" stroke-dasharray="none" stroke-opacity="" data-original="#000000"></path><path d="M0 0c-1.391 2.546-2.085 5.436-1.818 8.488l19.279 220.408c.881 10.067 8.675 18.368 19.278 18.368h231.342c10.603 0 18.398-8.302 19.277-18.368l19.28-220.408c.881-10.067-8.676-18.366-19.278-18.366h-24.453" style="stroke-width:20;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:2.613;stroke-dasharray:none;stroke-opacity:1" transform="translate(103.589 151.88)" fill="none" stroke="#ffffff" stroke-width="20" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="2.613" stroke-dasharray="none" stroke-opacity="" data-original="#000000"></path><path d="M0 0v76.655c0 31.822 26.035 57.857 57.857 57.857 31.821 0 57.856-26.041 57.856-57.857V0" style="stroke-width:20;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:2.613;stroke-dasharray:none;stroke-opacity:1" transform="translate(198.142 367.49)" fill="none" stroke="#ffffff" stroke-width="20" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="2.613" stroke-dasharray="none" stroke-opacity="" data-original="#000000"></path><path d="m0 0 20.068-38.712 43.02-7.124-30.616-31.049L38.99-120 0-100.477-38.99-120l6.518 43.115-30.616 31.049 43.02 7.124z" style="stroke-width:20;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:22.926;stroke-dasharray:none;stroke-opacity:1" transform="translate(255.999 330.572)" fill="none" stroke="#ffffff" stroke-width="20" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="22.926" stroke-dasharray="none" stroke-opacity="" data-original="#000000"></path></g></g></svg>
											</span>
											<div class="media-body">
												<p class="fs-16 font-w600 mb-2">Total
													Sales Amount</p>
												<span class="title font-w600">{{$totalordersum[0]->sums}} KWD</span>
											</div>
										</div>
										
									</div>
									<div class="effect bg-success"></div>

									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
										<path fill="rgba(255,255,255,0.3)" fill-opacity="1" d="M0,128L34.3,112C68.6,96,137,64,206,96C274.3,128,343,224,411,250.7C480,277,549,235,617,213.3C685.7,192,754,192,823,181.3C891.4,171,960,149,1029,117.3C1097.1,85,1166,43,1234,58.7C1302.9,75,1371,149,1406,186.7L1440,224L1440,320L1405.7,320C1371.4,320,1303,320,1234,320C1165.7,320,1097,320,1029,320C960,320,891,320,823,320C754.3,320,686,320,617,320C548.6,320,480,320,411,320C342.9,320,274,320,206,320C137.1,320,69,320,34,320L0,320Z"></path>
									</svg>
								</div>
							</div>


							
							
						</div>
					</div>

					<div class="col-xl-12 col-xxl-12">


						<div class="row">
						 <div class="col-xl-12 col-xxl-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Sales Analytics</h4>
                                    </div>
                                    <div class="card-body">
                                        <div id="chartBarAdmin"></div>
                                    </div>
                                </div>
                            </div>
							</div>

							<div class="col-xl-12 col-xxl-12">

 <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Newly Added Vendors</h4>

								<a href="{{route('list-vendors')}}">
									<button class="btn btn-primary">View all</button>
								</a>
                            </div>
                            <div class="card-body p-0">
                                <div id="DZ_W_TimeLine" class="widget-timeline  my-4 px-4 dz-scroll height370">
									<div class="table-responsive">
										<table class="table table-responsive-md">
											<thead>
												<tr>
													<th style="width:80px;">#</th>
													<th>Name</th>
													<th>Mobile No</th>
													<th>Area</th>
													<th>Action</th>
												</tr>
											</thead>
											<tbody>
												@foreach($vendors as $key => $vendor)
												<tr>
													<td><strong class="text-black">{{$key+1}}</strong></td>
													<td>{{$vendor->vendor_name}}</td>
													<td>{{$vendor->vendor_phone}}</td>
													<td>{{$vendor->Area_Title}}</td>
													
													<td>
														<div class="dropdown">
															<button type="button" class="btn btn-success light sharp" data-bs-toggle="dropdown">
																<svg width="20px" height="20px" viewBox="0 0 24 24" version="1.1"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><rect x="0" y="0" width="24" height="24"/><circle fill="#000000" cx="5" cy="12" r="2"/><circle fill="#000000" cx="12" cy="12" r="2"/><circle fill="#000000" cx="19" cy="12" r="2"/></g></svg>
															</button>
															<div class="dropdown-menu">
																<a class="dropdown-item" href="{{route('editvendor',$vendor->vendor_id)}}">Edit</a>
																
															</div>
														</div>
													</td>
												</tr>
												@endforeach

											</tbody>
										</table>
									</div>
                                </div>
                            </div>
                        </div>
								</div>




						</div>

						</div>
					
					
					
				</div>

            </div>
        </div>
		@endif
		<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

        <script>
            function renderChartAdmin(elementId, data, categories) {
                console.log("Rendering chart with data:", data); // Debugging log for data
                console.log("Rendering chart with categories:", categories); // Debugging log for categories

                var optionsArea = {
                    series: [{
                        name: "Sales Amount",
                        data: data
                    }],
                    chart: {
                        height: 400,
                        type: 'area',
                        toolbar: { show: false },
                        zoom: { enabled: false }
                    },
                    dataLabels: { enabled: false },
                    stroke: {
                        width: [4],
                        colors: ['#007BFF'],
                        curve: 'smooth'
                    },
                    xaxis: {
                        categories: categories,
                        labels: {
                            style: {
                                colors: '#787878',
                                fontSize: '14px',
                                fontFamily: 'Poppins',
                                fontWeight: 100,
                            },
                        },
                    },
                    yaxis: {
                        labels: {
                            offsetX: -16,
                            style: {
                                colors: '#787878',
                                fontSize: '14px',
                                fontFamily: 'Poppins',
                                fontWeight: 100,
                            },
                        },
                    },
                    fill: {
                        colors: ['rgba(0, 143, 150, 1)'],
                        type: 'solid',
                        opacity: 0.7
                    },
                    colors: ['#FF3282'],
                    grid: {
                        borderColor: '#f1f1f1',
                        xaxis: { lines: { show: true } }
                    },
                    responsive: [{
                        breakpoint: 575,
                        options: { chart: { height: 250 } }
                    }]
                };

                var chartArea = new ApexCharts(document.querySelector(`#${elementId}`), optionsArea);
                chartArea.render();
            }

            @if(isset($ordertotals))
                document.addEventListener("DOMContentLoaded", function() {
                    // Convert the PHP string into an array in JavaScript
                    var ordertotals = {!! json_encode($ordertotals) !!}.split(',').map(Number); // Converts to an array of numbers
                    var categories = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

                    // Debugging: Check if ordertotals array is correctly formed
                    console.log("ordertotals array:", ordertotals);

                    // Call the render function only if ordertotals has values
                    if (Array.isArray(ordertotals) && ordertotals.length === 12) {
                        renderChartAdmin('chartBarAdmin', ordertotals, categories);
                    } else {
                        console.error("Error: ordertotals data is invalid or incomplete.");
                    }
                });
            @endif
        </script>
		@endsection
		



 


