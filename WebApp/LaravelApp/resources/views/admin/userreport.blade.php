-@extends('admin.layout.master')

@section('content')
@if(session('ViewUserReport')==1)
<style>
	.export_excel{
		float:right !important;
		margin-right:15px  !important;
	}
	.export_pdf{
		float:right !important;
		margin-right:15px  !important;
		margin-bottom:20px !important;
	}
</style>
<div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-12">
                        <div class="card">
                            <div class="card-header justify-content-between">
                                <h4 class="card-title">User Report</h4>

                                
                            </div>



                                 

                            <div class="card-body">
                                <div class="row mb-5">
                                   <div class="col-xl-3 col-lg-3 col-sm-6 col-12">
                                        <select  id="vendor-user-select" class="form-control default-select ">
										{{!!$vendorhtml!!}}
                                            </select>
                                    </div>
                                    <div class="col-xl-3 col-lg-3 col-sm-6 col-12 ">
                                        <select class="form-control default-select" id="sort4" >
                                                <option value="" selected="selected">Sorting</option>
                                                <option value="1">Sort by Latest</option>
                                                <option value="2">Sort by A-Z</option>
                                            </select>
                                    </div>
                                    <div class="col-xl-6 col-lg-6 col-12">


                                        <div class="input-group">
                                            <input type="text" id="searchuser"  class="form-control" placeholder="search users..." aria-label="Username" aria-describedby="basic-addon1">
                                            <span class="input-group-text" id="basic-addon1" ><a class="vendorsearch4" href="javascript:void(0);">Search</a></span>
                                      </div>
                                    </div>
                                </div>

                               
                        

                                <div class="table-responsive">
                                    <table id="example2" class="display min-w850">
                                        <thead>
                                            <tr>
                                                <th>S.No.</th>
                                               
                                                <th>User name</th>
                                                <th>Email</th>
                                                <th>Phone</th>
                                                <th>Vendor name</th>
                                                <th>Total Service book</th>
                                                <th>Total Amount</th>
                                                <th>Status</th>
                                               
                                               
                                               
                                            </tr>
                                        </thead>
                                        <tbody>
										
										@foreach($users as $key => $user)
                                            <tr>
                                                <td>{{$key+1}}</td>
                                               
                                                <td><a href="{{route('customer-profile',$user->user_id)}}">{{$user->name}}</a></td>
                                                 <td><a href="{{route('customer-profile',$user->user_id)}}">{{$user->email}}</a></td>
                                                  <td><a href="{{route('customer-profile',$user->user_id)}}">{{$user->user_phone}}</a></td>
                                                <td></td>
                                                <td>{{$user->counter}}</td>
                                                <td>KWD {{$user->total}}</td>
                                                <td><span class="badge light badge-success">@if($user->block==0) Active @endif @if($user->block==1) Inactive @endif</span></td>
                                              
                                             
                                               
                                               											
                                            </tr>
										@endforeach 

                                         





                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		@endif
@endsection





	