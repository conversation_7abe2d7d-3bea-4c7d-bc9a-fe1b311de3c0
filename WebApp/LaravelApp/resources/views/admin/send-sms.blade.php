 @extends('admin.layout.master')

@section('content')
@if(session('SendMessages')==1)
     <div class="content-body default-height">
			<div class="container-fluid">
				
				<!-- Row -->
				<div class="row">
					<div class="col-xl-12">
					
						
						<div class="row">
							<div class="col-xl-12">
								
							
								<div class="filter cm-content-box box-primary">
									<div class="content-title">
										<div class="cpa">
											Messages
										</div>
										<div class="tools">
											<a href="javascript:void(0);" class="expand SlideToolHeader"><i class="fal fa-angle-down"></i></a>
										</div>
									</div>
									<div class="cm-content-body  form excerpt">
										<div class="card-body">
										<form method="post" action="{{route('send-sms-send')}}" enctype='multipart/form-data'>
  {{csrf_field()}}
<div class="row">
@if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif
											<div class="row">
												<div class="col-xl-6 col-sm-6">
													
													  <div class="mb-3">
														<label  class="form-label">Select User <span> *</span></label>
														<select  id="single-select" style="width:100%;" required name="users">
                                                            <option value="">Select</option>
															<option value="All">All</option>
															@foreach($users as $user)
															<option value="{{$user->id}}">{{$user->name}}</option>
                                                            @endforeach
                                                           
                                                        </select>
													  </div>
													
												</div>
                                                <div class="col-xl-6 col-sm-6">
													
													  <div class="mb-3">
														<label  class="form-label">Title <span> *</span></label>
														<input type="text" class="form-control" required name="subject" id="subject" placeholder="Title">
													  </div>
													
												</div>

                                                <div class="col-xl-12 col-sm-12">
												
													  <div class="mb-3">
														<label  class="form-label">Message <span> *</span></label>
                                                        <textarea  class="form-control" name="message" required rows="3" placeholder="Enter Message"></textarea>
													  </div>
													
												</div>

                                                <div class="filter cm-content-box box-primary style-1 mb-0 border-0">
                                                    <div class="content-title">
                                                        
                                                        <button type="submit" class="btn btn-primary my-2">Send</button>
                                                    </div>
                                                </div>
                                               
											</div>	
											</form>
										
										</div>
									</div>
								</div>
							
								
								
							
							</div>
							
						</div>
					</div>
				</div>
			</div>
		</div>
       @endif
@endsection


	


	