 @extends('admin.layout.master')

@section('content')
@if(session('CustomerView')==1)
    <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Customer Database</h4>
                            </div>
                            <div class="card-body">
								<div class="row mb-5">
                                 
                                       <div class="card-body">
                                <div class="row mb-5">
                                   
                                    <div class="col-xl-3 col-lg-3 col-sm-6 col-12 ">
                                        <select class="form-control default-select" id="sort7" >
                                                <option value="" selected="selected">Sorting</option>
                                                <option value="1">Sort by Latest</option>
                                                <option value="2">Sort by A-Z</option>
                                            </select>
                                    </div>
                                     <div class="col-xl-3 col-lg-3 col-sm-6 col-12">
                                        <select id="vendor-select" style="width:100%;">
                                            <option value="">Select Vendor</option>
                                               @foreach($vendors as $vendor)
                                               <option value="{{$vendor->vendor_name}}">{{ $vendor->vendor_name }}</option>
                                               @endforeach
											
                                            </select>
                                    </div>
									<div class="col-xl-3 col-lg-3 col-sm-6 col-12">
                                        <select id="usertype-select" style="width:100%;">
                                            <option value="">Select User Type</option>
                                               
                                               <option value="Play Store">Play Store</option>
                                               <option value="App Store">App Store</option>
											
                                            </select>
                                    </div>
                                    <div class="col-xl-6 col-lg-6 col-12 mt-5">


                                        <div class="input-group">
                                            <input type="text" id="searchuser"  class="form-control" placeholder="search users..." aria-label="Username" aria-describedby="basic-addon1">
                                            <span class="input-group-text" id="basic-addon1" ><a class="vendorsearch7" href="javascript:void(0);">Search</a></span>
                                      </div>
                                    </div>
                                </div>
<span id="filtered-count"></span>

                                <div class="table-responsive">
                                    <table id="example7" class="display min-w850">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th>Name</th>
                                                <th>Vendor</th>
                                                <th>Gender</th>
                                                <th>Country</th>
                                                <th>Mobile</th>
                                                <th>Email</th>
												<th>Type</th>
												<th>Enable/ Disable</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										<?php $k=1; ?>
										@foreach($users  as $key => $user)
                                            <tr>
                                                <td>@if($user->image!=null)<img class="rounded-circle" width="35" src="{{asset($user->image)}}" alt="">@endif</td>
                                                <td>{{$user->name}}</td>
                                                 <td>{{$user->vendor_name}}</td>
                                                <td>{{$user->Gender}}</td>
                                                <td>{{$user->Country}}</td>
                                                <td>{{$user->user_phone}}</td>
                                                <td><a href="javascript:void(0);">{{$user->email}}</a></td>
                                               <td>@if($user->google_id==1) Play Store @endif @if($user->apple_id!='') App Store @endif</td>
												<td>
                                                 
													<div class="material-switch justify-content-center">
														<input id="someSwitchOptionDefault{{$key+1}}" class="enableuser" data-id="{{$user->id}}" name="enable" value="{{$user->block}}" @if($user->block==0) checked @endif type="checkbox"/>
														<label for="someSwitchOptionDefault{{$key+1}}" class="label-default"></label>
													</div>

												</td>
                                                <td>
													<div class="d-flex">
                                                        <a href="{{route('customer-profile',$user->id)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="view"><i class="fa fa-eye"></i></a>
														
													
													</div>												
												</td>												
                                            </tr>
                                          <?php $k++; ?>
											@endforeach
                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
    @endif   
@endsection


	