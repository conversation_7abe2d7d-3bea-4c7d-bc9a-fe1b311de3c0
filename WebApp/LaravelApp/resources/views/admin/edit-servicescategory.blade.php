@extends('admin.layout.master')

@section('content')

   <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
				


                    <div class="col-xl-12">
								

                        <div class="filter cm-content-box box-primary">
                            <div class="content-title">
                                <div class="cpa">Edit Services Category			
                                </div>
                                <div class="tools">
                                    <a href="javascript:void(0);" class="expand SlideToolHeader"><i class="fal fa-angle-down"></i></a>
                                </div>
                            </div>
                            <div class="cm-content-body  form excerpt">
                                <div class="card-body">
								 @if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif
                                     <form method="post" action="{{route('editservicescategory-vendor-update',$services->Cat_ID)}}" enctype='multipart/form-data'>
                                    <div class="row">
{{csrf_field()}}
                                        
                           

                          
                          <div class="col-md-6 mb-2">
						  <label class="control-label form-label">Select Vendor <span> *</span></label>
						<select class="form-control default-select" id="single-select" style="width:100%;" required name="vendor_id" >
														<option value="">Select</option>
                                                           @foreach($vendor as $vend)
														   
      <option value="{{$vend->vendor_id}}" @if($vend->vendor_id==$services->vendor_id) selected @endif >{{$vend->vendor_name}}</option>

														 
															   
														    @endforeach
                                                           
                                                        </select>
														 </div>
                          

                              <div class="col-md-6 mb-2">
                                <label class="control-label form-label">Service Category Name (English)<span> *</span></label>
								
                                <input class="form-control form-white" placeholder="Service Name" type="text" name="Cat_Name" required value="{{$services->Cat_Name}}">
                            </div>
							<div class="col-md-6 mb-2">
                                <label class="control-label form-label">Service Category Name (Arabic)<span> *</span></label>
								
                                <input class="form-control form-white" placeholder="Service Name" type="text" name="Cat_Name_ar" required value="{{$services->Cat_Name_ar}}">
                           
														 </div>
                            

                            <div class="col-md-6 mb-2">
                                <label class="control-label form-label">Ordering <span> *</span></label>
                                <input class="form-control form-white" placeholder="Ordering" type="text" name="Ordering" value="{{$services->Ordering}}" required>
                            </div>
							
							
						
                                       
                                     

                                      

                                      

                                        
                                    

                                    </div>
                                    <div class="filter cm-content-box box-primary style-1 mb-0 border-0">
                                        <div class="content-title">
                                            
                                            <button type="submit" class="btn btn-primary my-2">Save</button>
                                        </div>
                                    </div>
									
									</form>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		
@endsection

