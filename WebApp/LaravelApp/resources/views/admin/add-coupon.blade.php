 @extends('admin.layout.master')

@section('content')
@if(session('AddPromotion')==1)
     <div class="content-body default-height">
			<div class="container-fluid">
				
				<!-- Row -->
				<div class="row">
					<div class="col-xl-12">
					
						
						<div class="row">
							<div class="col-xl-12">
								
							
								
							


							
								
								
								

								<div class="filter cm-content-box box-primary">
									<div class="content-title">
										<div class="cpa">Add Promotion						
										</div>
										<div class="tools">
											<a href="javascript:void(0);" class="expand SlideToolHeader"><i class="fal fa-angle-down"></i></a>
										</div>
									</div>
									<div class="cm-content-body  form excerpt">
										<div class="card-body">
											
											<div class="row">
<form method="post" action="{{route('save-coupon')}}" >
  {{csrf_field()}}
<div class="row">
@if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif
                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Title <span> *</span></label>
													<input name="title" required class="form-control" placeholder="Title" value="{!! old('title') !!}">
												</div>
												<div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Code <span> *</span></label>
													<input name="code" required class="form-control" placeholder="Code" value="{!! old('code') !!}">
												</div>

                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Applicable For <span> *</span></label>
													<select name="applicable" required class="form-control default-select promo-user-applicable" id="sel1">
                                                        <option value="All Users">All Users</option>
                                                        <option value="Specific Users">Specific Users</option>
                                                     
                                                       
                                                    </select>
												</div>

                                                <div class="col-xl-6 col-sm-12 mb-3 usersdiv" style="display:none;">
													<label class="form-label">Users <span> *</span></label>
													<select name="users"   class="form-control" id="single-select" style="width:100%;">
													<option value="">Select</option>
                                                       @foreach($users as $user)
													   <option value="{{$user->id}}">{{$user->name}}</option>
													   @endforeach
                                                     
                                                       
                                                    </select>
												</div>
												  <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Applicable For Vendors <span> *</span></label>
													<select name="applicable_vendor" required class="form-control promo-venors-applicable" id="sel1">
                                                        <option value="0">All Vendors</option>
                                                        <option value="1">Specific Vendors</option>
                                                     
                                                       
                                                    </select>
												</div>
												
												  <div class="col-xl-6 col-sm-12 mb-3 vendors" style="display:none;">
													<label class="form-label">Vendors <span> *</span></label>
													<select name="vendors[]"   multiple class="form-control multi-select" style="width:100%;">
													<option value="">Select</option>
                                                       @foreach($vendors as $vendor)
													   <option value="{{$vendor->vendor_id}}">{{$vendor->vendor_name}}</option>
													   @endforeach
                                                     
                                                       
                                                    </select>
												</div>
												
												<div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Start Date <span> *</span></label>
													<input name="startdate" required class="datepicker-default form-control" id="datepicker" value="{!! old('startdate') !!}">
												</div>

												<div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">End Date <span> *</span></label>
													<input name="enddate" required class="datepicker-default form-control" id="datepicker1" value="{!! old('enddate') !!}">
												</div>
												  <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Offer Type <span> *</span></label>
													<select name="offertype" required class="form-control default-select" id="sel1">
                                                      
                                                        <option>Fixed Amount</option>
                                                        <option>A percent amount discount</option>
                                                    
                                                     
                                                       
                                                    </select>
												</div>


												<div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Condition <span> *</span></label>
													<input name="amount" required class="form-control" placeholder="Amount or Percentage" value="{!! old('amount') !!}">
												</div>

                                              

											</div>
                                            <div class="filter cm-content-box box-primary style-1 mb-0 border-0">
                                                <div class="content-title">
                                                    
                                                    <button type="submit" class="btn btn-primary my-2">Submit</button>
                                                </div>
                                            </div>
											</form>
										</div>
									</div>
								</div>
							</div>
						
						</div>
					</div>
				</div>
			</div>
		</div>
		@endif
@endsection


	