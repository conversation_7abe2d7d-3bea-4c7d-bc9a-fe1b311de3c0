 @extends('admin.layout.master')

@section('content')
 @if(session('SendEmails')==1)
      <div class="content-body default-height">
			<div class="container-fluid">
				
				<!-- Row -->
				<div class="row">
					<div class="col-xl-12">
					
						
						<div class="row">
							<div class="col-xl-12">
								
							
								<div class="filter cm-content-box box-primary">
									<div class="content-title">
										<div class="cpa">
											Send Mail
										</div>
										<div class="tools">
											<a href="javascript:void(0);" class="expand SlideToolHeader"><i class="fal fa-angle-down"></i></a>
										</div>
									</div>
									<form method="post" action="{{route('send-email-send')}}" enctype='multipart/form-data'>
  {{csrf_field()}}
<div class="row">
@if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif
									<div class="cm-content-body  form excerpt">
										<div class="card-body">
										
											<div class="row">
												<div class="col-xl-12 col-sm-6">
													
													  <div class="mb-3">
														<label  class="form-label">Select User <span> *</span></label>
														<select id="single-select" style="width:100%;" required name="users">
                                                            <option value="">Select</option>
                                                             <option value="ALL">ALL</option>
															@foreach($users as $user)
															<option value="{{$user->id}}">{{$user->name}}</option>
                                                            @endforeach
                                                           
                                                        </select>
													  </div>
													
												</div>
                                               

                                                <div class="col-xl-12 col-sm-6">
													
													  <div class="mb-3">
														<label  class="form-label">Subject <span> *</span></label>
														<input type="text" required name="subject" class="form-control" placeholder="Subject">
													  </div>
													
												</div>

                                                <div class="col-xl-12 col-sm-12">
													
													  <div class="mb-3">
														<label  class="form-label">Message <span> *</span></label>
                                                        <textarea name="message" required class="form-control" rows="3" placeholder="Message"></textarea>
													  </div>
													
												</div>
                                                <div class="col-xl-12 col-sm-12">
													
                                                
                                                   <label class="imagepicker imagepicker-add thumbnail">
                                                        <input id="files" name="files[]"  type="file" multiple>
                                                   </label>
                                               

                                                </div>

                                                <div class="filter cm-content-box box-primary style-1 mb-0 border-0">
                                                    <div class="content-title">
                                                        
                                                        <button type="submit" class="btn btn-primary my-2">Send</button>
                                                    </div>
                                                </div>
                                               
											</div>	
											</form>
										
										</div>
									</div>
								</div>
							
								
								
							
							</div>
							
						</div>
					</div>
				</div>
			</div>
		</div>
    @endif   
@endsection


	