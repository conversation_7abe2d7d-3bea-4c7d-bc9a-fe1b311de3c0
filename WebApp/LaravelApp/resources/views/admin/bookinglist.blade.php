 @extends('admin.layout.master')

@section('content')
@if(session('ViewAllBooking')==1)
 <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-12">
                        <div class="card">
                            <div class="card-header justify-content-between">
                                <h4 class="card-title">All Booking</h4>

                                
                            </div>





                            <div class="card-body">
                                <div class="row mb-5 g-3">
                                    <div class="col-xl-3 col-lg-3 col-sm-6 col-12">
                                        <select id="single-select" class="location" style="width:100%;">
										{{!!$html!!}}
                                            </select>
                                    </div>
                                    <div class="col-xl-3 col-lg-3 col-sm-6 col-12 ">
                                        <select class="form-control default-select" id="sort1" class="sort">
                                                <option value="" selected="selected">Sorting</option>
                                                <option value="1">Sort by Latest</option>
                                                <option value="2">Sort by A-Z</option>
												
                                            </select>
                                    </div>

									<div class="col-xl-3 col-lg-3 col-sm-6 col-12 ">
                                        <select class="form-control default-select payment" id="payment">
                                                <option value="">Filter by Payment method</option>
                                                <option value="Card">Card</option>
                                                <option value="Knet">Knet</option>
												
                                            </select>
                                    </div>

									<div class="col-xl-3 col-lg-3 col-sm-6 col-12 ">
										<select class="form-control default-select" id="status1">
											<option value="" selected="selected">Filter by status</option>
											<option value="Scheduled">Scheduled</option>
											<option value="Cancelled">Cancelled </option>
											<option value="Completed">Completed</option>
										</select>
                                    </div>
                                   <div class="col-xl-3 col-lg-3 col-sm-6 col-12">
                                        <select id="vendor-booking-select" class="form-control default-select">
										{{!!$vendorhtml!!}}
                                            </select>
                                    </div>
                                    <div class="col-xl-4 col-lg-6 col-12">


                                        <div class="input-group">
                                            <input type="text" id="searchuser" class="form-control" placeholder="search users..." aria-label="Username" aria-describedby="basic-addon1">
                                            <span class="input-group-text" id="basic-addon1"><a href="javascript:void(0);" class="vendorsearch1">Search</a></span>
                                      </div>
                                    </div>
                                </div>

                               
                        

                                <div class="table-responsive">
                                    <table id="example4" class="display min-w850">
                                        <thead>
                                            <tr>
                                                <th>S.No.</th>
                                                <th>Date</th>
												<th>Booking ID</th>
                                                <th>User</th>
                                                <th>Vendor</th>
												<th>Area</th>
												
                                                <th>Total Amount</th>
												<th>Payment Method</th>
												<th>Payment Status</th>
                                                <th>Status</th>
                                               
                                               
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										@foreach($booking as $key => $book)
                                            <tr>
                                                <td>{{$key+1}}</td>
                                                <td data-order="{{strtotime($book->bookdate)}}">{{$book->bookdate}}</td>
                                                <td>{{$book->Bookid}}</td>
                                                <td>{{$book->name}}<br> {{@$book->email}}<br> {{@$book->user_phone}}</td>
                                                <td>{{$book->vendor_name}}</td>
                                                <td>{{$book->Area_Title}}</td>
                                                
                                              
                                             
                                                    <td data-order="{{$book->total_price}}">KWD {{$book->total_price}}</td>
													
													<td>{{$book->payment_method}}</td>
													<td>{{$book->payment_status}}</td>
                                                    <td><span class="badge light badge-warning mb-2">@if($book->status==1) Scheduled @endif @if($book->status==2) Completed @endif @if($book->status==3) Cancelled @endif @if($book->status==6) Failed @endif</span>
														
													</td>
                                                <td>
													<div class="d-flex">
                                                      
														<a href="{{route('view-bookdetails',$book->id)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="View"><i class="fa fa-eye"></i></a>
													<div class="dropdown">
															<button type="button" class="btn btn-success light sharp" data-bs-toggle="dropdown">
																<svg width="20px" height="20px" viewBox="0 0 24 24" version="1.1"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><rect x="0" y="0" width="24" height="24"/><circle fill="#000000" cx="5" cy="12" r="2"/><circle fill="#000000" cx="12" cy="12" r="2"/><circle fill="#000000" cx="19" cy="12" r="2"/></g></svg>
															</button>
															<div class="dropdown-menu">
																<a class="dropdown-item statusbooking" data-id="{{$book->id}}" data-status="1" href="#">Scheduled</a>
																<a class="dropdown-item statusbooking" data-id="{{$book->id}}" data-status="2" href="#">Completed</a>
																<a class="dropdown-item statusbooking" data-id="{{$book->id}}" data-status="3" href="#">Cancelled</a>


															</div>
														</div>
													</div>												
												</td>												
                                            </tr>
											 @endforeach  
											





                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		@endif
@endsection





	