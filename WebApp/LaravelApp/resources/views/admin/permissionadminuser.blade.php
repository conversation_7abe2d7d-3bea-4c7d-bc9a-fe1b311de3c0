 @extends('admin.layout.master')

@section('content')
@if(session('StaffPermission')==1)
     <div class="content-body default-height">
			<div class="container-fluid">
				
				<!-- Row -->
				<div class="row">
					<div class="col-xl-12">

                        <h3 class="title mb-5"> Permissions</h3>
					
						<div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Dashboard
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Dashboard View </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="DashBoardView" name="DashBoardView" data-id="{{$user->id}}" value="{{$user->DashBoardView}}" @if($user->DashBoardView==1) checked @endif type="checkbox"/>
                                                        <label for="DashBoardView" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                               

                                            
												

												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>



                        <div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Customer
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Customer View</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="CustomerView" name="CustomerView" data-id="{{$user->id}}" @if($user->CustomerView==1) checked @endif type ="checkbox"/>
                                                        <label for="CustomerView" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                                



                                            
												

												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>



                        <div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Vendor
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Add Vendor</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="AddVendor" name="AddVendor" data-id="{{$user->id}}" @if($user->AddVendor==1) checked @endif type="checkbox"/>
                                                        <label for="AddVendor" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												  <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Edit Vendor</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="EditVendor" name="EditVendor" data-id="{{$user->id}}" @if($user->EditVendor==1) checked @endif type="checkbox"/>
                                                        <label for="EditVendor" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Delete Vendor</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="DeleteVendor" name="DeleteVendor" data-id="{{$user->id}}" @if($user->DeleteVendor==1) checked @endif type="checkbox"/>
                                                        <label for="DeleteVendor" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>List Vendors</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="AllVendorView" name="AllVendorView" data-id="{{$user->id}}" @if($user->AllVendorView==1) checked @endif type="checkbox"/>
                                                        <label for="AllVendorView" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                            
												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>


                        <div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Services Category
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Add Service Category</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="AddServiceCategory" name="AddServiceCategory" data-id="{{$user->id}}" @if($user->AddServiceCategory==1) checked @endif type="checkbox"/>
                                                        <label for="AddServiceCategory" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												  <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Edit Service Category</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="EditServiceCategory" name="EditServiceCategory" data-id="{{$user->id}}" @if($user->EditServiceCategory==1) checked @endif type="checkbox"/>
                                                        <label for="EditServiceCategory" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												  <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Delete Service Category</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="DeleteServiceCategory" name="DeleteServiceCategory" data-id="{{$user->id}}" @if($user->DeleteServiceCategory==1) checked @endif type="checkbox"/>
                                                        <label for="DeleteServiceCategory" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												  <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>List Service Category</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission"  id="ServiceCategoryList" name="ServiceCategoryList" data-id="{{$user->id}}" @if($user->ServiceCategoryList==1) checked @endif type="checkbox"/>
                                                        <label for="ServiceCategoryList" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                            
												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>


                        <div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Services
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Add Service</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="AddServices" name="AddServices"  data-id="{{$user->id}}" @if($user->AddServices==1)  checked @endif type="checkbox"/>
                                                        <label for="AddServices" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												<div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Edit Service</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="EditServices" name="EditServices"  data-id="{{$user->id}}" @if($user->EditServices==1)  checked @endif  type="checkbox"/>
                                                        <label for="EditServices" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												<div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Delete Service</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="DeleteServices" name="DeleteServices"  data-id="{{$user->id}}" @if($user->DeleteServices==1)  checked @endif  type="checkbox"/>
                                                        <label for="DeleteServices" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												<div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>List Services</label>
                                                    <div class="material-switch">
                                                        <input  class="changepermission" id="ServicesList" name="ServicesList"  data-id="{{$user->id}}" @if($user->ServicesList==1)  checked @endif  type="checkbox"/>
                                                        <label for="ServicesList" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                            
												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>


                        <div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Reports
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Sale Report</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="ViewSaleReport" name="ViewSaleReport" data-id="{{$user->id}}" @if($user->ViewSaleReport==1)  checked @endif type="checkbox"/>
                                                        <label for="ViewSaleReport" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												    <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Vendor Report</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="ViewVendorReport" name="ViewVendorReport" data-id="{{$user->id}}" @if($user->ViewVendorReport==1)  checked @endif type="checkbox"/>
                                                        <label for="ViewVendorReport" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												  <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>User Report</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="ViewUserReport" name="ViewUserReport" data-id="{{$user->id}}" @if($user->ViewUserReport==1)  checked @endif type="checkbox"/>
                                                        <label for="ViewUserReport" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                            
												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>



                        <div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Booking
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>View All Booking</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="ViewAllBooking" name="ViewAllBooking" data-id="{{$user->id}}" @if($user->ViewAllBooking==1)  checked @endif type="checkbox"/>
                                                        <label for="ViewAllBooking" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>View Cancelled Booking</label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="ViewAllCancelBooking" name="ViewAllCancelBooking" data-id="{{$user->id}}" @if($user->ViewAllCancelBooking==1)  checked @endif type="checkbox"/>
                                                        <label for="ViewAllCancelBooking" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                            
												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>


                        <div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Notifications Control
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Notifications Control
                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="ViewNotification" name="ViewNotification" data-id="{{$user->id}}" @if($user->ViewNotification==1)  checked @endif type="checkbox"/>
                                                        <label for="ViewNotification" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                            
												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>
						
						
						
						<div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Send E-mails
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Send E-mails
                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="SendEmails" name="SendEmails" data-id="{{$user->id}}" @if($user->SendEmails==1)  checked @endif type="checkbox"/>
                                                        <label for="SendEmails" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                            
												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>
						
						
						
						<div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Send Messages
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Send Messages
                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="SendMessages" name="SendMessages" data-id="{{$user->id}}" @if($user->SendMessages==1)  checked @endif type="checkbox"/>
                                                        <label for="SendMessages" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                            
												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>


                        <div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Uploaded Files
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>View Uploaded Files
                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="ViewUploadedFiles" name="ViewUploadedFiles" data-id="{{$user->id}}" @if($user->ViewUploadedFiles==1)  checked @endif type="checkbox"/>
                                                        <label for="ViewUploadedFiles" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>


                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Add Uploaded Files

                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="AddUploadedFIles" name="AddUploadedFIles" data-id="{{$user->id}}" @if($user->AddUploadedFIles==1)  checked @endif type="checkbox"/>
                                                        <label for="AddUploadedFIles" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Delete Uploaded Files

                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="DeleteUploadedFIles" name="DeleteUploadedFIles" data-id="{{$user->id}}" @if($user->DeleteUploadedFIles==1)  checked @endif type="checkbox"/>
                                                        <label for="DeleteUploadedFIles" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>


                                                

                                                

                                            
												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>


                        <div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Promotions
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>View Promotions
                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="ViewPromotionsList" name="ViewPromotionsList" data-id="{{$user->id}}" @if($user->ViewPromotionsList==1)  checked @endif type="checkbox"/>
                                                        <label for="ViewPromotionsList" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>


                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Add Promotion

                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="AddPromotion" name="AddPromotion" data-id="{{$user->id}}" @if($user->AddPromotion==1)  checked @endif type="checkbox"/>
                                                        <label for="AddPromotion" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Edit Promotion

                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="EditPromotion" name="EditPromotion" data-id="{{$user->id}}" @if($user->EditPromotion==1)  checked @endif type="checkbox"/>
                                                        <label for="EditPromotion" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Delete Promotion

                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="DeletePromotion" name="DeletePromotion" data-id="{{$user->id}}" @if($user->DeletePromotion==1)  checked @endif type="checkbox"/>
                                                        <label for="DeletePromotion" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>


                                                

                                                

                                            
												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>

                        <div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Settings
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>SMTP Settings
                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="SMTP" name="SMTP" type="checkbox" data-id="{{$user->id}}" @if($user->SMTP==1)  checked @endif />
                                                        <label for="SMTP" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>General Settings
                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="SettingsGeneral" name="SettingsGeneral" type="checkbox" data-id="{{$user->id}}" @if($user->SettingsGeneral==1)  checked @endif/>
                                                        <label for="SettingsGeneral" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Governorate
                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="Governarate" name="Governarate" type="checkbox" data-id="{{$user->id}}" @if($user->Governarate==1)  checked @endif/>
                                                        <label for="Governarate" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Areas
                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="Areas" name="Areas" type="checkbox" data-id="{{$user->id}}" @if($user->Areas==1)  checked @endif/>
                                                        <label for="Areas" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Privacy Policy
                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="PrivacyPolicy" name="PrivacyPolicy" type="checkbox" data-id="{{$user->id}}" @if($user->PrivacyPolicy==1)  checked @endif/>
                                                        <label for="PrivacyPolicy" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Terms & Condition
                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="TermsandConditions" name="TermsandConditions" type="checkbox" data-id="{{$user->id}}" @if($user->TermsandConditions==1)  checked @endif/>
                                                        <label for="TermsandConditions" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>


                                                


                                                

                                                

                                            
												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>
						
						
						
						<div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Staffs
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>View Staffs
                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="ViewStaffs" name="ViewStaffs" data-id="{{$user->id}}" @if($user->ViewStaffs==1)  checked @endif type="checkbox"/>
                                                        <label for="ViewStaffs" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>


                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Add Staff

                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="AddStaffs" name="AddStaffs" data-id="{{$user->id}}" @if($user->AddStaffs==1)  checked @endif type="checkbox"/>
                                                        <label for="AddStaffs" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Edit Staff

                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="EditStaff" name="EditStaff data-id="{{$user->id}}" @if($user->EditStaff==1)  checked @endif type="checkbox"/>
                                                        <label for="EditStaff" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Staff Permision

                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changepermission" id="StaffPermission" name="StaffPermission" data-id="{{$user->id}}" @if($user->StaffPermission==1)  checked @endif type="checkbox"/>
                                                        <label for="StaffPermission" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>


                                                

                                                

                                            
												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>
						
						
						
						
						
						
					</div>
				</div>
			</div>
		</div>
		@endif
@endsection


	