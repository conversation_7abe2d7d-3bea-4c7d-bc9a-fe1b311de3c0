 @extends('admin.layout.master')

@section('content')
@if(session('Governarate')==1)
 <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-8">
                        <div class="card">
                            <div class="card-header justify-content-between">
                                <h4 class="card-title">Governorate</h4>
                                <div class="col-xl-6 col-xxl-9">

                                       
                                    <input type="text" class="form-control"  id="search" name="search" placeholder="Search" >
                                
                            </div>

                               
                            </div>





                            <div class="card-body">
                                <div class="row mb-5 justify-content-end align-items-end">
                                  
                                 
                                
                
                                   
                                </div>

                               
                        

                                <div class="table-responsive">
                                    <table  class="table" id="table">
                                        <thead class="thead-info">
                                            <tr>
                                                <th></th>
                                                <th>Name</th>
                                                
                                                <th>Status</th>
												<th>Options</th>
                                                
                                            </tr>
                                        </thead>
                                        <tbody>
										 @foreach($governorates as $key => $governorate)
                                            <tr id="{{$governorate->Gov_ID}}">
                                                <td>{{$key+1}}</td>
                                                <td>{{$governorate->Gov_Title}}</td>
                                                
                                            
                                                <td>
                                                    <div class="material-switch">
                                                        <input id="someSwitchOptionDefault02" class="enablegov" data-id="{{$governorate->Gov_ID}}" name="enable" value="{{$governorate->enabled}}" @if($governorate->enabled==1) checked @endif type="checkbox" />
                                                        <label for="someSwitchOptionDefault02" class="label-default"></label>
                                                    </div>
												  </td>
												  <td>
												  
                                                    <div class="d-flex">
                                                     
														<a href="{{route('edit-governorate',$governorate->Gov_ID)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="Edit"><i class="fa fa-pencil"></i></a>
													
														<a href="#" data-id="{{$governorate->Gov_ID}}" class="deletegov btn btn-danger shadow btn-xs sharp" title="delete"><i class="fa fa-trash"></i></a>
													</div>		
												</td>
                                             
												
											
                                            </tr>
										@endforeach  

                                          


                                          
                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="col-xl-4">
								

                        <div class="filter cm-content-box box-primary">
                            <div class="content-title">
                                <div class="cpa">Add New Governorate			
                                </div>
                                <div class="tools">
                                    <a href="javascript:void(0);" class="expand SlideToolHeader"><i class="fal fa-angle-down"></i></a>
                                </div>
                            </div>
                            <div class="cm-content-body  form excerpt">
                                <div class="card-body">
								 @if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif
                                     <form method="post" action="{{route('governorate-save')}}">
                                    <div class="row">
{{csrf_field()}}
                                        <div class="col-xl-12 col-sm-12 mb-3">
                                            <label class="form-label">Name<span> *</span></label>
                                            <input type="text" name="Gov_Title" required class="form-control" placeholder="Name" value="{!! old('Gov_Title') !!}">
                                        </div>
                                       
                                     

                                      

                                      

                                        
                                    

                                    </div>
                                    <div class="filter cm-content-box box-primary style-1 mb-0 border-0">
                                        <div class="content-title">
                                            
                                            <button type="submit" class="btn btn-primary my-2">Save</button>
                                        </div>
                                    </div>
									
									</form>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		@endif
@endsection


	