 @extends('admin.layout.master')

@section('content')
@if(session('ViewPromotionsList')==1)
     <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-12">
                        <div class="card">
                            <div class="card-header justify-content-between">
                                <h4 class="card-title">Promotions</h4>

                                <a href="{{route('add-coupon')}}" class="btn btn-primary btn-rounded"> Add Promotion</a>
                            </div>





                            <div class="card-body">
                                <div class="row mb-5 justify-content-end align-items-end">
                                  
                                    <div class="col-xl-3 col-xxl-12">

                                       
                                            <input type="text" class="form-control" id="searchuser1" name="search" placeholder="Search" >
                                        
                                    </div>

                                
                
                                   
                                </div>

                               
                        

                                <div class="table-responsive">
                                    <table id="example8" class="display min-w850">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th>Title</th>
                                                <th>Promo Code</th>
                                                <th>Start Date</th>
                                                <th>End Date</th>
                                                <th>Applicable Users</th>
                                               <th>Applicable Vendors</th>
                                                <th>Status</th>
												<th>Options</th>
                                                
                                            </tr>
                                        </thead>
                                        <tbody>
										@foreach($coupons as $key=>$coupon)
                                            <tr>
                                                <td>{{$key+1}}</td>
                                                <td>{{$coupon->coupon_name}}</td>
                                                <td>{{$coupon->coupon_code}}</td>
                                                <td>{{$coupon->start_date}}</td>
                                                <td>{{$coupon->end_date}}</td>
                                                <td>
                                                   
                                                    @if($coupon->applicable=='All Users')
                                                    All Users
                                                    @else
 @php
                                                        $user_names = DB::table('users')
    ->where('id', explode(',', $coupon->user_id))
    ->pluck('name')->implode(', ');;
                                                        @endphp
                                                        {{ $user_names }}
                                                    @endif
                                                    
                                                 
                                                    
                                                    
                                                    </td>
                                                <td>
                                                    @php
                                                    $available_vendors=$coupon->available_for_vendors;
                                                    @endphp
                                                    @if($coupon->available_for_vendors==1)
                                                        @php
                                                        $vendor_names = DB::table('vendor')
    ->whereIn('vendor_id', explode(',', $coupon->available_vendors))
    ->pluck('vendor_name')
    ->implode(', ');
                                                        @endphp
                                                        {{ $vendor_names }}
                                                    
                                                    @else
                                                All Vendors
                                                   @endif
                                                   </td>
                                                
                                             
                                                <td>
                                                    <div class="material-switch">
                                                        <input id="someSwitchOptionDefault02" data-id="{{$coupon->coupon_id}}" class="Enablecoupon" name="enabled" type="checkbox" value="1" @if($coupon->enabled==1) checked @endif/>
                                                        <label for="someSwitchOptionDefault02" class="label-default"></label>
                                                    </div>
												  </td>
												  <td>
												  
                                                    <div class="d-flex">
                                                     @if(session('EditPromotion')==1)
														<a href="{{route('edit-coupon',$coupon->coupon_id)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="Edit"><i class="fa fa-pencil"></i></a>
													@endif
													 @if(session('DeletePromotion')==1)
														<a href="javascript:void(0);" data-id="{{$coupon->coupon_id}}" class="Deletecoupon btn btn-danger shadow btn-xs sharp" title="delete"><i class="fa fa-trash"></i></a>
													@endif
													</div>		
												</td>
                                             
												
											
                                            </tr>

											@endforeach
                                          
                                          
                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		@endif
@endsection


	