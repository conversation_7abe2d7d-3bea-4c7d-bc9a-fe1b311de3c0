 @extends('admin.layout.master')

@section('content')
@if(session('SMTP')==1)
     <div class="content-body default-height">
			<div class="container-fluid">
				
				<!-- Row -->
				<div class="row">
					<div class="col-xl-12">
					
						
						<div class="row">
							<div class="col-xl-12">
								
							
								
							


							
								
								
								

								<div class="filter cm-content-box box-primary">
									<div class="content-title">
										<div class="cpa">Contact Us						
										</div>
										<div class="tools">
											<a href="javascript:void(0);" class="expand SlideToolHeader"><i class="fal fa-angle-down"></i></a>
										</div>
									</div>
									<div class="cm-content-body  form excerpt">
										<div class="card-body">
											
											<div class="row">
<form method="post" action="{{route('contactus-save')}}" >
  {{csrf_field()}}
<div class="row">
@if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif
                                                <div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Customer Services <span> *</span></label>
													<input name="customer_service" required class="form-control" placeholder="Customer Services" value="{{$contactus->customer_service}}">
												</div>
												<div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Whatsapp <span> *</span></label>
													<input name="whatsapp" required class="form-control" placeholder="Whatsapp" value="{{$contactus->whatsapp}}">
												</div>
												<div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Website <span> *</span></label>
													<input name="website" required class="form-control" placeholder="Website" value="{{$contactus->website}}">
												</div>
												<div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Facebook <span> *</span></label>
													<input name="facebook" required class="form-control" placeholder="Facebook" value="{{$contactus->facebook}}">
												</div>
												<div class="col-xl-6 col-sm-12 mb-3">
													<label class="form-label">Instagram <span> *</span></label>
													<input name="instagram" required class="form-control" placeholder="Instagram" value="{{$contactus->instagram}}">
												</div>

                                               
                                            <div class="filter cm-content-box box-primary style-1 mb-0 border-0">
                                                <div class="content-title">
                                                    
                                                    <button type="submit" class="btn btn-primary my-2">Submit</button>
                                                </div>
                                            </div>
											</form>
										</div>
									</div>
								</div>
							</div>
						
						</div>
					</div>
				</div>
			</div>
		</div>
		@endif
@endsection


	