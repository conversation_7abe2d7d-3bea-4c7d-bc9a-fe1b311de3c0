 @extends('admin.layout.master')

@section('content')
@if(session('AllVendorView')==1)
  <div class="content-body default-height">
			<div class="container-fluid">
				
				<!-- Row -->
				<div class="row">
					<div class="col-xl-12">

                        <h3 class="title mb-5"> Permissions</h3>
					
						<div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											System
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Seller Dashboard</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="dashboard" name="dashboard" data-id="{{$vendor->vendor_id}}" value="{{$vendor->dashboard}}" @if($vendor->dashboard==1) checked @endif type="checkbox" />
                                                        <label for="dashboard" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                              

                                            
												

												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>


	<div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Calendar
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Calendar</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="calendar" name="calendar" data-id="{{$vendor->vendor_id}}" value="{{$vendor->calendar}}" @if($vendor->calendar==1) checked @endif type="checkbox"/>
                                                        <label for="calendar" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                              

                                            
												

												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>
						
						
						
						<div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Booking
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Booking</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="booking" name="booking" data-id="{{$vendor->vendor_id}}" value="{{$vendor->booking}}" @if($vendor->booking==1) checked @endif type="checkbox"/>
                                                        <label for="booking" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Add Booking</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="addbooking" name="addbooking" data-id="{{$vendor->vendor_id}}" value="{{$vendor->addbooking}}" @if($vendor->addbooking==1) checked @endif type="checkbox"/>
                                                        <label for="addbooking" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                              

                                            
												

												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>
						
						
						
						<div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Client
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Client List</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="clientlist" name="clientlist" data-id="{{$vendor->vendor_id}}" value="{{$vendor->clientlist}}" @if($vendor->clientlist==1) checked @endif type="checkbox"/>
                                                        <label for="clientlist" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Add Client</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="addclient" name="addclient" data-id="{{$vendor->vendor_id}}" value="{{$vendor->addclient}}" @if($vendor->addclient==1) checked @endif type="checkbox"/>
                                                        <label for="someSwitchOptionDefault02" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Edit Client</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="editclient" name="editclient" data-id="{{$vendor->vendor_id}}" value="{{$vendor->editclient}}" @if($vendor->editclient==1) checked @endif type="checkbox"/>
                                                        <label for="editclient" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Delete Client</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="deleteclient" name="deleteclient" data-id="{{$vendor->vendor_id}}" value="{{$vendor->deleteclient}}" @if($vendor->deleteclient==1) checked @endif type="checkbox"/>
                                                        <label for="deleteclient" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>View Client</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="viewclient" name="viewclient" data-id="{{$vendor->vendor_id}}" value="{{$vendor->viewclient}}" @if($vendor->viewclient==1) checked @endif type="checkbox"/>
                                                        <label for="viewclient" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                              

                                            
												

												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>
						
						
						<div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Employees
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Employees List</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="employeelist" name="employeelist" data-id="{{$vendor->vendor_id}}" value="{{$vendor->employeelist}}" @if($vendor->employeelist==1) checked @endif type="checkbox"/>
                                                        <label for="employeelist" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Add Employee</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="addemployee" name="addemployee" data-id="{{$vendor->vendor_id}}" value="{{$vendor->addemployee}}" @if($vendor->addemployee==1) checked @endif type="checkbox"/>
                                                        <label for="addemployee" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Edit Employee</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="editemployee" name="editemployee" data-id="{{$vendor->vendor_id}}" value="{{$vendor->editemployee}}" @if($vendor->editemployee==1) checked @endif type="checkbox"/>
                                                        <label for="editemployee" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                              
 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>View Employee</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="viewemployee" name="viewemployee" data-id="{{$vendor->vendor_id}}" value="{{$vendor->viewemployee}}" @if($vendor->viewemployee==1) checked @endif type="checkbox"/>
                                                        <label for="viewemployee" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
                                            
												

												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>


                        <div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Services
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Services List</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="serviceslist" name="serviceslist" data-id="{{$vendor->vendor_id}}" value="{{$vendor->serviceslist}}" @if($vendor->serviceslist==1) checked @endif type="checkbox"/>
                                                        <label for="serviceslist" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Add Service
                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="addservice" name="addservice" data-id="{{$vendor->vendor_id}}" value="{{$vendor->addservice}}" @if($vendor->addservice==1) checked @endif type="checkbox"/>
                                                        <label for="addservice" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>


                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Service Edit
                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="serviceedit" name="serviceedit" data-id="{{$vendor->vendor_id}}" value="{{$vendor->serviceedit}}" @if($vendor->serviceedit==1) checked @endif type="checkbox"/>
                                                        <label for="serviceedit" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>


                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Service Delete
                                                    </label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="servicedelete" name="servicedelete" data-id="{{$vendor->vendor_id}}" value="{{$vendor->servicedelete}}" @if($vendor->servicedelete==1) checked @endif type="checkbox"/>
                                                        <label for="servicedelete" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                                
                                              



                                            
												

												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>



                        <div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Reviews
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Reviews List</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="reviewslist" name="reviewslist" data-id="{{$vendor->vendor_id}}" value="{{$vendor->reviewslist}}" @if($vendor->reviewslist==1) checked @endif type="checkbox"/>
                                                        <label for="reviewslist" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>
												
												
												 <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>View Reviews</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="viewreviews" name="viewreviews" data-id="{{$vendor->vendor_id}}" value="{{$vendor->viewreviews}}" @if($vendor->viewreviews==1) checked @endif type="checkbox"/>
                                                        <label for="viewreviews" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                            
												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>


                       


                        <div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Settings
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
<div class="row">
										
                                                <div class="form-group col-md-3">

                                                    <div class="permition-box">
                                                    <label>Settings</label>
                                                    <div class="material-switch">
                                                        <input class="changesellerpermission" id="settings" name="settings" data-id="{{$vendor->vendor_id}}" value="{{$vendor->settings}}" @if($vendor->settings==1) checked @endif type="checkbox"/>
                                                        <label for="settings" class="label-default"></label>
                                                    </div>
                                                </div>
                                                </div>

                                            
												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
								
								</div>
							</div>
						</div>


                      


                        


                       


                        

                      
					  
					  
					  
					  
					  
					  
					</div>
				</div>
			</div>
		</div>
	@endif
@endsection




	