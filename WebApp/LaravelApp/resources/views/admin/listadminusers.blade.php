 @extends('admin.layout.master')

@section('content')
@if(session('ViewStaffs')==1)
    <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-12">
                        <div class="card">
                            <div class="card-header justify-content-between">
                                <h4 class="card-title">All Staffs</h4>

                                
                            </div>





                            <div class="card-body">
                             

                               
                        

                                <div class="table-responsive">
                                    <table id="example4" class="display min-w850">
                                        <thead>
                                            <tr>
                                                <th>S.No.</th>
                                                <th>Name</th>
												<th>Image</th>
                                                <th>Phone</th>
                                                 <th>E-mail</th>
                                               
                                               
                                                <th>Action</th>
												<th>Option</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										@foreach($users as $key => $user)
                                            <tr>
                                                <td>{{$key+1}}</td>
                                                <td>{{$user->admin_name}}</td>
                                                <td>	<img src="{{asset($user->admin_image)}}" width="100"/></td>
                                                <td>{{$user->admin_phone}}</td>
                                                <td>{{$user->admin_email}}</td>
                                                
                                                 <td>
													<div class="d-flex">
                                                        @if(session('EditStaff')==1)
														<a href="{{route('Edit-admin-user',$user->id)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="Edit"><i class="fa fa-pencil"></i></a>
														@endif
														
													</div>												
												</td>
												
												<td>
												@if(session('StaffPermission')==1)
													<div class="dropdown">
														<button type="button" class="btn btn-success light sharp" data-bs-toggle="dropdown">
															<svg width="20px" height="20px" viewBox="0 0 24 24" version="1.1"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><rect x="0" y="0" width="24" height="24"/><circle fill="#000000" cx="5" cy="12" r="2"/><circle fill="#000000" cx="12" cy="12" r="2"/><circle fill="#000000" cx="19" cy="12" r="2"/></g></svg>
														</button>
														<div class="dropdown-menu">
															
															<a class="dropdown-item" href="{{route('Permission-admin-user',$user->id)}}"> Admin Permission</a>
															


														</div>
													</div>
													@endif
												</td>											
                                            </tr>
											 @endforeach  
											





                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		@endif
@endsection


	