 @extends('admin.layout.master')

@section('content')
 @if(session('AddServices')==1)
    <div class="content-body default-height">
			<div class="container-fluid">
				
				<!-- Row -->
				<div class="row">
					<div class="col-xl-12">
					
						<div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Add Service
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
										  <form method="post" action="{{route('service-save')}}" enctype='multipart/form-data'>
  {{csrf_field()}}
<div class="row">
@if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif

										
                                                <div class="form-group col-md-6">
                                                    <label>Service Name <span> *</span></label>
                                                    <input type="text" class="form-control" required placeholder="Service Name" value="{!! old('Services_Name') !!}" name="Services_Name">
                                                </div>

                                                <div class="form-group col-md-6">
													<label  class="form-label">Choose service category <span> *</span></label>
													<select  class="form-control default-select" required id="sel2" name="Services_Category">
                                                        <option value="">Select</option>
														 @foreach($parentcategories as  $parentcategory)
											    <option value="{{$parentcategory->Cat_ID}}">{{$parentcategory->Cat_Name}}</option>
														@endforeach  
                                                       
                                                        
                                                        </select>
                                                </div>
                                              
        
												

												
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
									<div class="filter cm-content-box box-primary style-1 mb-0 border-0">
										<div class="content-title">
											
											<button type="submit" class="btn btn-primary my-2">Submit</button>
										</div>
									</div>
								</div>
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		@endif
@endsection


	