 @extends('admin.layout.master')

@section('content')
 @if(session('EditServiceCategory')==1)
  <div class="content-body default-height">
			<div class="container-fluid">
				
				<!-- Row -->
				<div class="row">
					<div class="col-xl-12">
					
						<div class="row">
						
							<div class="col-xl-12">
								<div class="filter cm-content-box box-primary">
									<div class="content-title flex-wrap">
										<div class="cpa d-flex align-items-center flex-wrap">
											Edit Service Categories
											
										</div>
										
									</div>
									<div class="cm-content-body form excerpt rounded-0">
										<div class="card-body">
										  <form method="post" action="{{route('update-ServiceCategory',$categories->Cat_ID)}}" enctype='multipart/form-data'>
  {{csrf_field()}}
                                              <div class="row">
										@if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif


                                                <div class="form-group col-md-6">
                                                    <label>Service Name (English)<span> *</span></label>
                                                    <input type="text" name="servicename" required class="form-control" value="{{$categories->Cat_Name}}" placeholder="Service Name">
                                                </div>
                                                <div class="form-group col-md-6">
                                                    <label>Service Name (Arabic)<span> *</span></label>
                                                    <input type="text" name="servicename_ar" required class="form-control" value="{{$categories->Cat_Name_ar}}" placeholder="Service Name">
                                                </div>
        
												<div class="form-group col-md-6">
                                                    <label>Ordering Number <span> *</span></label>
                                                    <input type="number" name="ordering" value="{{$categories->Ordering}}" required class="form-control" placeholder="Ordering Number">
                                                </div>

												<div class="form-group col-md-6">
                                                    <label>Icon  (215px x 215px) <span> *</span></label>
                                                   <a href="javascript:void(0);" class="imagepicker imagepicker-add thumbnail modalpopup" data-store="Icon" data-loc="Icons" data-count="1" > </a>
															<div id="Icons"></div>
															<input type="hidden" name="Icon" id="Icon" value="" />
													<img src="{{asset($categories->Icon)}}" width="75"/>
                                                </div>
                                           
											</div>

                                       
											
                                        
        
        
                                     


										</div>		
									</div>
									<div class="filter cm-content-box box-primary style-1 mb-0 border-0">
										<div class="content-title">
											
											<button type="submit" class="btn btn-primary my-2">Submit</button>
										</div>
									</div>
									
								</div>
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		@endif
@endsection


	