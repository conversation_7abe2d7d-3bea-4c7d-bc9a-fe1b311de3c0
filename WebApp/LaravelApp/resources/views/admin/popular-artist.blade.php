 @extends('admin.layout.master')

@section('content')
@if(session('AllVendorView')==1)
 <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-12">
                        <div class="card">
                            <div class="card-header justify-content-between">
                                <h4 class="card-title">Popular Artist</h4>

                                
                            </div>





                            <div class="card-body">
							@if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif
                                <div class="row mb-5">
                                   
                                    <div class="col-xl-3 col-lg-3 col-sm-6 col-12 ">
                                        <select class="form-control default-select vendorsort"  id="single-select">
                                                <option value="" selected="selected">Sort By Vendor</option>
												@foreach($vendorslist as $vendorlistnew)
                                                <option value="{{ $vendorlistnew->vendor_name}}">{{ $vendorlistnew->vendor_name}}</option>
												@endforeach
                                            </select>
                                    </div>
                                    <div class="col-xl-6 col-lg-6 col-12">


                                        <div class="input-group">
                                            <input type="text" class="form-control" id="searchpopular" placeholder="search Artist..." aria-label="Username" aria-describedby="basic-addon1">
                                            <span class="input-group-text" id="basic-addon1"><a href="javascript:void(0);" class="artistsearch">Search</a></span>
                                      </div>
                                    </div>
                                </div>

                               
                        

                                <div class="table-responsive">
                                    <table id="example85" class="display min-w850">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th>Vendor Name</th>
                                                <th>Artist Name</th>
                                               
                                               
												<th>Popular</th>
												<th>Position</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										
										@foreach($vendors as $key => $vendor)
                                            <tr id="{{$vendor->staff_id}}">
                                                <td>{{$key+1}}</td>
                                                <td>{{$vendor->vendor_name}}</td>
                                                <td>{{$vendor->staff_name}}</td>
                                              
                                                
                                             
                                               
												  <td>
												  
												  <div class="material-switch justify-content-center">
													<input id="someSwitchOptionDefault{{$key+1}}" data-id="{{$vendor->staff_id}}" class="enabledpopular" name="online_popular" type="checkbox" value="1" @if($vendor->popular==1) checked @endif/>
													<label for="someSwitchOptionDefault{{$key+1}}" class="label-default"></label>
												</div>
												
												</td>
												<td>{{$vendor->position}}</td>
                                                <td><div class="d-flex">
                                                       
														<a href="{{route('PopularArtistEdit',$vendor->staff_id)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="Edit"><i class="fa fa-pencil"></i></a>
														
													</div>	</td>
                                            </tr>
                                           @endforeach  
                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
        
		 
	@endif
@endsection




	