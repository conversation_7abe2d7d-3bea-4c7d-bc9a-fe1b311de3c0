 @extends('admin.layout.master')

@section('content')
@if(session('ServiceCategoryList')==1)
  <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-12">
                        <div class="card">
                            <div class="card-header justify-content-between">
                                <h4 class="card-title">Service Categories </h4>

                                <a href="{{route('service-category')}}" class="btn btn-primary btn-rounded"> Add Categories</a>
                            </div>
                            <div class="card-body">
									<div class="row mb-5">
                                 
                                       <div class="card-body">
                                <div class="row mb-5">
                                   
                                  
                                    <div class="col-xl-6 col-lg-6 col-12">


                                        <div class="input-group">
                                            <input type="text" id="searchcategory"  class="form-control" placeholder="search Category..." aria-label="Category" aria-describedby="basic-addon1">
                                            <span class="input-group-text" id="basic-addon1" ><a class="categorysearch3" href="javascript:void(0);">Search</a></span>
                                      </div>
                                    </div>
                                </div>


                                <div class="table-responsive">
                                    <table id="example3" class="display min-w850">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th>ICON</th>
                                                <th>Service Name (English)</th>
                                                <th>Service Name (Arabic)</th>

                                               
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
										<?php $k=1; ?>
										@foreach($categories as $category)
										
                                            <tr id="{{$category->Cat_ID}}">
                                                <td>{{$k}}</td>
                                                <td>
												<img src="{{asset($category->Icon)}}" width="100"/>
												</td>
                                                <td>   
                                                    
                                                    <a href="javascript:void()" class="btn btn-primary light btn-xs mb-1">{{$category->Cat_Name}}</a> 
                                                    
                                                
                                                </td>
												 <td>   
                                                    
                                                    <a href="javascript:void()" class="btn btn-primary light btn-xs mb-1">{{$category->Cat_Name_ar}}</a> 
                                                    
                                                
                                                </td>
                                              
                                                
                                                
                                                <td>
													<div class="d-flex">
                                                       @if(session('EditServiceCategory')==1)
														<a href="{{route('editservice-category',$category->Cat_ID)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="Edit"><i class="fa fa-pencil"></i></a>
														@endif
														 @if(session('DeleteServiceCategory')==1)
														<a href="#"  data-id="{{$category->Cat_ID}}" class="DeleteCategory btn btn-danger shadow btn-xs sharp" title="delete"><i class="fa fa-trash"></i></a>
														@endif
													</div>												
												</td>												
                                            </tr>
											<?php $k++; ?>
											@endforeach
                                           
                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		@endif
@endsection


	