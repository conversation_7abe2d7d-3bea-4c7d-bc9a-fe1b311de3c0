 @extends('admin.layout.master')

@section('content')
	@if(session('Areas')==1)
 <div class="content-body default-height">
            <div class="container-fluid">
              


                <div class="row">
                   
                  
					<div class="col-8">
                        <div class="card">
                            <div class="card-header justify-content-between">
                                <h4 class="card-title">Areas
                                </h4>
                                <div class="col-xl-3 col-xxl-12">

                                       
                                    <input type="text" class="form-control" id="search" name="search" placeholder="Type name & Enter" >
                                
                            </div>

                               
                            </div>





                            <div class="card-body">
                                <div class="row mb-5 justify-content-end align-items-end">
                                  
                                 
                                
                
                                   
                                </div>

                               
                        

                                <div class="table-responsive">
                                    <table  class="table" id="table">
                                        <thead class="thead-info">
                                            <tr>
                                                <th></th>
                                                <th>Name</th>
												<th>Name (Arabic)</th>
                                                <th>Governorate Name</th>
                                                
                                                <th>Status</th>
												<th>Options</th>
                                                
                                            </tr>
                                        </thead>
                                        <tbody>
										@foreach($areas as $key => $area)
                                            <tr id="{{$area->Area_id}}">
                                                <td>{{$key+1}}</td>
                                                <td>{{$area->Area_Title}}</td>
                                                <td>{{$area->Area_Title_ar}}</td>
                                             <td>{{$area->Gov_Title}}</td>
                                                <td>
                                                    <div class="material-switch">
                                                        <input id="someSwitchOptionDefault{{$key+1}}" class="enablearea" data-id="{{$area->Area_id}}" name="enable" value="{{$area->enabled}}" @if($area->enabled==1) checked @endif type="checkbox"/>
                                                        <label for="someSwitchOptionDefault{{$key+1}}" class="label-default"></label>
                                                    </div>
												  </td>
												  <td>
												  
                                                    <div class="d-flex">
                                                     
														<a href="{{route('edit-area',$area->Area_id)}}" class="btn btn-primary shadow btn-xs sharp me-1" title="Edit"><i class="fa fa-pencil"></i></a>
													
														<a href="#" data-id="{{$area->Area_id}}" class="deletearea btn btn-danger shadow btn-xs sharp" title="delete"><i class="fa fa-trash"></i></a>
													</div>		
												</td>
                                             
												
											
                                            </tr>
@endforeach  

                                           


                                          
                                          
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="col-xl-4">
								

                        <div class="filter cm-content-box box-primary">
                            <div class="content-title">
                                <div class="cpa">Add New Area		
                                </div>
                                <div class="tools">
                                    <a href="javascript:void(0);" class="expand SlideToolHeader"><i class="fal fa-angle-down"></i></a>
                                </div>
                            </div>
                            <div class="cm-content-body  form excerpt">
                                <div class="card-body">
                                    @if (count($errors) > 0)
                  @if($errors->any())
                    <div class="alert alert-primary" role="alert">
                      {{$errors->first()}}
                      
                    </div>
                  @endif
              @endif
			  @if(session()->has('message'))
    <div class="alert alert-success">
        {{ session()->get('message') }}
    </div>
@endif

  <form method="post" action="{{route('area-save')}}">
  {{csrf_field()}}
                                    <div class="row">

                                        <div class="col-xl-12 col-sm-12 mb-3">
                                            <label class="form-label">Name (English)<span> *</span></label>
                                            <input type="text" required name="Area_Title" class="form-control" value="{!! old('Area_Title') !!}" placeholder="Name">
                                        </div>
										<div class="col-xl-12 col-sm-12 mb-3">
                                            <label class="form-label">Name (Arabic)<span> *</span></label>
                                            <input type="text" required name="Area_Title_ar" class="form-control" value="{!! old('Area_Title_ar') !!}" placeholder="Name">
                                        </div>
                                       
                                     

                                      

                                        <div class="col-xl-12 col-sm-12 mb-3">
                                            <label class="form-label">Governorate <span> *</span></label>
                                            <select id="single-select" name="governarate" required style="width:100%;">
                                                <option value="">Choose Location</option>
                                               @foreach($governorates as  $governorate)
											    <option value="{{$governorate->Gov_ID}}">{{$governorate->Gov_Title}}</option>
												@endforeach  
                                            </select>
                                        </div>

                                      

                                        
                                    

                                    </div>
                                    <div class="filter cm-content-box box-primary style-1 mb-0 border-0">
                                        <div class="content-title">
                                            
                                            <button type="submit" class="btn btn-primary my-2">Save</button>
                                        </div>
                                    </div>
									</form>
                                </div>
                            </div>
                        </div>
                    </div>
					
				
				</div>
            </div>
        </div>
		@endif
@endsection


	