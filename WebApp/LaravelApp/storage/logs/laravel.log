[2025-07-23 14:16:27] local.ERROR: SQLSTATE[HY000] [1049] Unknown database 'bookme_local' (SQL: select * from information_schema.tables where table_schema = bookme_local and table_name = smtp_settings and table_type = 'BASE TABLE') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'bookme_local' (SQL: select * from information_schema.tables where table_schema = bookme_local and table_name = smtp_settings and table_type = 'BASE TABLE') at /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connection.php:760)
[stacktrace]
#0 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#1 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connection.php(405): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#2 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connection.php(392): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#3 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(43): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#4 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(338): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('smtp_settings')
#5 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/app/Providers/MailConfigServiceProvider.php(17): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#6 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(712): App\\Providers\\MailConfigServiceProvider->register()
#7 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Foundation/ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(App\\Providers\\MailConfigServiceProvider))
#8 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(689): Illuminate\\Foundation\\ProviderRepository->load(Array)
#9 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#10 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(265): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#11 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#12 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#13 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 {main}

[previous exception] [object] (PDOException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'bookme_local' at /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:70)
[stacktrace]
#0 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(70): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', '', Array)
#2 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1181): call_user_func(Object(Closure))
#6 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connection.php(486): Illuminate\\Database\\Connection->getPdo()
#7 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from i...', Array)
#9 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select * from i...', Array, Object(Closure))
#10 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connection.php(405): Illuminate\\Database\\Connection->run('select * from i...', Array, Object(Closure))
#11 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Connection.php(392): Illuminate\\Database\\Connection->select('select * from i...', Array, false)
#12 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(43): Illuminate\\Database\\Connection->selectFromWriteConnection('select * from i...', Array)
#13 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(338): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('smtp_settings')
#14 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/app/Providers/MailConfigServiceProvider.php(17): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#15 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(712): App\\Providers\\MailConfigServiceProvider->register()
#16 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Foundation/ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(App\\Providers\\MailConfigServiceProvider))
#17 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(689): Illuminate\\Foundation\\ProviderRepository->load(Array)
#18 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#19 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(265): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#20 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(383): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#21 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(153): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#22 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}
"} 
[2025-07-23 15:45:27] local.ERROR: The "--router" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--router\" option does not exist. at /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/symfony/console/Input/ArgvInput.php:223)
[stacktrace]
#0 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/symfony/console/Input/ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('router', 'server-producti...')
#1 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/symfony/console/Input/ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--router=server...')
#2 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/symfony/console/Input/ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--router=server...', true)
#3 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/symfony/console/Input/Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/symfony/console/Command/Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Console/Command.php(152): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/symfony/console/Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\ServeCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Console/Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 {main}
"} 
[2025-07-23 15:55:57] local.INFO: Service2  
[2025-07-23 15:56:05] local.INFO: Service2  
[2025-07-23 16:13:28] local.INFO: Service2  
[2025-07-23 16:13:36] local.INFO: Service11  
[2025-07-23 16:15:16] local.INFO: Service3  
