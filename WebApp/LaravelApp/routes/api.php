<?php

use Illuminate\Http\Request;
use App\Http\Controllers\Api\UserController;
date_default_timezone_set("Asia/Kuwait");
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
	 
});
Route::group([
    'namespace' => 'Api',
    'middleware' => ['lang.switch']
], function () {
     Route::post('login_with_email', [UserController::class,'login_with_email']);
    Route::post('sociallogin','UserController@social_login')->name('sociallogin');
    Route::group(['middleware' => 'auth:sanctum'], function() {
    Route::post('/logout', 'UserController@logout')->name('apilogout');
    });  
    Route::post('resendotp','UserController@resendotp')->name('resendotp');
    
    Route::get('Services_Category', 'ServicesController@ServicesCategory');
    Route::post('services', 'ServicesController@services');
    Route::post('popularartists', 'ServicesController@popular_artist');
    Route::get('google_map','MapsetController@google_map');
    Route::get('timeslotfilter','TimeslotController@timeslotfilter');
    Route::post('searchvendor', 'VendorController@SearchVendor');
    Route::post('vendordetail', 'VendorController@VendorDetail');
    Route::get('promotionbanners','promotionBannerController@promotionBanner');
    Route::post('bookings','BookingController@bookings');
  	Route::post('COD','BookingController@COD');
    Route::post('rebookings','BookingController@rebookings');
    Route::post('bookingdetails','BookingController@bookingdetail');
    Route::post('downloadPDF','BookingController@downloadPDF');
    Route::post('upcomingbookings','BookingController@upcoming_orders');
    Route::post('completedbookings','BookingController@completed_orders');
    Route::post('cancelledbookings','BookingController@cancelled_orders');
    Route::post('cancellbooking','BookingController@orders_cancelled');
    Route::post('modifybooking','BookingController@orders_modify');
    Route::post('staffSelection','BookingController@staffSelection');
    Route::post('updateuserprofile','UserController@updateuserprofile');
    Route::get('autocompletion_cron','BookingController@autocompletion_cron');
    Route::post('viewuserprofile','UserController@viewuserprofile');
    Route::post('nearbysalons','VendorController@getnearbysalons');
    Route::post('vendorexplore','VendorController@vendorexplore');
    Route::post('filters','VendorController@filters');
    Route::post('mapsalons','VendorController@mapsalons');
    Route::post('filterservicecategories','VendorController@filterservicecategories');
    Route::post('RegisterUser','UserController@RegisterUser');
    Route::post('changePassword','UserController@changePassword');
    
    Route::get('privacypolicy','SettingsController@privacypolicy');
    Route::get('termsconditions','SettingsController@termsconditions');
    Route::get('faqs','SettingsController@faqs');
    Route::get('contactus','SettingsController@contactus');
    
    Route::get('sendsms','UserController@sendsms');
    Route::post('addAddress','UserController@addAddress');
    Route::post('editAddress','UserController@editAddress');
    Route::post('viewAddresses','UserController@viewAddresses');
    Route::post('deleteAddress','UserController@deleteAddress');
    Route::post('MakeDefaultAddress','UserController@MakeDefaultAddress');
    Route::post('DeleteAccount','UserController@DeleteAccount');
    Route::post('forgotPassword','UserController@forgotPassword');
    Route::post('verifyotp','UserController@verifyOtp');
    Route::post('verifyotpregistration','UserController@verifyotpregistration');
    Route::post('resendotpregistration','UserController@resendotpregistration');
    Route::post('addWishlist','VendorController@addWishlist');
    Route::post('viewWishlists','VendorController@viewWishlists');
    
    Route::post('transactionhistory','BookingController@transactionhistory');
    Route::post('vendortimeslot','BookingController@vendortimeslot');
    Route::post('stafftimeslot','BookingController@staff_getSlots');
    Route::post('staffAvailableSlots','BookingController@staff_AvailableSlots');
    Route::post('addToCart','BookingController@addToCart');
    Route::post('VendorServicesCategory','BookingController@VendorServicesCategory');
    Route::post('VendorServices','BookingController@VendorServices');
    Route::post('DeleteItem','BookingController@DeleteItemCart');
    Route::post('ViewCart','BookingController@ViewCart');
    Route::post('UpdateCart','BookingController@UpdateCart');
    Route::post('DeleteAllCart','BookingController@DeleteAllCart');
    Route::post('remindmeenable','BookingController@RemindMeEnableDisable');
    Route::post('add_salon_rating','VendorController@add_salon_rating');
    
    Route::get('areas','UserController@Arealist');
     Route::post('updatelanguage','UserController@updatelanguage');
    
    Route::get('splashscreens','SplashScreenController@SplashScreens');
    Route::post('coupon-code-apply','CouponCodeController@couponCodeStore');
    
    
    //notifications//
    Route::post('allnotifications', 'NotificationController@notificationlist');
    Route::post('read_by_user', 'NotificationController@read_by_user');
    Route::post('mark_all_read', 'NotificationController@mark_all_as_read');
    Route::post('delete_all_notifications', 'NotificationController@delete_all');
    Route::post('notificationsettingsRead', 'NotificationController@notificationsettingsRead');
    Route::post('notificationsettingsUpdate', 'NotificationController@notificationsettingsUpdate');
    
    //Payment Methods
	Route::post('AddCard','BookingController@AddCard');
	Route::get('AddCardResultPage','BookingController@AddCardResultPage')->name('AddCardResultPage');
	Route::post('ListAllCards','BookingController@ListAllCards')->name('ListAllCards');
	Route::get('ResultsPage','BookingController@ResultsPage')->name('resultpage');
	Route::post('Notificationpage','BookingController@Notificationpage')->name('notificationpage');
	
	
	//Reviews
	Route::post('add_likes_review','VendorController@add_likes_review');
	Route::post('add_report_reviews','VendorController@add_report_reviews');
	
	//Notification Settings
	Route::post('RemindernotificationRead','NotificationController@RemindernotificationRead')->name('RemindernotificationRead');
	Route::post('RemindernotificationUpdate','NotificationController@RemindernotificationUpdate')->name('RemindernotificationUpdate');
	
	//ReminderCron
	Route::get('reminder_cron','BookingController@reminder_cron')->name('reminder_cron');
});

   
   
	

