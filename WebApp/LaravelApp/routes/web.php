<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/adminlogin', 'AdminController@Login')->name('login');
Route::get('/expirynotification', 'AdminController@expirynotification')->name('expirynotification');
Route::get('/logout', 'AdminController@adminLogout')->name('logout');
Route::post('/checklogin', 'Admin<PERSON><PERSON>roller@checkadminLogin')->name('admin-login');
Route::get('/index', 'AdminController@adminDashboard')->name('adminDashboard');
Route::get('/smtp-settings', 'AdminController@smtpSettings')->name('smtp-settings');
Route::post('/smtp-save', 'AdminController@smtpSettings_save')->name('smtp-save');
Route::get('/governorate', 'AdminController@governorate')->name('governorate');
Route::post('/governorate-save', 'AdminController@governorate_save')->name('governorate-save');
Route::get('/governorate/edit/{id}','AdminController@Editgovernorate')->name('edit-governorate');
Route::post('/governorate/update/{id}','AdminController@Updategovernorate')->name('update-governorate');
Route::post('/governorate/delete/{id}','AdminController@Deletegovernorate')->name('delete-governorate');
Route::post('/governorate/enable/{id}','AdminController@Enablegovernorate')->name('enable-governorate');
Route::get('/add-admin-user','AdminController@AddAdminUser')->name('add-admin-user');
Route::post('/save-admin-user','AdminController@SaveAddAdminUser')->name('save-adminuser');
Route::get('/list-admin-users','AdminController@ListAdminUsers')->name('list-admin-users');
Route::get('/admin-user/edit/{id}','AdminController@EditAdminUser')->name('Edit-admin-user');
Route::post('/admin-user/update/{id}','AdminController@UpdateAdminUser')->name('update-admin-user');
Route::get('/admin-user/permission/{id}','AdminController@PermissionAdminUser')->name('Permission-admin-user');
Route::post('/admin-user/update-permission/{id}','AdminController@PermissionUpdateAdminUser')->name('permission-update-admin-user');
Route::get('/edit-profile','AdminController@EditProfile')->name('edit-profile');
Route::post('/save-edit-profile','AdminController@SaveEditProfile')->name('save-edit-profile');
Route::post('/uploadfiles','AdminController@Uploadfiles')->name('uploadfiles');
Route::get('/uploadedfiles','AdminController@ViewUploadfiles')->name('uploadedfiles');
Route::post('/files/delete','AdminController@ViewfileDelete')->name('filesdelete');

Route::get('/areas', 'AdminController@areas')->name('areas');
Route::post('/areas-save', 'AdminController@area_save')->name('area-save');
Route::get('/area/edit/{id}','AdminController@Editarea')->name('edit-area');
Route::post('/area/update/{id}','AdminController@Updatearea')->name('update-area');
Route::post('/area/delete/{id}','AdminController@Deletearea')->name('delete-area');
Route::post('/area/enable/{id}','AdminController@Enablearea')->name('enable-area');

Route::get('/amenetise','AdminController@amenetise')->name('amenetise');
Route::get('/listamenetise','AdminController@listamenetise')->name('listamenetise');
Route::post('/Saveamenetise','AdminController@Saveamenetise')->name('save-amenties');
Route::get('/amenetise/edit/{id}','AdminController@editamenetise')->name('edit-amenties');
Route::post('/amenetise/update/{id}','AdminController@editsaveamenties')->name('editsave-amenties');

Route::post('/delete-ameneties/{id}','AdminController@DeleteAmeneties')->name('delete-ameneties');

Route::get('/faqs', 'AdminController@faqs')->name('faqs');
Route::post('/faqsave', 'AdminController@faqSave')->name('faq-save');
Route::post('/faq/delete/{id}', 'AdminController@faqDelete')->name('faq-delete');
Route::get('/faq/edit/{id}','AdminController@FaqEdit')->name('edit-faq');
Route::post('/faq/update/{id}','AdminController@FaqEditSave')->name('FaqEditSave');


Route::get('/contact-us', 'AdminController@contactus')->name('contactus');
Route::post('/contact-save', 'AdminController@contactusSave')->name('contactus-save');

Route::get('/privacy-policy', 'AdminController@privacypolicy')->name('privacy-policy');
Route::post('/privacy-save', 'AdminController@privacypolicySave')->name('privacy-save');

Route::get('/terms', 'AdminController@terms')->name('terms');
Route::post('/terms-save', 'AdminController@termsSave')->name('terms-save');

Route::get('/general-settings', 'AdminController@generalSettings')->name('general-settings');
Route::post('/settings-save', 'AdminController@generalSettingsSave')->name('settings-save');

Route::get('/service-category', 'ServiceCategoryController@addServiceCategory')->name('service-category');
Route::post('/service-category-save', 'ServiceCategoryController@ServiceCategorySave')->name('service-categorysave');
Route::get('/service-categories', 'ServiceCategoryController@ServiceCategory')->name('service-categories');
Route::get('/service-category/edit/{id}','ServiceCategoryController@EditServiceCategory')->name('editservice-category');
Route::post('/service-category/update/{id}','ServiceCategoryController@UpdateServiceCategory')->name('update-ServiceCategory');
Route::post('/service-category/delete/{id}','ServiceCategoryController@DeleteServiceCategory')->name('delete-ServiceCategory');


Route::get('/services-list', 'ServicesController@Services')->name('services-list');
Route::get('/add-service', 'ServicesController@addService')->name('add-service');
Route::post('/service-save', 'ServicesController@ServiceSave')->name('service-save');
Route::get('/services/edit/{id}','ServicesController@EditService')->name('editservice');
Route::post('/services/update/{id}','ServicesController@UpdateService')->name('update-Service');
Route::post('/services/delete/{id}','ServicesController@DeleteService')->name('delete-Service');

Route::get('/customer-list', 'CustomerController@CustomerList')->name('customer-list');
Route::get('/customer-list/profile/{id}','CustomerController@CustomerProfile')->name('customer-profile');
Route::post('/customer/enable/{id}','CustomerController@Enablecustomer')->name('enable-customer');
Route::get('/send-email', 'SendEmailController@SendEmail')->name('send-email');
Route::post('/send-email-send', 'SendEmailController@SendEmailSend')->name('send-email-send');

Route::get('/send-sms', 'SendSmsController@SendSms')->name('send-sms');
Route::post('/send-sms-send', 'SendSmsController@SendSmsSend')->name('send-sms-send');

Route::get('/promotionbanners', 'PromotionBannerController@PromotionBannerList')->name('promotionbanners');
Route::get('/promotionbanners/edit/{id}', 'PromotionBannerController@PromotionBannerEdit')->name('promotionbanneredit');
Route::get('/promotionbannersadd', 'PromotionBannerController@PromotionBannerAdd')->name('promotionbannersadd');
Route::post('/promotionbannerssave', 'PromotionBannerController@SavePromotionBanner')->name('promotionbannerssave');
Route::post('/promotionbanner/update/{id}','PromotionBannerController@PromotionBannerUpdate')->name('promotionbannersupdate');
Route::post('/promotionbanner/delete/{id}','PromotionBannerController@DeletePromoBanner')->name('promotionbannersdelete');
Route::post('/promotionbanner/enable/{id}','PromotionBannerController@EnablePromoBanner')->name('promotionbannersenable');

Route::get('/vendor/{vendor_id}', 'VendorController@metaPage');
Route::get('/add-vendor', 'VendorController@addVendor')->name('add-vendor');
Route::post('/save-vendor', 'VendorController@saveVendor')->name('save-vendor');
Route::get('/list-vendors', 'VendorController@listVendors')->name('list-vendors');
Route::get('/PopularArtist', 'VendorController@PopularArtist')->name('PopularArtist');
Route::get('/PopularArtistEdit/{id}', 'VendorController@PopularArtistEdit')->name('PopularArtistEdit');
Route::post('/PopularArtistEditUpdate/{id}','VendorController@PopularArtistEditUpdate')->name('PopularArtistEditUpdate');
Route::get('/vendors/edit/{id}','VendorController@EditVendor')->name('editvendor');
Route::post('/vendors/update/{id}','VendorController@saveEditVendor')->name('update-vendor');
Route::post('/vendor/deletephoto/{id}','VendorController@DeletePhoto')->name('delete-photo');
Route::post('/vendor/deleteattachment/{id}','VendorController@DeleteAttachment')->name('delete-attachment');
Route::post('/vendor/enable/{id}','VendorController@EnableVendor')->name('enable-vendor');
Route::post('/PopularArtistAjax/{id}','VendorController@PopularArtistAjax')->name('PopularArtistAjax');
Route::get('/vendor/view/{id}','VendorController@ViewVendor')->name('viewvendor');
Route::post('/vendor/contractupdate','VendorController@EditVendorContract')->name('editvendorcontract');
Route::get('/vendor/login/{id}','VendorController@vendorsecretlogin')->name('vendorsecretlogin');
Route::get('/vendor/permission/{id}','VendorController@vendorpermission')->name('vendorpermission');
Route::post('/vendor/update-permission/{id}','VendorController@PermissionUpdateVendor')->name('PermissionUpdateVendor');
Route::get('/vendor/adminmanage/{id}','VendorController@vendoradminmanage')->name('vendoradminmanage');
Route::post('/vendor/update-commision/{id}','VendorController@UpdateVendorCommision')->name('UpdateVendorCommision');

Route::get('/booking-list','BookingController@BookingList')->name('booking-list');
Route::get('/booking-list-detail/{id}','BookingController@BookingListDetails')->name('view-bookdetails');
Route::post('/booking/status/{id}','BookingController@BookingStatus')->name('booking-status');
Route::get('/booking-listcancelled','BookingController@BookingCancelled')->name('booking-listcancelled');
Route::get('/download-pdf/{bookingId}', 'BookingController@downloadPDF')->name('downloadPDF');

Route::get('/sale-report','ReportsController@SaleReport')->name('sale-report');
Route::get('/vendor-report','ReportsController@VendorReport')->name('vendor-report');
Route::get('/user-report','ReportsController@UserReport')->name('user-report');
Route::any('/getAjaxUserReport','ReportsController@getAjaxUserReport')->name('getAjaxUserReport');


Route::get('/add-coupon','CouponController@AddCoupon')->name('add-coupon');
Route::post('/save-coupon', 'CouponController@SaveCoupon')->name('save-coupon');
Route::get('/list-coupon','CouponController@ListCoupon')->name('list-coupon');
Route::get('/edit-coupon/{id}','CouponController@EditCoupon')->name('edit-coupon');
Route::post('/update-coupon/{id}','CouponController@UpdateCoupon')->name('update-coupon');
Route::post('/delete-coupon/{id}','CouponController@DeleteCoupon')->name('delete-coupon');
Route::post('/coupon/enable/{id}','CouponController@EnableCoupon')->name('enable-coupon');

Route::get('/send-notification','NotificationController@adminNotification')->name('send-notification');
Route::post('/adminNotificationSend','NotificationController@adminNotificationSend')->name('adminNotificationSend');




Route::get('/vendor-login', 'VendorPanelController@Login')->name('vendor-login');
Route::get('/vendor-logout', 'VendorPanelController@VendorLogout')->name('vendor-logout');
Route::post('/vendor-logincheck', 'VendorPanelController@checkadminLogin')->name('vendor-logincheck');
Route::get('/vendor-dashboard', 'VendorPanelController@vendorDashboard')->name('vendorDashboard');
Route::get('/vendor-basicdetails', 'VendorPanelController@vendorBasic')->name('vendor-basicdetails');
Route::get('/vendor-cancel', 'VendorPanelController@vendorCancel')->name('vendor-cancel');
Route::post('/vendor-cancelsave', 'VendorPanelController@vendorCancelSave')->name('vendor-cancelsave');
Route::post('/vendor-basicdetailssave', 'VendorPanelController@vendorBasicSave')->name('vendor-basicdetailssave');
Route::get('/vendor-refund', 'VendorPanelController@vendorRefund')->name('vendor-refund');

Route::get('/vendor-booking', 'VendorBookingController@vendorBooking')->name('vendor-booking');
Route::get('/vendor-breaktime', 'VendorBookingController@vendorBreaktime')->name('vendor-breaktime');
Route::get('/vendor-bookingdetail/{id}', 'VendorBookingController@ViewVendorBooking')->name('vendor-bookingdetail');
Route::post('/vendor/staff-update/{id}', 'VendorBookingController@ChangeStaff')->name('vendor-staff-update');
Route::post('/vendor/bookstatus-update/{id}', 'VendorBookingController@ChangeStatus')->name('vendor-status-update');
Route::post('/booking/slots/{id}', 'VendorBookingController@Showtimeslots')->name('vendor-slots');
Route::post('/booking-add-save', 'VendorBookingController@SaveBookingSlot')->name('vendor-booking-save');
Route::get('/notifications-list', 'VendorBookingController@fetch')->name('notifications.fetch');
Route::post('/notifications-list/read', 'VendorBookingController@markAsRead')->name('notifications.read');

Route::get('/vendor-clients', 'VendorClientController@vendorBooking')->name('vendor-clients');
Route::get('/vendor-customer-profile/{id}', 'VendorClientController@customerprofile')->name('vendor-customer-profile');
Route::post('/save-customer', 'VendorClientController@SaveCustomer')->name('save-customer');


Route::get('/vendor-services', 'VendorServiceController@vendorServices')->name('vendor-services');
Route::post('/vendor-services-save', 'VendorServiceController@vendorServicesSave')->name('vendor-services-save');
Route::get('/editservices-vendor/{id}', 'VendorServiceController@vendorServicesEdit')->name('editservices-vendor');
Route::post('/editservices-vendor-update/{id}', 'VendorServiceController@vendorServicesUpdate')->name('editservices-vendor-update');
Route::post('/service/enable/{id}','VendorServiceController@EnableServices')->name('enable-services-vendor');
Route::post('/service-vendor/delete/{id}','VendorServiceController@DeleteServices')->name('delete-services-vendor');


Route::get('/vendor-servicescategories', 'VendorServiceCategoryController@vendorServicesCategories')->name('vendor-servicescategories');
Route::get('/editservicescategory-vendor/{id}', 'VendorServiceCategoryController@vendorServicesEdit')->name('editservicescategory-vendor');
Route::post('/editservicescategory-vendor-update/{id}', 'VendorServiceCategoryController@vendorServicesCategoryUpdate')->name('editservicescategory-vendor-update');
Route::post('/servicecategory-vendor/delete/{id}','VendorServiceCategoryController@DeleteServicesCategory')->name('delete-services-category-vendor');
Route::post('/vendor-services-category-save', 'VendorServiceCategoryController@vendorServicesCategorySave')->name('vendor-services-category-save');
Route::post('/servicecategory/enable/{id}','VendorServiceCategoryController@EnableServicesCategory')->name('enable-services-category-vendor');

Route::get('/vendor-emplyees', 'VendorEmployeeController@VendorEmployeeList')->name('vendor-emplyees');
Route::get('/vendor-addemplyee', 'VendorEmployeeController@AddVendorEmployee')->name('vendor-addemplyee');
Route::post('/vendor-saveemployee', 'VendorEmployeeController@SaveVendorEmployee')->name('vendor-saveemployee');
Route::get('/vendor-editemployee/{id}', 'VendorEmployeeController@EditVendorEmployee')->name('vendor-editemployee');
Route::post('/vendor-updateemplyee/{id}', 'VendorEmployeeController@UpdateVendorEmployee')->name('vendor-updateemplyee');
Route::post('/employee/enable/{id}', 'VendorEmployeeController@EnableVendorEmployee')->name('vendor-Enableemployee');
Route::get('/vendor-viewemployee/{id}', 'VendorEmployeeController@VendorEmployeeProfile')->name('vendor-viewemployee');

Route::get('/vendor-reviews', 'VendorReviewController@ViewVendorReviews')->name('vendor-reviews');
Route::post('/vendor-comment', 'VendorReviewController@CommentVendorReview')->name('vendor-comments');
Route::post('/vendor/review-update/{id}', 'VendorReviewController@EnableVendorReview')->name('vendor-Enablereview');
Route::post('/ReportVendorReview/{id}', 'VendorReviewController@ReportVendorReview')->name('ReportVendorReview');

Route::get('/vendor-logo', 'VendorPanelController@vendorLogo')->name('vendor-logo');
Route::post('/vendor-photo-delete/{id}', 'VendorPanelController@vendorDeletephoto')->name('vendor-photo-delete');
Route::post('/vendor-photo-update', 'VendorPanelController@vendorLogoUpdate')->name('vendor-photo-update');
Route::get('/vendor-timings', 'VendorPanelController@vendorTimings')->name('vendor-timings');
Route::post('/vendor-time-update', 'VendorPanelController@vendorTimeUpdate')->name('vendor-time-update');
Route::get('/vendor-address', 'VendorPanelController@vendorAddress')->name('vendor-address');
Route::post('/vendor-update-address', 'VendorPanelController@vendorAddressUpdate')->name('vendor-update-address');
Route::get('/vendor-calendar', 'VendorCalendarController@Calendar')->name('vendor-calendar');


Route::get('/vendor-changeaddress', 'VendorPanelController@vendorChangeaddress')->name('vendor-changeaddress');
Route::post('/vendor-update-changepassword', 'VendorPanelController@vendorchangepassword')->name('vendor-update-changepassword');


Route::get('/staff-login', 'VendorStaffController@Login')->name('staff-login');
Route::post('/staff-logincheck', 'VendorStaffController@checkstaffLogin')->name('staff-logincheck');
Route::get('/staff-dashboard', 'VendorStaffController@staffDashboard')->name('staffDashboard');
Route::post('/staff-savecustomer', 'VendorStaffController@SaveCustomer')->name('staff-savecustomer');
Route::post('/staff-booking-add-save', 'VendorStaffController@SaveBookingSlot')->name('staff-booking-save');
Route::get('/staff-calendar', 'VendorStaffController@Calendar')->name('staff-calendar');
Route::get('/staff-logout', 'VendorStaffController@StaffLogout')->name('staff-logout');


Route::get('/staff-booking', 'VendorStaffController@vendorBooking')->name('staff-booking');
Route::get('/staff-bookingdetail/{id}', 'VendorStaffController@ViewVendorBooking')->name('staff-bookingdetail');
Route::post('/staff/staff-update/{id}', 'VendorStaffController@ChangeStaff')->name('staff-staff-update');
Route::post('/staff/bookstatus-update/{id}', 'VendorStaffController@ChangeStatus')->name('staff-status-update');
Route::post('/staff/slots/{id}', 'VendorBookingController@Showtimeslots')->name('staff-slots');
//Route::post('/staff-add-save', 'VendorBookingController@SaveBookingSlot')->name('staff-booking-save');
Route::get('/staff-profile', 'VendorStaffController@Profile')->name('staff-profile');
Route::get('/staff-changeaddress', 'VendorStaffController@staffChangeaddress')->name('staff-changeaddress');
Route::post('/staff-update-changepassword', 'VendorStaffController@staffchangepassword')->name('staff-update-changepassword');

Route::get('/add-splash-screen', 'SplashScreenController@AddSplashScreen')->name('add-splash-screen');
Route::post('/save-splash-screen', 'SplashScreenController@SaveSplashScreen')->name('splash-save');
Route::get('/edit-splash-screen/{id}', 'SplashScreenController@EditSplashScreen')->name('edit-splash-screen');
Route::post('/update-splash-screen/{id}', 'SplashScreenController@UpdateSplashScreen')->name('splash-update');
Route::post('/splash/delete/{id}','SplashScreenController@DeleteSplash')->name('delete-splash');
Route::post('/splash/enable/{id}','SplashScreenController@EnableSplash')->name('enable-splash');
Route::post('/stafftime/slots/{id}', 'VendorStaffController@Showtimeslots')->name('stafftime-slots');
Route::post('/stafftime/slots1/{id}', 'VendorStaffController@Showtimeslots1')->name('stafftime-slots1');