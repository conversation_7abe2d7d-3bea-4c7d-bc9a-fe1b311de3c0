<?php

/**
 * <PERSON>vel Development Server Router with Production-Style URL Support
 * 
 * This custom server router enables `php artisan serve` to handle production-style URLs
 * with /public/ prefix while maintaining full Laravel functionality.
 * 
 * When <PERSON>vel's serve command runs, it automatically uses this file instead of
 * the default framework server.php, enabling seamless production URL simulation.
 * 
 * Supported URL patterns:
 * - /public/uploads/file.png (production-style)
 * - /public/images/logo.png (production-style)
 * - /public/css/style.css (production-style)
 * - /adminlogin (Laravel routes)
 * - / (Laravel routes)
 */

// Get the current working directory (this will be the public directory when run via artisan serve)
$publicPath = getcwd();

$uri = urldecode(
    parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH) ?? ''
);

// Handle production-style URLs with /public/ prefix (e.g., /public/uploads/file.png)
if ($uri !== '/' && strpos($uri, '/public/') === 0) {
    // Remove '/public/' prefix (8 characters) to get the actual file path
    $filePath = substr($uri, 8);
    $fullPath = $publicPath . '/' . $filePath;

    // Check if the file exists and is actually a file (not a directory)
    if (file_exists($fullPath) && is_file($fullPath)) {
        // Serve the file directly with proper headers
        $mimeType = mime_content_type($fullPath);
        header('Content-Type: ' . $mimeType);
        header('Content-Length: ' . filesize($fullPath));
        readfile($fullPath);
        exit;
    }
}

// Handle standard static file requests (for backward compatibility)
// This maintains support for direct file access without /public/ prefix
if ($uri !== '/' && file_exists($publicPath . $uri) && is_file($publicPath . $uri)) {
    return false;
}

// For all other requests (Laravel routes, API endpoints, etc.),
// forward to Laravel's main entry point
require_once $publicPath . '/index.php';
