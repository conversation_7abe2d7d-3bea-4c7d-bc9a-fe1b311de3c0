APP_NAME='BookMe'
APP_ENV=local
APP_KEY=base64:Sb4M9om7SdtsCbPgerRpdGHCeK5WJxh3g1RWfMUUuh0=
APP_DEBUG=true
APP_URL=https://app.appbookme.com/public

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=appbookme-database49
DB_USERNAME=appbookme-user49
DB_PASSWORD="wcj6PZu59ongVyIq21Qe#@38"

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="znfepwjpfguaseno"
MAIL_ENCRYPTION="tls"
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="BookMe"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

JWT_SECRET=hGcy5ddpG4BNMOU7kJD6uGwSWNHD8E19YY2hWx31nnz8ZY2lB9gXmyU4ZKEGRLAP
