---
type: "manual"
---

# BookMe Production Deployment & Management Guide

## 🎯 Overview

This document provides comprehensive guidelines for production deployment and management of the BookMe Laravel application. This guide covers safe deployment procedures, production monitoring, and emergency protocols for the live production environment.

⚠️ **CRITICAL WARNING**: This guide deals with the LIVE PRODUCTION environment. All changes affect real users and live data. Always follow safety protocols.

## 🏭 Production Environment Details

### Production Server Configuration ✅ VERIFIED
- **Production URL**: `https://app.appbookme.com/public`
- **Environment**: `local` (configured as local but running in production)
- **Server IP**: `*************`
- **SSH Port**: `22` (Standard SSH port)
- **Username**: `developer-chrisans`
- **SSH Key**: `~/.ssh/cloudpanel_key`
- **Document Root**: `/home/<USER>/htdocs/app.appbookme.com/public/`
- **Server Status**: ✅ Online, AWS Ubuntu 22.04 LTS
- **System**: Linux ip-172-26-5-85 6.8.0-1028-aws x86_64
- **Memory**: 7.6GB total, 6.1GB available
- **Disk**: 155GB total, 146GB available (7% used)

### Application Configuration
- **Laravel Version**: 9.52.16
- **PHP Version**: 8.4.7
- **Composer Version**: 2.8.9
- **Node.js**: Not installed
- **NPM**: Not installed

### Production Database Details
- **Database**: `appbookme-database49`
- **Username**: `appbookme-user49`
- **Password**: `wcj6PZu59ongVyIq21Qe#@38`
- **Host**: `127.0.0.1`
- **Port**: `3306`

### Production Directory Structure
```
/home/<USER>/htdocs/app.appbookme.com/public/
├── app/                    # Laravel application logic
│   ├── Console/           # Artisan commands
│   ├── Exceptions/        # Exception handlers
│   ├── Helpers/           # Helper classes
│   ├── Http/              # Controllers, middleware
│   │   ├── Controllers/   # Application controllers
│   │   │   └── Api/      # API controllers
│   │   └── Middleware/   # HTTP middleware
│   ├── Mail/              # Mail classes
│   ├── Models/            # Eloquent models
│   ├── Providers/         # Service providers
│   ├── Services/          # Business logic services
│   └── Traits/            # Reusable traits
├── bootstrap/             # Application bootstrap
├── config/                # Configuration files
├── database/              # Database migrations & seeders
│   ├── factories/         # Model factories
│   ├── migrations/        # Database migrations
│   └── seeders/           # Database seeders
├── lang/                  # Language files
├── public/                # Public web assets
├── resources/             # Views, assets, language files
│   ├── css/              # CSS source files
│   ├── js/               # JavaScript source files
│   ├── lang/             # Localization files
│   └── views/            # Blade templates
├── routes/                # Route definitions
├── storage/               # File storage & logs
│   ├── app/              # Application files
│   ├── firebase/         # Firebase configuration
│   ├── framework/        # Framework cache/sessions
│   └── logs/             # Application logs
├── tests/                 # Application tests
├── vendor/                # Composer dependencies
├── .env                   # Environment configuration
├── artisan               # Laravel command-line interface
├── composer.json         # Composer dependencies
└── package.json          # NPM dependencies
```

## 🔐 Production Access & Security

### SSH Connection Requirements ✅ TESTED
- **SSH Key**: Uses `~/.ssh/cloudpanel_key` for authentication
- **CRITICAL**: Must include `-o StrictHostKeyChecking=no` for automated connections
- Host key has been verified and added to known_hosts

### Basic SSH Command Structure
```bash
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "[COMMAND]"
```

### Essential Production SSH Commands
```bash
# Test connection
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "pwd"

# Navigate to Laravel app
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && ls -la"

# Check server status
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "df -h && date"

# Check Laravel application status
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan --version"

# Check database connection
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan tinker --execute='DB::connection()->getPdo(); echo \"Database connected successfully\";'"
```

### Site-Scoped SSH/SFTP User (CloudPanel)

Use this when you need **restricted site-only** access (no server-wide privileges). This is separate from the admin user `developer-chrisans`.

**User details**
- **Username**: `appbookme-ssh`
- **Host/IP**: `*************`
- **Port**: `22`
- **Home/Document Root after login**: `/home/<USER>/htdocs/www.appbookme.com/`

**Generate a dedicated key pair (local machine)**
```bash
ssh-keygen -t ed25519 -C "<EMAIL>" -f ~/.ssh/appbookme_ed25519
# Private: ~/.ssh/appbookme_ed25519
# Public : ~/.ssh/appbookme_ed25519.pub
chmod 700 ~/.ssh
chmod 600 ~/.ssh/appbookme_ed25519
chmod 644 ~/.ssh/appbookme_ed25519.pub
```

**Add the public key in CloudPanel**
- Go to **Site → SSH/FTP → New SSH User**.
- Set **User Name** to `appbookme-ssh`.
- Paste the contents of `~/.ssh/appbookme_ed25519.pub` into **SSH Keys**.
- Click **Add User**.

**SSH login (site-scoped user)**
```bash
ssh -i ~/.ssh/appbookme_ed25519 -p 22 appbookme-ssh@*************
```

**Optional SSH config for short alias**
Create or edit `~/.ssh/config` and add:
```
Host appbookme-site
    HostName *************
    User appbookme-ssh
    Port 22
    IdentityFile ~/.ssh/appbookme_ed25519
```
Then connect with:
```bash
ssh appbookme-site
```

**SFTP (if needed)**
```bash
# Interactive SFTP session (uses the same key)
sftp -P 22 -i ~/.ssh/appbookme_ed25519 appbookme-ssh@*************
# Typical working path after login:
# /home/<USER>/htdocs/www.appbookme.com/
```

> 🔎 **Which user to use?**
> - Use **`developer-chrisans`** + `~/.ssh/cloudpanel_key` for **admin/server-level** tasks documented above.
> - Use **`appbookme-ssh`** + `~/.ssh/appbookme_ed25519` for **site-only** uploads or maintenance within the site chroot.

## 🚀 Production Deployment Workflow

### 🔴 CRITICAL: Pre-Deployment Safety Protocol

#### MANDATORY Steps Before ANY Production Deployment:
1. **✅ Test Locally**: All changes MUST be tested in local environment first
2. **✅ Version Control**: Commit all changes to Git with proper documentation
3. **✅ GitHub Backup**: Push commits to GitHub for backup and collaboration
4. **✅ Backup Production**: Create full backup of production files and database
5. **✅ Maintenance Mode**: Put application in maintenance mode during deployment
6. **✅ Verify Changes**: Double-check all files and changes
7. **✅ Plan Rollback**: Have rollback strategy ready

### Git-Based Version Control for Production 📚

#### Essential Git Workflow for Production Deployment
```bash
# 1. Ensure all changes are committed locally
cd /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp
git status

# 2. Add and commit any final changes
git add .
git commit -m "Production deployment: [describe changes]"

# 3. Push to GitHub for backup
git push origin main

# 4. Tag the release (recommended for production)
git tag -a v1.0.1 -m "Production release v1.0.1"
git push origin v1.0.1
```

#### Production .gitignore Requirements
Ensure production deployments exclude sensitive files:
```bash
# Environment files (CRITICAL)
.env
.env.*

# Dependencies (exclude from deployment)
/node_modules
/vendor

# Development files
.vscode/
.idea/
*.log

# Laravel specific
/storage/logs/*
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*
/bootstrap/cache/*

# Build and temporary files
/public/hot
/public/storage
npm-debug.log*
```

### GitHub Integration for Production 🌐

#### Production-Safe GitHub Workflow
```bash
# Create production branch for stable releases
git checkout -b production
git push origin production

# For hotfixes, create dedicated branches
git checkout -b hotfix/critical-bug-fix
git commit -m "Hotfix: Resolve critical production issue"
git push origin hotfix/critical-bug-fix

# Merge to production after testing
git checkout production
git merge hotfix/critical-bug-fix
git push origin production
```

#### GitHub Actions for Automated Deployment (Optional)
- **Automated Testing**: Run tests before deployment
- **SSH Key Management**: Store SSH private key in GitHub Secrets
- **Automated rsync**: Trigger deployment on main branch push
- **Rollback Capabilities**: Quick revert to previous versions

### Combined Deployment Strategy: Git → GitHub → rsync 🔄

#### Recommended Three-Step Production Deployment
1. **Git for Versioning**: Track all file changes and maintain deployment history
2. **GitHub for Collaboration**: Remote backup and team coordination
3. **rsync for Deployment**: Efficient, selective file transfer to production

#### Complete Production Deployment Workflow
```bash
# Step 1: Local Development and Testing
# (All changes tested in local environment first)

# Step 2: Version Control
git add .
git commit -m "Production: Implement new feature with security improvements"
git push origin main

# Step 3: Create Production Backup
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs && tar -czf backup_$(date +%Y%m%d_%H%M%S).tar.gz app.appbookme.com/"

# Step 4: Put Application in Maintenance Mode
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan down"

# Step 5: Deploy using lftp (TEST FIRST with --dry-run)
lftp -e "set sftp:connect-program 'ssh -a -x -i ~/.ssh/cloudpanel_key -p 22'; cd /home/<USER>/htdocs/app.appbookme.com/public; mirror -R --dry-run --parallel=4 --use-pget-n=8 --verbose --continue --only-newer --exclude=vendor --exclude=node_modules --exclude=storage/logs /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp .; quit" -u developer-chrisans sftp://*************

# Step 6: Actual deployment (after dry-run verification)
lftp -e "set sftp:connect-program 'ssh -a -x -i ~/.ssh/cloudpanel_key -p 22'; cd /home/<USER>/htdocs/app.appbookme.com/public; mirror -R --parallel=4 --use-pget-n=8 --verbose --continue --only-newer --exclude=vendor --exclude=node_modules --exclude=storage/logs /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp .; quit" -u developer-chrisans sftp://*************

# Step 7: Run production optimizations
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan cache:clear && php artisan config:cache && php artisan route:cache"

# Step 8: Bring application back online
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan up"

# Step 9: Verify deployment
curl -I https://app.appbookme.com/public
```

### rsync Production Deployment Strategy 🚀

#### Production-Optimized lftp Command
```bash
# ALWAYS test first with --dry-run (MANDATORY)
lftp -e "set sftp:connect-program 'ssh -a -x -i ~/.ssh/cloudpanel_key -p 22'; cd /home/<USER>/htdocs/app.appbookme.com/public; mirror -R --dry-run --parallel=4 --use-pget-n=8 --verbose --continue --only-newer --exclude=vendor --exclude=node_modules --exclude=storage/logs --exclude=.git --exclude=.env /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp .; quit" -u developer-chrisans sftp://*************

# Production deployment (after successful dry-run)
lftp -e "set sftp:connect-program 'ssh -a -x -i ~/.ssh/cloudpanel_key -p 22'; cd /home/<USER>/htdocs/app.appbookme.com/public; mirror -R --parallel=4 --use-pget-n=8 --verbose --continue --only-newer --exclude=vendor --exclude=node_modules --exclude=storage/logs --exclude=.git --exclude=.env /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp .; quit" -u developer-chrisans sftp://*************
```

#### Production rsync Benefits
- ✅ **Minimal Transfer**: Only changed files uploaded (bandwidth efficient)
- ✅ **Atomic Operations**: Reliable file synchronization
- ✅ **Progress Monitoring**: Real-time transfer status
- ✅ **Selective Exclusion**: Automatic protection of sensitive files
- ✅ **Rollback Ready**: Combined with Git for easy version management

#### Critical rsync Safety Measures
```bash
# ALWAYS use --dry-run first (MANDATORY)
rsync --dry-run [all-options] [source] [destination]

# Monitor transfer progress
rsync --progress [all-options] [source] [destination]

# Verify exclusions are working
rsync --dry-run --verbose [all-options] [source] [destination] | grep -E "\.env|node_modules|\.git"
```

### Production Backup Procedures

#### 1. Create Production Database Backup
```bash
# Create database backup with timestamp
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && mysqldump -h 127.0.0.1 -u appbookme-user49 -p'wcj6PZu59ongVyIq21Qe#@38' appbookme-database49 > /tmp/backup_db_$(date +%Y%m%d_%H%M%S).sql"

# Verify backup was created
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "ls -la /tmp/backup_db_*.sql | tail -1"
```

#### 2. Create Production Files Backup
```bash
# Create full application backup
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs && tar -czf backup_files_$(date +%Y%m%d_%H%M%S).tar.gz app.appbookme.com/"

# Verify backup was created
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "ls -la /home/<USER>/htdocs/backup_files_*.tar.gz | tail -1"
```

### Production Deployment Methods

#### Method 1: Single File Deployment (Recommended for Small Changes)
```bash
# 1. Put application in maintenance mode
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan down"

# 2. Deploy single file
scp -i ~/.ssh/cloudpanel_key -P 22 -o StrictHostKeyChecking=no [LOCAL_FILE] developer-chrisans@*************:/home/<USER>/htdocs/app.appbookme.com/public/[REMOTE_PATH]

# 3. Clear caches
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan cache:clear && php artisan config:clear && php artisan view:clear"

# 4. Bring application back online
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan up"

# 5. Verify deployment
curl -I https://app.appbookme.com/public
```

#### Method 2: Multiple Files Deployment (Use with Extreme Caution)
```bash
# 1. Put application in maintenance mode
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan down"

# 2. Deploy using lftp (EXCLUDES CRITICAL FILES)
lftp -e "set sftp:connect-program 'ssh -a -x -i ~/.ssh/cloudpanel_key -p 22'; cd /home/<USER>/htdocs/app.appbookme.com/public; mirror -R --parallel=4 --use-pget-n=8 --verbose --continue --only-newer --exclude=vendor --exclude=node_modules --exclude=storage/logs --exclude=.git --exclude=.env /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp .; quit" -u developer-chrisans sftp://*************

# 3. Run production optimizations
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && composer install --no-dev --optimize-autoloader && php artisan cache:clear && php artisan config:clear && php artisan view:clear && php artisan route:cache && php artisan config:cache"

# 4. Bring application back online
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan up"

# 5. Verify deployment
curl -I https://app.appbookme.com/public
```

## 📊 Production Database Management

### Database Operations (EXTREME CAUTION REQUIRED)

#### Safe Database Backup
```bash
# Create timestamped backup
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && mysqldump -h 127.0.0.1 -u appbookme-user49 -p'wcj6PZu59ongVyIq21Qe#@38' appbookme-database49 > /tmp/backup_$(date +%Y%m%d_%H%M%S).sql"
```

#### Database Migration (PRODUCTION)
```bash
# ⚠️ DANGER: Only run after thorough testing
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan migrate --force"
```

### Production Cache Management
```bash
# Clear all caches (safe operation)
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan cache:clear && php artisan config:clear && php artisan route:clear && php artisan view:clear"

# Optimize for production (after deployment)
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan route:cache && php artisan config:cache && php artisan view:cache"
```

## 🚨 Emergency Procedures

### Emergency Rollback Procedures

#### 1. Quick File Rollback
```bash
# Restore from latest backup
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs && tar -xzf backup_files_[TIMESTAMP].tar.gz"
```

#### 2. Database Rollback
```bash
# ⚠️ EXTREME DANGER: This will overwrite all current data
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && mysql -h 127.0.0.1 -u appbookme-user49 -p'wcj6PZu59ongVyIq21Qe#@38' appbookme-database49 < /tmp/backup_db_[TIMESTAMP].sql"
```

#### 3. Emergency Maintenance Mode
```bash
# Put site in maintenance mode immediately
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan down --message='Emergency maintenance in progress' --retry=60"
```

## 📈 Production Monitoring

### Health Check Commands
```bash
# Check application status
curl -I https://app.appbookme.com/public

# Check server resources
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "df -h && free -h && uptime"

# Check Laravel logs
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && tail -20 storage/logs/laravel.log"

# Check database connectivity
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan migrate:status"
```

### Performance Monitoring
```bash
# Check response time
time curl -s https://app.appbookme.com/public > /dev/null

# Monitor database size
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "mysql -h 127.0.0.1 -u appbookme-user49 -p'wcj6PZu59ongVyIq21Qe#@38' -e \"SELECT table_schema AS 'Database', ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)' FROM information_schema.tables WHERE table_schema = 'appbookme-database49';\""
```

## 🔒 Environment File Management

### Why `.env` Should Not Be Synced

Environment configuration files like `.env` contain sensitive and environment-specific data (e.g., DB credentials, API keys). These should **not be committed to Git** or transferred using `rsync`, especially from local to production environments.

### Best Practices

1. **Ignore .env in Git**
   Ensure `.env` is listed in `.gitignore`:
   ```bash
   # .gitignore
   .env
   .env.*
   ```

2. **Maintain Separate Files**
   - Local: `./.env`
   - Production: `/home/<USER>/htdocs/app.appbookme.com/public/.env`

   These should be manually configured and kept separate.

3. **Exclude .env from lftp**
   When deploying via lftp, exclude `.env`:
   ```bash
   lftp -e "set sftp:connect-program 'ssh -a -x -i ~/.ssh/cloudpanel_key -p 22'; cd /home/<USER>/htdocs/app.appbookme.com/public; mirror -R --exclude=.env --exclude=.git --exclude=node_modules /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp .; quit" -u developer-chrisans sftp://*************
   ```

4. **Manage Production .env Securely**
   Edit `.env` directly via SSH:
   ```bash
   ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@*************
   nano /home/<USER>/htdocs/app.appbookme.com/public/.env
   ```

   Or upload cautiously using SCP:
   ```bash
   scp -i ~/.ssh/cloudpanel_key -P 22 -o StrictHostKeyChecking=no .env.production developer-chrisans@*************:/home/<USER>/htdocs/app.appbookme.com/public/.env
   ```

5. **Use Example Files for Reference**
   Keep a `.env.production.example` in the repo to document required keys (but no secrets).

### Summary
- 🔒 `.env` files must be protected from versioning and unintended transfer
- 🔁 Keep `.env` files manually managed per environment
- ✅ Use `.env.example` to guide configuration without exposing secrets

## ⚠️ CRITICAL Production Warnings

### NEVER DO IN PRODUCTION:
- ❌ **Never deploy untested changes**
- ❌ **Never modify database directly without backup**
- ❌ **Never run migrations without testing**
- ❌ **Never deploy during peak hours**
- ❌ **Never skip backup procedures**
- ❌ **Never use debug mode in production**
- ❌ **Never expose sensitive credentials**

### ALWAYS DO IN PRODUCTION:
- ✅ **Always backup before changes**
- ✅ **Always test in local environment first**
- ✅ **Always use maintenance mode during deployment**
- ✅ **Always verify deployment success**
- ✅ **Always monitor after deployment**
- ✅ **Always have rollback plan ready**
- ✅ **Always document changes made**

## 🛠️ Production Maintenance Workflow

### Daily Monitoring Tasks
```bash
# Check application health
curl -I https://app.appbookme.com/public

# Monitor server resources
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "df -h | head -5 && uptime"

# Check for errors in logs
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && tail -50 storage/logs/laravel.log | grep -i error"
```

### Weekly Maintenance Tasks
```bash
# Create weekly backup
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && mysqldump -h 127.0.0.1 -u appbookme-user49 -p'wcj6PZu59ongVyIq21Qe#@38' appbookme-database49 > /tmp/weekly_backup_$(date +%Y%m%d).sql"

# Clear old log files (if needed)
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && find storage/logs/ -name '*.log' -mtime +30 -delete"

# Optimize database (with caution)
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan optimize"
```

## 🔧 Production Troubleshooting

### Common Production Issues

#### Application Down/500 Errors
```bash
# Check Laravel logs
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && tail -100 storage/logs/laravel.log"

# Check file permissions
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && ls -la storage/ bootstrap/cache/"

# Clear all caches
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan cache:clear && php artisan config:clear && php artisan view:clear"
```

#### Database Connection Issues
```bash
# Test database connection
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan migrate:status"

# Check database server status
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "mysql -h 127.0.0.1 -u appbookme-user49 -p'wcj6PZu59ongVyIq21Qe#@38' -e 'SELECT 1'"
```

#### High Server Load
```bash
# Check server resources
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "top -n 1 | head -20"

# Check disk usage
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "df -h && du -sh /home/<USER>/htdocs/app.appbookme.com/public/*"
```

## 📋 Production Security Best Practices

### Security Monitoring
```bash
# Check for unauthorized access attempts
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "grep -i 'failed\|invalid' /var/log/auth.log | tail -10" 2>/dev/null || echo "Auth log not accessible"

# Monitor file changes
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && find . -name '*.php' -mtime -1 | head -10"
```

### Security Configuration
- **Environment**: Always `production` (never `local` or `development`)
- **Debug Mode**: Always `false` in production
- **HTTPS**: Always use SSL/TLS encryption
- **Database**: Use strong passwords and limited privileges
- **File Permissions**: Restrict access to sensitive files

## 🎯 Production Performance Optimization

### Laravel Production Optimizations
```bash
# Run all production optimizations
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "cd /home/<USER>/htdocs/app.appbookme.com/public && php artisan config:cache && php artisan route:cache && php artisan view:cache && composer install --no-dev --optimize-autoloader"
```

### Database Optimization
```bash
# Analyze database performance (with caution)
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@************* "mysql -h 127.0.0.1 -u appbookme-user49 -p'wcj6PZu59ongVyIq21Qe#@38' -e 'SHOW PROCESSLIST; SHOW STATUS LIKE \"Slow_queries\";'"
```

## 📞 Production Support & Escalation

### Emergency Contacts
- **Technical Issues**: Refer to local development team
- **Server Issues**: Contact hosting provider
- **Database Issues**: Create backup first, then investigate

### Escalation Procedures
1. **Level 1**: Clear caches, restart services
2. **Level 2**: Check logs, investigate errors
3. **Level 3**: Restore from backup if necessary
4. **Level 4**: Contact technical support

### Documentation Requirements
- **Change Log**: Document all production changes
- **Incident Reports**: Record all production issues
- **Backup Verification**: Confirm backup integrity regularly

---

**Last Updated**: January 2025
**Environment**: Production
**Status**: ✅ Live Production Environment - Handle with Extreme Care
