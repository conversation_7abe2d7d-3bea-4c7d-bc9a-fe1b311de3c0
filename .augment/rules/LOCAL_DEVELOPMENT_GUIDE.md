---
type: "manual"
---

# BookMe Local Development Guide

## 🎯 Overview

This document provides comprehensive guidelines for local development of the BookMe Laravel application. The local environment is a complete replica of the production environment, allowing for safe development and testing.

## 🔧 Local Environment Setup

### Environment Details
- **Local URL**: `http://127.0.0.1:8000`
- **Environment**: `local` (development mode)
- **Database**: `bookme_local` (MySQL)
- **Laravel Version**: 9.52.20
- **PHP Version**: 8.3.22
- **Composer Version**: 2.8.9

### Directory Structure
```
/Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp/
├── app/                    # Application logic
│   ├── Console/           # Artisan commands
│   ├── Exceptions/        # Exception handlers
│   ├── Helpers/           # Helper classes
│   ├── Http/              # Controllers, middleware
│   │   ├── Controllers/   # Application controllers
│   │   │   └── Api/      # API controllers
│   │   └── Middleware/   # HTTP middleware
│   ├── Mail/              # Mail classes
│   ├── Models/            # Eloquent models
│   ├── Providers/         # Service providers
│   ├── Services/          # Business logic services
│   └── Traits/            # Reusable traits
├── bootstrap/             # Application bootstrap
├── config/                # Configuration files
├── database/              # Migrations, seeders, factories
│   ├── factories/         # Model factories
│   ├── migrations/        # Database migrations
│   └── seeders/           # Database seeders
├── lang/                  # Language files
├── public/                # Public assets
├── resources/             # Views, assets, language files
│   ├── css/              # CSS source files
│   ├── js/               # JavaScript source files
│   ├── lang/             # Localization files
│   └── views/            # Blade templates
├── routes/                # Route definitions
├── storage/               # File storage, logs, cache
│   ├── app/              # Application files
│   ├── firebase/         # Firebase configuration
│   ├── framework/        # Framework cache/sessions
│   └── logs/             # Application logs
├── tests/                 # Application tests
├── vendor/                # Composer dependencies
├── .env                   # Local environment configuration
├── artisan               # Laravel command-line interface
├── composer.json         # Composer dependencies
└── package.json          # NPM dependencies
```

## 🚀 Starting the Development Server

### Start Laravel Server
```bash
cd /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp
php artisan serve --host=127.0.0.1 --port=8000
```

### Access Points
- **Frontend**: http://127.0.0.1:8000
- **Admin Panel**: http://127.0.0.1:8000/admin
- **API Endpoints**: http://127.0.0.1:8000/api/*

## 🔒 Production Isolation Verification

### ✅ Confirmed Isolation Features
1. **Separate Database**: `bookme_local` (not connected to production)
2. **Local Environment**: `APP_ENV=local` (debug mode enabled)
3. **Local URL**: `http://127.0.0.1:8000` (no external access)
4. **Unique App Key**: Generated specifically for local environment
5. **Local File Storage**: All uploads/files stored locally

### ✅ Safety Guarantees
- ❌ **Cannot access production database** from local environment
- ❌ **Cannot accidentally deploy to production** without explicit action
- ❌ **No shared credentials** between local and production
- ✅ **Complete data isolation** - local changes stay local
- ✅ **Independent configuration** - separate .env files

## 🛠️ Development Workflow

### 1. Making Changes
```bash
# Navigate to project directory
cd /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp

# Make your code changes using your preferred editor
# Test changes locally at http://127.0.0.1:8000
```

### 2. Database Operations
```bash
# Run migrations
php artisan migrate

# Seed database (if needed)
php artisan db:seed

# Create new migration
php artisan make:migration create_new_table

# Rollback migrations (local only)
php artisan migrate:rollback
```

### 3. Cache Management
```bash
# Clear application cache
php artisan cache:clear

# Clear configuration cache
php artisan config:clear

# Clear route cache
php artisan route:clear

# Clear view cache
php artisan view:clear
```

### 4. Testing
```bash
# Run PHPUnit tests
php artisan test

# Run specific test
php artisan test --filter=TestName

# Generate test coverage
php artisan test --coverage
```

## 📊 Database Information

### Local Database Details
- **Database Name**: `bookme_local`
- **Host**: `127.0.0.1`
- **Port**: `3306`
- **Username**: `root`
- **Password**: (empty)

### Current Data (Imported from Production)
- **Users**: Complete user data imported from production
- **Vendors**: Service providers and their profiles
- **Services**: Available services and categories
- **Bookings**: Order and booking history
- **Tables**: 50+ tables total (complete schema)

### Key Tables
- `users` - Customer accounts
- `vendor` - Service provider accounts
- `services` - Available services
- `orders` - Booking orders
- `vendor_services` - Services offered by vendors
- `admin` - Admin user accounts
- `servicecategory` - Service categories

### Database Commands
```bash
# Access MySQL directly
mysql -u root bookme_local

# Export local database
mysqldump -u root bookme_local > local_backup.sql

# Import database
mysql -u root bookme_local < backup.sql

# Import production backup
mysql -u root bookme_local < appbookme_production_backup_20250723_164026.sql
```

## 🔄 Development & Deployment Workflow

### Git-Based Version Control 📚

#### Setting Up Git Repository
```bash
# Initialize Git repository (if not already done)
cd /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp
git init

# Add all files to version control
git add .
git commit -m "Initial commit: BookMe local development environment setup"
```

#### Essential Git Commands for Development
```bash
# Check status of changes
git status

# Add specific files
git add [filename]

# Add all changes
git add .

# Commit changes with descriptive message
git commit -m "Feature: Add new functionality"

# View commit history
git log --oneline

# Create and switch to new branch
git checkout -b feature/new-feature

# Switch between branches
git checkout main
git checkout feature/new-feature
```

#### Maintaining .gitignore
Ensure your `.gitignore` file includes:
```bash
# Environment files
.env
.env.*

# Dependencies
/node_modules
/vendor

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Laravel specific
/storage/logs/*
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*
/bootstrap/cache/*

# Build files
/public/hot
/public/storage
npm-debug.log*
```

### GitHub Integration 🌐

#### Setting Up GitHub Repository
```bash
# Add GitHub remote (replace with your repository URL)
git remote add origin https://github.com/yourusername/bookme.git

# Push to GitHub for the first time
git push -u origin main

# Regular pushes
git push origin main
```

#### GitHub Workflow for Development
```bash
# Create feature branch
git checkout -b feature/product-improvements

# Make changes and commit
git add .
git commit -m "Improve product display functionality"

# Push feature branch to GitHub
git push origin feature/product-improvements

# After testing, merge to main
git checkout main
git merge feature/product-improvements
git push origin main
```

#### Benefits of GitHub Integration
- ✅ **Remote Backup**: Code safely stored in the cloud
- ✅ **Collaboration**: Team members can contribute
- ✅ **Version History**: Complete change tracking
- ✅ **Branch Management**: Parallel development streams
- ✅ **CI/CD Ready**: Integration with automated deployment

### Safe Deployment Process

#### Recommended Three-Step Deployment Strategy
1. **Git for Versioning**: Track all file changes using Git
2. **GitHub for Collaboration**: Push commits for backup and team access
3. **rsync for Deployment**: Deploy only changed files to production

#### Step-by-Step Deployment Workflow
```bash
# 1. Develop and test locally
# Make your changes and test at http://127.0.0.1:8000

# 2. Version control with Git
git add .
git commit -m "Fix: Resolve product display issue"

# 3. Push to GitHub (optional but recommended)
git push origin main

# 4. Deploy to production using lftp
lftp -e "set sftp:connect-program 'ssh -a -x -i ~/.ssh/cloudpanel_key -p 22'; cd /home/<USER>/htdocs/app.appbookme.com/public; mirror -R --parallel=4 --use-pget-n=8 --verbose --continue --only-newer --exclude=vendor --exclude=node_modules --exclude=storage/logs /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp .; quit" -u developer-chrisans sftp://*************

# 5. Verify deployment
curl -I https://app.appbookme.com/public
```

### rsync Deployment Strategy 🚀

#### Recommended lftp Command
```bash
# Test deployment (dry run - safe to test)
lftp -e "set sftp:connect-program 'ssh -a -x -i ~/.ssh/cloudpanel_key -p 22'; cd /home/<USER>/htdocs/app.appbookme.com/public; mirror -R --dry-run --parallel=4 --use-pget-n=8 --verbose --continue --only-newer --exclude=vendor --exclude=node_modules --exclude=storage/logs --exclude=.git --exclude=.env /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp .; quit" -u developer-chrisans sftp://*************

# Actual deployment (after testing with --dry-run)
lftp -e "set sftp:connect-program 'ssh -a -x -i ~/.ssh/cloudpanel_key -p 22'; cd /home/<USER>/htdocs/app.appbookme.com/public; mirror -R --parallel=4 --use-pget-n=8 --verbose --continue --only-newer --exclude=vendor --exclude=node_modules --exclude=storage/logs --exclude=.git --exclude=.env /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp .; quit" -u developer-chrisans sftp://*************
```

#### Benefits of rsync Deployment
- ✅ **Minimal Transfer**: Only changed files are uploaded
- ✅ **Fast Deployment**: Efficient file synchronization
- ✅ **Reliable**: Built-in error checking and retry mechanisms
- ✅ **Selective Sync**: Exclude sensitive files automatically
- ✅ **Bandwidth Efficient**: Reduces deployment time

#### rsync Safety Features
```bash
# Always test first with --dry-run
rsync --dry-run [options] [source] [destination]

# Use --progress to monitor transfer
rsync --progress [options] [source] [destination]

# Use --verbose for detailed output
rsync -v [options] [source] [destination]
```

### Alternative Deployment Methods (Reference)

#### Single File Upload
```bash
# Upload specific file to production
scp -i ~/.ssh/cloudpanel_key -P 22 -o StrictHostKeyChecking=no [LOCAL_FILE] developer-chrisans@*************:/home/<USER>/htdocs/app.appbookme.com/public/[REMOTE_PATH]
```

#### Manual SSH Commands
```bash
# Connect to production server
ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@*************

# Navigate to application directory
cd /home/<USER>/htdocs/app.appbookme.com/public

# Run Laravel commands on production (use with caution)
php artisan cache:clear
```

## 🔒 Environment File Management

### Why `.env` Should Not Be Synced

Environment configuration files like `.env` contain sensitive and environment-specific data (e.g., DB credentials, API keys). These should **not be committed to Git** or transferred using `rsync`, especially from local to production environments.

### Best Practices

1. **Ignore .env in Git**
   Ensure `.env` is listed in `.gitignore`:
   ```bash
   # .gitignore
   .env
   .env.*
   ```

2. **Maintain Separate Files**
   - Local: `./.env`
   - Production: `/home/<USER>/htdocs/app.appbookme.com/public/.env`

   These should be manually configured and kept separate.

3. **Exclude .env from lftp**
   When deploying via lftp, exclude `.env`:
   ```bash
   lftp -e "set sftp:connect-program 'ssh -a -x -i ~/.ssh/cloudpanel_key -p 22'; cd /home/<USER>/htdocs/app.appbookme.com/public; mirror -R --exclude=.env --exclude=.git --exclude=node_modules /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp .; quit" -u developer-chrisans sftp://*************
   ```

4. **Manage Production .env Securely**
   Edit `.env` directly via SSH:
   ```bash
   ssh -i ~/.ssh/cloudpanel_key -p 22 -o StrictHostKeyChecking=no developer-chrisans@*************
   nano /home/<USER>/htdocs/app.appbookme.com/public/.env
   ```

   Or upload cautiously using SCP:
   ```bash
   scp -i ~/.ssh/cloudpanel_key -P 22 -o StrictHostKeyChecking=no .env.production developer-chrisans@*************:/home/<USER>/htdocs/app.appbookme.com/public/.env
   ```

5. **Use Example Files for Reference**
   Keep a `.env.production.example` in the repo to document required keys (but no secrets).

### Summary
- 🔒 `.env` files must be protected from versioning and unintended transfer
- 🔁 Keep `.env` files manually managed per environment
- ✅ Use `.env.example` to guide configuration without exposing secrets

## ⚠️ Important Warnings

### DO NOT:
- ❌ **Never commit .env files** to version control
- ❌ **Never use production credentials** in local environment
- ❌ **Never test payment gateways** with real credentials locally
- ❌ **Never deploy untested changes** to production
- ❌ **Never modify production database** directly

### DO:
- ✅ **Always test changes locally first**
- ✅ **Use separate .env files** for different environments
- ✅ **Backup production before deployment**
- ✅ **Use version control** for code changes
- ✅ **Document significant changes**

## 🐛 Troubleshooting

### Common Issues and Solutions

#### Laravel Server Won't Start
```bash
# Check if port is in use
lsof -i :8000

# Use different port
php artisan serve --port=8001
```

#### Database Connection Issues
```bash
# Verify MySQL is running
brew services list | grep mysql

# Start MySQL if needed
brew services start mysql

# Test database connection
mysql -u root -e "SELECT 1"
```

#### Permission Issues
```bash
# Fix Laravel permissions
chmod -R 755 storage bootstrap/cache
```

#### Cache Issues
```bash
# Clear all caches
php artisan optimize:clear
```

## 📝 Best Practices

### Code Organization
- Follow Laravel conventions and PSR standards
- Use meaningful variable and function names
- Comment complex logic
- Keep controllers thin, models fat
- Use service classes for business logic

### Database Management
- Always use migrations for schema changes
- Use seeders for test data
- Never edit migration files after they're committed
- Use database transactions for complex operations

### Security
- Validate all user inputs
- Use Laravel's built-in security features
- Keep dependencies updated
- Never expose sensitive information in logs

### Performance
- Use Laravel's caching mechanisms
- Optimize database queries
- Use eager loading to prevent N+1 queries
- Monitor application performance

## � Asset Synchronization Guide

### Production Asset Synchronization

#### Asset Inventory Overview
The BookMe application contains the following production assets:
- **Images**: 87 files (3.3M) in `public/images/`
- **User Uploads**: 172+ files (50M) in `public/uploads/`
- **CSS Files**: 2 files (1.1M) in `public/css/`
- **JavaScript**: 29 files (348K) in `public/js/`
- **Icons**: Icon library (9.6M) in `public/icons/`
- **Vendor Assets**: Third-party libraries (19M) in `public/vendor/`
- **VendorPanel**: Admin panel assets (21M) in `public/vendorpanel/`
- **StaffPanel**: Staff panel assets (21M) in `public/staffpanel/`

#### Synchronization Commands

##### Download All Missing Assets
```bash
# Navigate to project directory
cd /Users/<USER>/Desktop/BookMeProject/WebApp/LaravelApp

# Download vendor assets
mkdir -p public/vendor
scp -i ~/.ssh/cloudpanel_key -P 22 -r developer-chrisans@*************:/home/<USER>/htdocs/app.appbookme.com/public/public/vendor/* public/vendor/

# Download vendorpanel assets
mkdir -p public/vendorpanel
scp -i ~/.ssh/cloudpanel_key -P 22 -r developer-chrisans@*************:/home/<USER>/htdocs/app.appbookme.com/public/public/vendorpanel/* public/vendorpanel/

# Download latest user uploads (if needed)
scp -i ~/.ssh/cloudpanel_key -P 22 -r developer-chrisans@*************:/home/<USER>/htdocs/app.appbookme.com/public/public/uploads/* public/uploads/
```

##### Configure Storage Symlinks
```bash
# Create Laravel storage symlink for local development
php artisan storage:link

# Verify symlink creation
ls -la public/storage
```

#### Asset Verification

##### Check Asset Integrity
```bash
# Verify image counts
echo "Images:" && find public/images -type f | wc -l
echo "Uploads:" && find public/uploads -type f | wc -l
echo "CSS:" && find public/css -type f | wc -l
echo "JS:" && find public/js -type f | wc -l

# Check directory sizes
du -sh public/images public/uploads public/css public/js public/icons
```

##### Test Asset Access
```bash
# Start local development server
php artisan serve

# Test asset URLs in browser:
# http://127.0.0.1:8000/images/logo.png
# http://127.0.0.1:8000/uploads/[filename]
# http://127.0.0.1:8000/css/style.css
```

#### Asset Exclusions

The following production assets are **excluded** from local synchronization:
- **Log Files**: `error_log`, `laravel.log` (security sensitive)
- **Cache Files**: `bootstrap/cache/*`, `storage/framework/cache/*`
- **Environment Files**: `.env` (environment specific)
- **Temporary Files**: `storage/logs/*`, `storage/app/tmp/*`

#### Maintenance Schedule

##### Weekly Asset Sync (Recommended)
```bash
# Sync only new user uploads
rsync -avz --update-only developer-chrisans@*************:/home/<USER>/htdocs/app.appbookme.com/public/public/uploads/ public/uploads/ -e "ssh -i ~/.ssh/cloudpanel_key -p 22"
```

##### Monthly Full Sync
```bash
# Complete asset synchronization (use with caution)
# This will download all assets again - only use if major changes detected
# Run the asset sync commands above
```

### Asset Development Guidelines

#### Local Asset Management
1. **Never modify production assets directly**
2. **Use local copies for development testing**
3. **Test asset paths in both local and production environments**
4. **Maintain asset version control for custom assets**

#### Asset Optimization
```bash
# Optimize images for development (optional)
# Install imagemagick first: brew install imagemagick
find public/images -name "*.jpg" -exec convert {} -quality 85 {} \;
find public/images -name "*.png" -exec convert {} -quality 85 {} \;
```

## �📞 Support

For technical issues or questions:
1. Check Laravel documentation: https://laravel.com/docs
2. Review application logs: `storage/logs/laravel.log`
3. Use Laravel debugging tools: `dd()`, `dump()`, etc.
4. Refer to SSH deployment guide: `.augment/rules/ssh_access.md`

---

**Last Updated**: January 2025
**Environment**: Local Development
**Status**: ✅ Fully Functional and Production-Isolated with Complete Asset Parity
