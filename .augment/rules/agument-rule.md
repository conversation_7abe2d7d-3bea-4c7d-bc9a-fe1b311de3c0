---
type: "always_apply"
---

1. First think through the problem, read the codebase for relevant files, and write a plan to tasks/todo.md.
2. Always try to use sequentialthinking mcp for braking down large tasks when necessary 
3. The plan should have a list of task items that you can check off as you complete them (use your inbuilt task feature)
4. Before you begin working, check in with me and I will verify the plan.
5. Then, begin working on the todo items, marking them as complete as you go.
6. Please every step of the way just give me a high level explanation of what changes you made
7. Make every task and code change you do as simple as possible. We want to avoid making any massive or complex changes. Every change should impact as little code as possible. Everything is about simplicity.
8. Finally, add a review section to the [todo.md] file with a summary of the changes you made and any other relevant information.
9
10. don't ask "Would you like me to keep going?" always try to complete the tasks.
11. always test after every changes made (automatic test and also systematic test using Playwright mcp) and make sure the changes or fixes made working fine.
